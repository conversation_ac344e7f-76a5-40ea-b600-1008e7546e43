<template>
  <div class="app-container">
    <div class="bg-[#ffffff] p-[10px] border-rd-10px mb-[10px]">
      <el-form ref="queryRef" :model="query" :inline="true" label-width="90px">
        <el-form-item label="名称：" prop="name">
          <el-input
            v-model="query.name"
            maxlength="20"
            placeholder="请输入名称"
            style="width: 140px"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleQuery">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button @click="handleAdd">添加</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table ref="table" v-loading="loading" :data="data" :height="600">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="名称" min-width="150" show-overflow-tooltip />
      <el-table-column label="图片" min-width="100">
        <template #default="{ row }">
          <el-image
            style="width: 50px; height: 50px"
            :src="row.image_url?.[0]?.file_url"
            :preview-src-list="row.image_url?.[0]?.file_url ? [row.image_url[0].file_url] : []"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column label="跳转类型" min-width="150">
        <template #default="{ row }">
          <el-tag>{{ getJumpTypeText(row.jump_type) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="跳转链接" min-width="150" show-overflow-tooltip>
        <template #default="{ row }">
          <template v-if="row.jump_type === 1 || row.jump_type === 2">
            {{ row.jump_url }}
          </template>
          <template v-else>
            <div>视频号ID：{{ row.video_num_id }}</div>
            <div>视频ID：{{ row.video_id }}</div>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="sort" label="顺序" width="80" />
      <el-table-column label="状态" width="80">
        <template #default="{ row }">
          <el-tag :type="row.status ? 'success' : 'info'">
            {{ row.status ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="create_at" label="创建时间" min-width="150" />
      <el-table-column prop="update_at" label="更新时间" min-width="150" />

      <el-table-column label="操作" width="100">
        <template #default="scope">
          <el-link type="success" class="mr-1" :underline="false" @click="handleEdit(scope.row)">
            编辑
          </el-link>
          <el-popconfirm title="确认删除该条数据吗？" @confirm="handleDelete(scope.row)">
            <template #reference>
              <el-link class="mr-1" type="success" :underline="false">删除</el-link>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="count > 0"
      v-model:total="count"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="getList"
    />

    <el-dialog
      v-model="dialogshow"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="dialogcancel"
      width="500px"
      top="5vh"
    >
      <el-form ref="formRef" :model="model" :rules="rules" label-width="120px">
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="model.name"
            placeholder="请输入名称"
            maxlength="20"
            show-word-limit
            clearable
          />
          <div class="el-upload__tip">* 非必填，最多20个字</div>
        </el-form-item>
        <el-form-item label="是否添加水印" prop="is_watermark">
          <el-radio-group v-model="model.is_watermark" style="margin-left: 10px">
            <el-radio label="否" :value="0"></el-radio>
            <el-radio label="是" :value="1"></el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="图片" prop="image_url">
          <ImgUploadsHouseType
            source="0"
            :isWatermark="model.is_watermark"
            v-model="model.image_url"
            :upload-btn="model.image_url.length ? '' : '上传图片'"
            file-type="image"
            :height="100"
          />
        </el-form-item>

        <el-form-item label="跳转类型" prop="jump_type">
          <el-radio-group v-model="model.jump_type">
            <el-radio :label="1">小程序</el-radio>
            <el-radio :label="2">H5</el-radio>
            <el-radio :label="3">内嵌小程序视频</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          label="跳转链接"
          prop="jump_url"
          v-if="model.jump_type === 1 || model.jump_type === 2"
        >
          <el-input
            v-model="model.jump_url"
            :placeholder="model.jump_type === 1 ? '请输入小程序链接' : '请输入H5链接'"
            clearable
          />
        </el-form-item>

        <template v-if="model.jump_type === 3">
          <el-form-item label="视频号ID" prop="video_num_id">
            <el-input v-model="model.video_num_id" placeholder="请输入视频号ID" clearable />
          </el-form-item>
          <el-form-item label="是否同主体" prop="is_same_subject">
            <el-radio-group v-model="model.is_same_subject">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="视频ID" prop="video_id">
            <el-input
              v-model="model.video_id"
              placeholder="视频id或feed-token（非同主体）"
              clearable
            />
          </el-form-item>
        </template>

        <el-form-item label="顺序" prop="sort">
          <el-input-number v-model="model.sort" :min="1" :max="100" />
          <div class="el-upload__tip">* 1-100的整数</div>
        </el-form-item>

        <el-form-item label="是否启用" prop="status">
          <el-radio-group v-model="model.status">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogcancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submit">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import ImgUploadsHouseType from '@/components/FileManage/ImgUploadsHouseType.vue'
import { getPageLimit } from '@/utils/settings'
import Pagination from '@/components/Pagination/index.vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

const loading = ref(false)
const dialogshow = ref(false)
const dialogTitle = ref('添加')
const data = ref([])
const formRef = ref(null)

// 表单校验规则
const rules = {
  name: [{ max: 20, message: '最多20个字符', trigger: 'blur' }],
  image_url: [{ required: true, message: '请上传图片', trigger: 'change' }],
  jump_type: [{ required: true, message: '请选择跳转类型', trigger: 'change' }],
  jump_url: [
    {
      required: true,
      message: '请输入跳转链接',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        const { jump_type } = model.value
        if ((jump_type === 1 || jump_type === 2) && !value) {
          callback(new Error('请输入跳转链接'))
        } else {
          callback()
        }
      }
    }
  ],
  video_num_id: [
    {
      required: true,
      message: '请输入视频号ID',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (model.value.jump_type === 3 && !value) {
          callback(new Error('请输入视频号ID'))
        } else {
          callback()
        }
      }
    }
  ],
  video_id: [
    {
      required: true,
      message: '请输入视频ID',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (model.value.jump_type === 3 && !value) {
          callback(new Error('请输入视频ID'))
        } else {
          callback()
        }
      }
    }
  ],
  sort: [{ required: true, message: '请输入顺序', trigger: 'blur' }],
  status: [{ required: true, message: '请选择是否启用', trigger: 'change' }]
}

// 重置的默认值
const resetmodel = ref({
  name: '',
  image_url: [],
  jump_type: 1,
  jump_url: '',
  video_num_id: '',
  video_id: '',
  sort: 1,
  status: 1,
  is_same_subject: 1,
  is_watermark: 0
})

// 表单数据
const model = ref({
  name: '',
  image_url: [],
  jump_type: 1,
  jump_url: '',
  video_num_id: '',
  video_id: '',
  sort: 1,
  status: 1,
  is_same_subject: 1
})

const query = ref({
  page: 1,
  limit: getPageLimit(),
  name: ''
})

const count = ref(0)
const queryRef = ref(null)

// 获取跳转类型文本
const getJumpTypeText = (jumpType) => {
  switch (jumpType) {
    case 1:
      return '小程序'
    case 2:
      return 'H5'
    case 3:
      return '内嵌小程序视频'
    default:
      return '--'
  }
}

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const res = await request({
      url: '/admin/advertise.ElementConfig/list',
      method: 'get',
      params: query.value
    })
    if (res.code === 200) {
      res.data.list = res.data.list.map((item) => ({
        ...item,
        image_url: item.image_url ? [{ file_url: item.image_url }] : []
      }))
      data.value = res.data.list
      count.value = res.data.count
    } else {
      ElMessage.error(res.message || '获取列表失败')
    }
  } catch (error) {
    console.error('获取列表失败:', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

// 删除
const handleDelete = async (row) => {
  try {
    const res = await request({
      url: '/admin/advertise.ElementConfig/del',
      method: 'post',
      data: { id: row.id }
    })
    if (res.code === 200) {
      ElMessage.success('删除成功')
      getList()
    } else {
      ElMessage.error(res.message || '删除失败')
    }
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  }
}

// 关闭弹窗
const dialogcancel = () => {
  dialogshow.value = false
  formRef.value?.resetFields()
}

// 重置查询
const resetQuery = () => {
  queryRef.value.resetFields()
  handleQuery()
}

// 查询
const handleQuery = () => {
  query.value.page = 1
  getList()
}

// 添加
const handleAdd = () => {
  dialogTitle.value = '添加'
  model.value = JSON.parse(JSON.stringify(resetmodel.value))
  dialogshow.value = true
}

// 编辑
const handleEdit = async (row) => {
  dialogTitle.value = '编辑'
  try {
    const res = await request({
      url: `/admin/advertise.ElementConfig/info?id=${row.id}`,
      method: 'get'
    })
    if (res.code === 200) {
      res.data.image_url = res.data.image_url ? [{ file_url: res.data.image_url }] : []
      model.value = res.data
      model.value.is_watermark = 0
      dialogshow.value = true
    } else {
      ElMessage.error(res.msg || '获取详情失败')
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  }
}

// 提交
const submit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const submitData = {
          ...model.value,
          image_url: model.value.image_url?.[0]?.file_url || ''
        }
        console.log('111', submitData.jump_type)
        if (submitData.jump_type === 1 || submitData.jump_type === 2) {
          submitData.video_num_id = ''
          submitData.video_id = ''
        } else {
          submitData.jump_url = ''
        }

        const res = await request({
          url: '/admin/advertise.ElementConfig/save',
          method: 'post',
          data: submitData
        })
        if (res.code === 200) {
          ElMessage.success('保存成功')
          dialogshow.value = false
          getList()
        } else {
          ElMessage.error(res.message || '保存失败')
        }
      } catch (error) {
        console.error('保存失败:', error)
        ElMessage.error('保存失败')
      } finally {
        loading.value = false
      }
    }
  })
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.el-dialog {
  .el-form {
    padding: 0 20px;
  }
}

.el-upload__tip {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}
</style>
