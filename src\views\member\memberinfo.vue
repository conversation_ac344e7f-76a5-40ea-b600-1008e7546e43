<template>
  <div class="p-[20px]">
    <el-descriptions title="基础信息" direction="horizontal" :column="1">
      <el-descriptions-item label="用户ID：">{{ userInfo.member_id }}</el-descriptions-item>
      <el-descriptions-item label="openid：">{{
        userInfo.wxopenid || '暂无'
      }}</el-descriptions-item>
      <el-descriptions-item label="unionid：">{{
        userInfo.wxunionid || '暂无'
      }}</el-descriptions-item>
      <el-descriptions-item label="用户昵称：">{{
        userInfo.nickname || '暂无'
      }}</el-descriptions-item>
      <el-descriptions-item label="手机号：">
        {{ currentPhone || '暂无' }}
        <template v-if="userInfo.phone">
          <span class="ml-[10px] cursor-[pointer]">
            <svg-icon icon-class="view" v-if="!isHidePhone" @click="handViewPhone" />
          </span>
        </template>
      </el-descriptions-item>
      <el-descriptions-item label="关注楼盘： ">{{
        userInfo.followHouse || '暂无'
      }}</el-descriptions-item>
      <!-- 历史 冻结理由 -->

      <template v-for="(item, index) in userInfo.memberScoreDetailCheat" :key="index">
        <el-descriptions-item label="历史身份： ">置业顾问</el-descriptions-item>
        <el-descriptions-item label="禁用理由： ">{{
          item.freeze_reason || '暂无'
        }}</el-descriptions-item>
        <el-descriptions-item label="惩处积分： ">{{ item.number }}</el-descriptions-item>
        <el-descriptions-item label="冻结时间： ">{{ item.create_time }}</el-descriptions-item>
      </template>
    </el-descriptions>
    <!-- 置业顾问审核 -->
    <div v-if="userInfo.identity_type == 2">
      <el-form-item label="身份：">
        <el-text>置业顾问</el-text>
      </el-form-item>

      <el-form-item label="姓名">
        <el-input v-model="userIdentityInfo.service_name" :disabled="true" />
      </el-form-item>
      <el-form-item label="手机号">
        <el-input v-model="userIdentityInfo.service_phone" :disabled="true" />
      </el-form-item>
      <el-form-item label="微信号">
        <el-input v-model="userIdentityInfo.wechat" :disabled="true" />
      </el-form-item>

      <el-form-item label="个人介绍">
        <el-input v-model="userIdentityInfo.person_mark" type="textarea" :disabled="true" />
      </el-form-item>
      <el-form-item label="所属公司">
        <el-input v-model="userIdentityInfo.belong_company" :disabled="true" />
      </el-form-item>
      <el-form-item label="关联楼盘">
        <el-select
          v-model="userIdentityInfo.salehouselist"
          clearable
          placeholder="选择关联楼盘"
          style="width: 240px"
          multiple
          :multiple-limit="1"
          :disabled="true"
        >
          <el-option
            v-for="item in houseListArr"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="头像" prop="platedetailimg_id">
        <ImgUpload
          source="0"
          :isWatermark="userIdentityInfo.is_watermark"
          v-model="userIdentityInfo.platedetailimg_id"
          v-model:file-url="userIdentityInfo.service_avatar"
          file-type="image"
          disabled
          :height="100"
        />
      </el-form-item>
      <el-form-item label="证明材料">
        <ImgUpload
          v-model="userIdentityInfo.platedetailimg_id"
          v-model:file-url="userIdentityInfo.evidence_img"
          file-type="image"
          disabled
          :height="100"
        />
      </el-form-item>
      <el-form-item label="身份证正面">
        <ImgUpload
          v-model="userIdentityInfo.platedetailimg_id"
          v-model:file-url="userIdentityInfo.id_card_face"
          file-type="image"
          disabled
          :height="100"
        />
      </el-form-item>
      <el-form-item label="身份证反面">
        <ImgUpload
          v-model="userIdentityInfo.platedetailimg_id"
          v-model:file-url="userIdentityInfo.id_card_emblem"
          file-type="image"
          disabled
          :height="100"
        />
      </el-form-item>

      <div v-if="userIdentityInfo.review_state == 1">
        <div>审核信息</div>

        <el-form :model="auditItem" label-width="auto" style="max-width: 600px">
          <el-form-item label="审核结果">
            <el-radio-group v-model="auditItem.review_state">
              <el-radio :value="2">通过</el-radio>
              <el-radio :value="3">不通过</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="审核理由">
            <el-input v-model="auditItem.review_mark" type="textarea" />
          </el-form-item>

          <el-form-item label="楼盘详情页展示">
            <el-radio-group v-model="auditItem.is_show">
              <el-radio :value="1">显示</el-radio>
              <el-radio :value="2">不显示</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="审核人">
            <el-text>{{ user.nickname }}</el-text>
          </el-form-item>

          <el-form-item>
            <el-button :loading="loading" type="primary" @click="submitreview"
              >提交审核结果</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <div v-else-if="userIdentityInfo.review_state == 2">
        <div>取消置业顾问身份</div>

        <el-form :model="cancelIdentity" label-width="auto" style="max-width: 600px">
          <el-form-item label="取消理由">
            <el-input v-model="cancelIdentity.review_mark" type="textarea" />
          </el-form-item>
          <el-form-item label="楼盘详情页展示">
            <el-radio-group v-model="userIdentityInfo.is_show">
              <el-radio :value="1">显示</el-radio>
              <el-radio :value="2">不显示</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
            <el-button :loading="loading" type="primary" @click="submitcancel">取消身份</el-button>
            <el-button :loading="loading" type="primary" @click="submiteditzhiyeguwen"
              >编辑</el-button
            >
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 置业顾问/选房师表单 -->
    <div
      v-else-if="
        (userInfo.identity_type == 1 ||
          userInfo.identity_type == 3 ||
          userInfo.identity_type == 4) &&
        userInfo.phone != ''
      "
    >
      <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-change="handleChange">
        <el-tab-pane label="选房师" name="houseselector"> </el-tab-pane>
        <el-tab-pane label="主播" name="anchor"> </el-tab-pane>
      </el-tabs>
      <el-form :model="userIdentityInfo" label-width="auto" style="max-width: 600px">
        <el-form-item label="选房师身份" v-if="activeName === 'houseselector'">
          <el-radio-group v-model="isHouseSelector" disabled>
            <el-radio :value="1">是</el-radio>
            <el-radio :value="2">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="主播身份" v-if="activeName === 'anchor'">
          <el-radio-group v-model="isAchor" disabled>
            <el-radio :value="1">是</el-radio>
            <el-radio :value="2">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model="userIdentityInfo.service_name" disabled="true" />
        </el-form-item>

       
      </el-form>
    </div>

    <div class="mt-[20px] font-bold font-size-[16px] color-[#303133]">浏览记录</div>
    <el-table
      ref="browsehistoryList"
      v-loading="historyTableData.loading"
      :data="historyTableData.list"
      height="350"
    >
      <el-table-column
        prop="createtime"
        label="浏览时间"
        width="160"
        :formatter="customformat('YYYY-MM-DD HH:mm')"
      />
      <el-table-column prop="id" label="ID" width="80" />

      <el-table-column prop="viewtype" label="类型" min-width="60" show-overflow-tooltip />
      <el-table-column label="手机设备及系统" min-width="160" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.devicemodel">{{ row.devicemodel }}</span>
          <br v-if="row.devicemodel" />
          <span v-if="row.devicesystem">{{ row.devicesystem }}</span>
        </template>
      </el-table-column>

      <el-table-column
        prop="housename"
        label="楼盘"
        min-width="170"
        show-overflow-tooltip
        :formatter="defaultFormat()"
      />
      <el-table-column
        prop="relate_content"
        label="关联内容"
        min-width="112"
        :formatter="defaultFormat()"
        show-overflow-tooltip
      />
      <el-table-column
        prop="more_content"
        label="更多内容"
        :formatter="defaultFormat()"
        min-width="112"
        show-overflow-tooltip
      />
    </el-table>
    <pagination
      v-model:total="historyTableData.total"
      v-model:page="historyTableData.page"
      v-model:limit="historyTableData.limit"
      @pagination="getUserHistoryList"
    />
  </div>
</template>

<script setup>
import {
  list,
  disable,
  memberInfo,
  getMemberBrowseHistry,
  getMemberPhoneDtail
} from '@/api/member/member'
import { customformat } from '@/utils/dateUtil'
import { defaultFormat } from '@/utils'
import { onBeforeRouteLeave } from 'vue-router'
import { computed, ref, watch, reactive, onMounted } from 'vue'
import { getPageLimit } from '@/utils/settings'
import { regionList } from '@/api/setting/region'

import {
  getIsExistIdentity,
  memberIdentityEidt,
  identityreview,
  identitycancel,
  zhiyeguwenisshow,
  isCooperateIdentity
} from '@/api/member/memberidentity'
import { useUserStore } from '@/store/modules/user'

const userStore = useUserStore()
const { user } = storeToRefs(userStore)

const route = useRoute()
const routeQuery = computed(() => route.query)
const STATIC_COFIG = { page: 1, limit: getPageLimit(), loading: false, total: 0 }
const loading = ref(false)
const activeName = ref('')
const userInfo = ref({})
const userIdentityInfo = ref({})

const allPhone = ref('')

const isHouseSelector = ref(2)

const isAchor = ref(2)

const regionListArr = ref([])
const houseListArr = ref([])
const isHidePhone = ref(false)
const auditItem = ref({ review_state: 1, is_show: 1, review_mark: '' })
const cancelIdentity = ref({ review_state: 4, review_mark: '' })
const identityType = ref(null)
const historyTableData = ref({ ...STATIC_COFIG, list: [] })
const currentPhone = computed(() => (isHidePhone.value ? allPhone.value : userInfo.value?.phone))

const getRegionList = async () => {
  const { data } = await regionList({ region_pid: 1101 })
  regionListArr.value = data.list
}


const getIsExistIdentityInfo = async (func_identity_type=0) => {
  
  // let identityTypeValue =
  //   userInfo.value.identity_type == 1 ? identityType.value : userInfo.value.identity_type
  let identityTypeValue
  if(func_identity_type == 0){
    if(userInfo.value.identity_type == 1){
      identityTypeValue = identityType.value
    }else{
      identityTypeValue = userInfo.value.identity_type
    }
    
  }else{
    identityTypeValue = func_identity_type
  }
  const { data } = await getIsExistIdentity({
    member_id: currentId.value
    ,identity_type: identityTypeValue
  })
  userIdentityInfo.value = data
 
  
/*   if(activeName.value == 'houseselector'){
    userIdentityInfo.value = data['houseselector']
  }else if(activeName.value == 'anchor'){
    userIdentityInfo.value = data['anchor']
  }else if(activeName.value=='' && userInfo.value.identity_type == 2){
    userIdentityInfo.value = data['propertyconsultant']

  } */
  
 
  
  // if(Object.keys(data).length !== 0){
  //   identityType.value = data.identity_type
  // }
  
}

const getUserInfo = async () => {
  const { data } = await memberInfo({ member_id: currentId.value })
  userInfo.value = data
  if (userInfo.value.identity_type == 3) {
    isHouseSelector.value = 1
    activeName.value = 'houseselector'
  }
  if (userInfo.value.identity_type == 4) {
    isAchor.value = 1
    activeName.value = 'anchor'
  }
  if (userInfo.value.identity_type == 2) {
    
    activeName.value = ''
  }

  if (userInfo.value.identity_type == 1) {
    
    activeName.value = 'houseselector'
  }
  upIdentityType()
  await getIsExistIdentityInfo()
  
  // isHouseSelector.value = userInfo.value.identity_type == 3 ? 1 : 2
  // isAchor.value =  userInfo.value.identity_type == 4 ? 1 : 2
}

const simpleGetUserInfo = async () => {
  const { data } = await memberInfo({ member_id: currentId.value })
  userInfo.value = data
  
}

const getUserHistoryList = async () => {
  historyTableData.loading = true
  const params = {
    member_id: currentId.value,
    page: historyTableData.value.page,
    limit: historyTableData.value.limit
  }
  const { data } = await getMemberBrowseHistry(params)
  historyTableData.value.list = data.list
  historyTableData.value.total = data.count
  historyTableData.loading = false
}

const handViewPhone = async () => {
  if (allPhone.value) return
  const { data } = await getMemberPhoneDtail({ member_id: currentId.value })
  allPhone.value = data.phone
  isHidePhone.value = true
}


const submitreview = async () => {
  try {
    loading.value = true
    await ElMessageBox.confirm('请确认审核结果？', '审核结果', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await identityreview({ ...auditItem.value, id: userIdentityInfo.value.id })
    ElMessage.success('审核成功')
  } catch {
    ElMessage.info('审核取消')
  } finally{
    loading.value = false
  }
}

const submitcancel = async () => {
  try {
    const result = await ElMessageBox.confirm('请确认取消置业顾问身份吗？', '取消置业顾问身份', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 用户点击“确定”后执行
    if (result === 'confirm') {
      // 检查是否存在绑定的合作楼盘
      const res = await isCooperateIdentity({
        identity_id: userIdentityInfo.value.id
      })
      const hasBinding = res.data

      if (hasBinding) {
        ElMessage({
          type: 'info',
          message: '该置业顾问存在绑定的合作楼盘，请先解绑'
        })
        return
      }

      // 无绑定，继续取消身份
      const cancelRes = await identitycancel({
        ...cancelIdentity.value,
        id: userIdentityInfo.value.id
      })
      ElMessage({
        type: 'success',
        message: cancelRes.msg
      })
    }
  } catch (error) {
    // 处理用户取消或请求失败
    if (error === 'cancel') {
      ElMessage({
        type: 'info',
        message: '操作取消'
      })
    } else {
      ElMessage({
        type: 'error',
        message: '取消置业顾问身份失败'
      })
      console.error('取消身份失败:', error)
    }
  }
}

const submiteditzhiyeguwen = async () => {
  try {
    loading.value = true
    await ElMessageBox.confirm('确认提交？', '编辑', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await zhiyeguwenisshow({
      id: userIdentityInfo.value.id,
      is_show: userIdentityInfo.value.is_show
    })
    ElMessage.success('编辑成功')
  } catch {
    ElMessage.info('操作取消')
  } finally{
    loading.value = false
  }
}

const handleViewHidePhone = () => (isHidePhone.value = false)

const query = ref({ page: 1, limit: getPageLimit() })
const visible = ref(true)
const tableData = reactive({ total: 0, list: [], loading: false })
const currentId = ref(null)

const handleClose = () => {
  currentId.value = null
}

const upIdentityType = () => {
  if(activeName.value == 'houseselector'){
    identityType.value = 3 
  }else if(activeName.value == 'anchor'){
    identityType.value = 4
  }

  
}

const handleChange = async () => {
  upIdentityType()

  
  await getIsExistIdentityInfo(identityType.value)
  
}

onMounted(async () => {
  currentId.value = routeQuery.value.id

  await getUserInfo()

  await getUserHistoryList()
  await getRegionList()
 
})
</script>
<style lang="scss" scoped>
.page-contaniner {
  min-width: 1200px;
  .table-box {
    padding: 10px;
    margin: 0 20px;
  }
}
</style>
