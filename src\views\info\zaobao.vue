<template>
  <div class="app-container">
    <el-form ref="queryRef" :model="query" :inline="true" label-width="90px">
      <el-form-item label="分享标题：" prop="name">
        <el-input
          v-model="query.name"
          maxlength="20"
          placeholder="请输入标题"
          style="width: 140px"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" title="搜索" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>

        <el-button @click="handleAdd">创建早报</el-button>
      </el-form-item>
    </el-form>
    <!-- 搜索end -->

    <el-table ref="table" v-loading="loading" :data="data" :height="600">
      <el-table-column fixed prop="id" label="ID" width="80" />
      <el-table-column prop="title" label="分享标题" min-width="150" show-overflow-tooltip />
      <el-table-column prop="describe" label="分享描述" min-width="150" show-overflow-tooltip />
      <el-table-column prop="describe" label="分享链接" min-width="100">
        <!-- <el-text>m.ihouses{{}}</el-text> -->
        <template #default="{ row }">
          <el-link type="primary" @click="handleClip(row)">复制链接</el-link>
        </template>
      </el-table-column>
      <el-table-column label="生成分享图" min-width="100">
        <!-- <el-text>m.ihouses{{}}</el-text> -->
        <template #default="{ row }">
          <el-link type="primary" @click="handleCreateImg(row,0)" v-if="row.state === 1">
            生成
          </el-link>
          &nbsp;&nbsp;
          <el-link type="primary" @click="handleCreateImg(row,1)" v-if="row.state === 1">
            强制生成
          </el-link>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="updatetime"
        label="更新时间"
        min-width="150"
        :formatter="customformat('YYYY-MM-DD HH:mm:ss')"
        show-overflow-tooltip
      />
      <el-table-column prop="viewnum" label="浏览次数" min-width="100"  align="center"  />
      <el-table-column prop="publishtime" label="发布时间" min-width="100"  align="center"  />
      <el-table-column fixed="right" label="操作" width="125">
        <template #default="scope">
          <el-link type="primary" class="mr-1" :underline="false" @click="handleEdit(scope.row)">
            编辑
          </el-link>
          <el-link
            type="primary"
            v-if="scope.row.state === 1"
            class="mr-1"
            :underline="false"
            @click="handleSetState(scope.row)"
          >
            下架
          </el-link>
          <el-link
            type="primary"
            v-if="scope.row.state === 0"
            class="mr-1"
            :underline="false"
            @click="handleSetState(scope.row)"
          >
            上架
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="count > 0"
      v-model:total="count"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="getList"
    />
    <ArticleDialog
      v-model:visible="visible"
      @close="currentID = ''"
      @update="resetQuery"
      :id="currentID"
    />
    <el-image-viewer
      v-if="imgUrl.length"
      class="image-viewer"
      hide-on-click-modal
      :infinite="false"
      :initial-index="0"
      :url-list="imgUrl"
      @close="imgUrl = []"
    />
  </div>
</template>
<script setup>
import { getPageLimit } from '@/utils/settings'
import * as Zaobao from '@/api/info/zaobao'
import { customformat } from '@/utils/dateUtil'
import Pagination from '@/components/Pagination/index.vue'
import ArticleDialog from './components/ArticleDialog.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import clip from '@/utils/clipboard'
import { ElLoading } from 'element-plus'
import { ref } from 'vue'

const loading = ref(true)
const data = ref([])

const query = ref({
  page: 1,
  limit: getPageLimit(),
  name: '', // 板块名称
  countyid: 0 // 所属板块
})

const count = ref(0)

const queryRef = ref(null)

const visible = ref(false)

const currentID = ref('')

const imgUrl = ref([])

const getList = async () => {
  loading.value = true
  try {
    const res = await Zaobao.list(query.value)
    data.value = res.data.list
    count.value = res.data.count
  } finally {
    loading.value = false
  }
}

const resetQuery = () => {
  query.value.priceState = ''
  query.value.evaluating = ''
  queryRef.value.resetFields()
  handleQuery()
}

const handleQuery = () => {
  query.value.pageNum = 1
  getList()
}

const handleAdd = () => {
  visible.value = true
}

const handleEdit = (row) => {
  currentID.value = row.id
  visible.value = true
}

const handleClip = (row) => {
  const { protocol, domaim, useid } = row
  const url = `${protocol}://${domaim}/topic/zaobao/${useid}.html`
  console.log(url)
  clip(url)
}

const handleCreateImg = async (row,clcache) => {
  const { protocol, domaim, useid } = row
  const url = `${protocol}://${domaim}/topic/zaobao/${useid}.html`
  const loadingInstance = ElLoading.service()
  try {
    const { data } = await Zaobao.createScreenshot({ url,clcache })
    imgUrl.value = [data]
  } finally {
    loadingInstance.close()
  }

  // loadingInstance.close()
}

const handleClonsePreview = () => {}
const handleSetState = (row) => {
  const message = row.state === 1 ? '确认下架吗' : '确认上架吗'
  ElMessageBox({
    title: '提示',
    message,
    showCancelButton: true,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        try {
          const res = await Zaobao.stateChange({ id: row.id, state: row.state ? 0 : 1 })
          ElMessage.success('操作成功')
          resetQuery()
        } finally {
          instance.confirmButtonLoading = false
          done()
        }
      } else {
        done()
      }
    }
  })
}

onMounted(getList)
</script>

<style lang="scss" scoped></style>
