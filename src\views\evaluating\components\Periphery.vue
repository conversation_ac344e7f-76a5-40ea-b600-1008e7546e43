<template>
      <div class="app-container">
        <!-- 操作 -->
    <el-row>
      <el-col>
      
        <!-- <el-button title="导出" class="float-right" @click="selectOpen('export')">导出</el-button> -->
        <el-tooltip content="多文件上传需保证文件表头一致" effect="dark" placement="left">
          <excel-import
            v-if="checkPermission(['admin/evaluating.Evaluating/peripheryimport'])"
            title="导入"
            @on-import="imports"      />
        </el-tooltip>
      </el-col>
    </el-row>
      </div>

    <!-- 列表 -->
  <el-table
    ref="table"
    v-loading="loading"
    :data="data"
    :height="height"
    @sort-change="sort"
    @selection-change="select"
    key="table1"
  >
    <el-table-column type="selection" width="42" title="全选/反选" />
    <el-table-column :prop="idkey" label="ID" width="80" sortable="custom" />
    <el-table-column prop="around_house_name" label="周边楼盘名称" min-width="100" />
    <el-table-column prop="distancenum" label="距离本楼盘（m）" min-width="100" />
    <el-table-column prop="average_price" label="均价" min-width="80" >
      <template #default="scope"> 
       
          <el-text v-if="scope.row.price_unit == 1" type="success">{{ scope.row.average_price }}元/平米</el-text>
          <el-text v-else-if="scope.row.price_unit == 2" type="warning">{{ Math.round(scope.row.average_price/10000) }}万元/套</el-text>
          <el-text v-else>-</el-text>
      </template>
    </el-table-column>
    <el-table-column prop="house_type" label="类型" min-width="80" >
      <template #default="scope"> 
          <el-text v-if="scope.row.house_type == 1" type="success">新房</el-text>
          <el-text v-else-if="scope.row.house_type == 2" type="warning">二手房</el-text>
          <el-text v-else>-</el-text>
      </template>
    </el-table-column>
    <el-table-column prop="createtime" label="上传时间" :formatter='customformat("YYYY-MM-DD HH:mm:ss")'   />
    <el-table-column prop="updatetime" label="更新时间" :formatter='customformat("YYYY-MM-DD HH:mm:ss")'   />
    
  </el-table>
  <!-- 分页 -->
  <pagination
    v-show="count > 0"
    v-model:total="count"
    v-model:page="query.page"
    v-model:limit="query.limit"
    @pagination="getPeripheryList"
  />

  <el-dialog
    v-model="detailloading"
    :title="dialogTitle"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="cancel"
    width="90%"
    top="1vh"
  >

  <el-table
    ref="table2"
    v-loading="tableloading"
    :data="detaildata"
    :height="dialogheight"
    key="table2"
  >
    
    
    <el-table-column prop="buildnum" label="楼栋"  />
    <el-table-column prop="unit" label="单元"    />
    <el-table-column prop="area_num" label="面积"   />
    <el-table-column prop="housenumfull" label="户号"   />
    <el-table-column prop="floor_num" label="楼层"   >
    
     
    </el-table-column>
  </el-table>

</el-dialog>
</template>
<script setup>

import {ref, onMounted} from "vue"
import checkPermission from '@/utils/permission';
import Pagination from '@/components/Pagination/index.vue'
// import Evaluating from '@/api/project/evaluating'
import * as Evaluating from '@/api/project/evaluating'
import { getPageLimit } from '@/utils/settings'
import ExcelImport from '@/components/ExcelImport/multipleupload.vue'
import { customformat } from '@/utils/dateUtil'



const houseInfo = defineProps({house:{type:Object}});

const loading = ref(true);

const data = ref([])
const height = ref(560)
const dialogheight = ref(700)
const query = ref({
        page: 1,
        limit: getPageLimit(),
       
        projectid: houseInfo.house.id
      });
const count = ref(0);
const idkey = ref('id');
const detailloading = ref(false);
const tableloading = ref(true);
const detaildata = ref([])
const dialog = ref(false);
const dialogTitle = ref('');
const selection = ref([]);


function getPeripheryList(){
    loading.value = true;
    Evaluating.getPeripheryByID(query.value).then((res) => {
          data.value = res.data.list
          // count.value = res.data.count
      
          loading.value = false
        })
        .catch(() => {
          loading.value = false
        })

}

// 导入，results数据，header表头
function imports({ results, header }) {
      loading.value = true
      Evaluating.peripheryimports({
        import: results,
        projectid:houseInfo.house.id
      })
        .then((res) => {
          getPeripheryList()
          ElMessage.success(res.msg)
        })
        .catch(() => {
            loading.value = false
        })
    }
    function cancel() {
      detailloading.value = false
      
    }
// 排序
function sort(sort) {
      this.query.sort_field = sort.prop
      this.query.sort_value = ''
      if (sort.order === 'ascending') {
        this.query.sort_value = 'asc'
        this.list()
      }
      if (sort.order === 'descending') {
        this.query.sort_value = 'desc'
        this.list()
      }
    }
// 操作
function select(selection) {
      selection.value = selection
      // this.selectIds = this.selectGetIds(selection).toString()
    }
onMounted(()=>{
    // this.height = screenHeight(310)
    getPeripheryList()
})
</script>