#elementplus管理后台项目
1. 项目介绍
    1. 项目名称：elementplus管理后台项目
    2. 项目描述：基于elementplus的管理后台项目
    3. 项目技术：vue3+elementplus+scss
    4. 项目环境：node20+vue3+elementplus+scss
2. 项目目录
    1. src
        1. api
            1. 接口文件
        2. assets
            1. 静态资源文件
        3. components
            1. 全局组件文件
        4. layout
            1. 布局文件
        5. router
            1. 路由文件
        6. store
            1. 状态管理文件
        7. styles
            1. 全局样式文件
        8. utils
            1. 工具函数文件
        9. views
            1. 页面文件
        10. App.vue
        11. main.ts
    2. public
1