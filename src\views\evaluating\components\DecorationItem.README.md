# DecorationItem 装修条目标准管理组件

一个基于 Vue 3 + Element Plus 的装修条目标准管理组件，提供完整的 CRUD 功能，遵循项目现有的代码风格和架构。

## 功能特性

- ✅ 装修条目数据列表展示（表格形式）
- ✅ 搜索筛选功能（室内分类、入户门、不含项目）
- ✅ 分页功能（集成现有 Pagination 组件）
- ✅ 新增/编辑装修条目（弹窗表单）
- ✅ 删除装修条目（带确认提示）
- ✅ 响应式设计，适配不同屏幕尺寸
- ✅ 加载状态处理和错误处理
- ✅ 表单验证
- ✅ 状态管理（启用/禁用）

## 使用方法

### 基础用法

```vue
<template>
  <DecorationItem :id="projectId" />
</template>

<script setup>
import DecorationItem from '@/views/evaluating/components/DecorationItem.vue'

const projectId = ref('123')
</script>
```

### 高级用法

```vue
<template>
  <DecorationItem 
    ref="decorationItemRef"
    :id="projectId" 
  />
</template>

<script setup>
import DecorationItem from '@/views/evaluating/components/DecorationItem.vue'

const decorationItemRef = ref()
const projectId = ref('123')

// 刷新组件数据
const refreshData = () => {
  decorationItemRef.value?.refresh()
}

// 监听项目ID变化
watch(() => projectId.value, (newId) => {
  console.log('项目ID已变更:', newId)
})
</script>
```

## Props

| 参数 | 说明 | 类型 | 必填 | 默认值 |
|------|------|------|------|--------|
| id | 项目ID或其他标识符 | String/Number | 是 | - |

## 方法

| 方法名 | 说明 | 参数 |
|--------|------|------|
| refresh | 刷新组件数据 | - |

## 表格字段

| 字段 | 说明 | 类型 |
|------|------|------|
| 序号 | 记录的唯一标识 | Number |
| 室内分类 | 装修项目的分类 | String |
| 入户门 | 装修位置信息 | String |
| 不含项目 | 不包含的装修项目 | String |
| TODO项目 | 待完成的装修项目 | String |
| 小计单价 | 单项装修的价格 | String/Number |
| 总金额 | 装修项目的总价格 | String/Number |
| 状态 | 启用/禁用状态 | Number (1:启用, 0:禁用) |

## 编辑表单字段

| 字段 | 说明 | 类型 | 必填 | 组件类型 |
|------|------|------|------|----------|
| 大类 | 装修分类 | String | 是 | 下拉选择 |
| 位置 | 装修位置 | String | 是 | 下拉选择 |
| 类型 | 项目类型 | String | 是 | 下拉选择 |
| 品牌 | 相关品牌 | Array | 否 | 复选框组 |
| 不含项目 | 不包含的项目 | String | 否 | 文本域 |
| TODO项目 | 待完成项目 | String | 否 | 文本域 |
| 小计单价 | 单项价格 | String/Number | 否 | 数字输入框 |
| 总金额 | 总价格 | String/Number | 否 | 数字输入框 |
| 状态 | 启用状态 | Number | 否 | 单选按钮 |

## API 接口

组件依赖以下 API 接口（位于 `/src/api/decorationitem.js`）：

### 获取列表
```
GET /admin/decoration.Item/list
参数：
- page: 页码
- limit: 每页数量
- project_id: 项目ID
- category: 室内分类（可选）
- entrance_door: 入户门（可选）
- excluded_items: 不含项目（可选）
```

### 获取详情
```
GET /admin/decoration.Item/info
参数：
- id: 记录ID
```

### 添加数据
```
POST /admin/decoration.Item/add
数据：
- project_id: 项目ID
- category: 室内分类
- entrance_door: 入户门
- type: 类型
- brand: 品牌（数组）
- excluded_items: 不含项目
- todo_items: TODO项目
- unit_price: 小计单价
- total_amount: 总金额
- status: 状态
```

### 编辑数据
```
POST /admin/decoration.Item/edit
数据：同添加接口，需额外包含 id 字段
```

### 删除数据
```
POST /admin/decoration.Item/delete
数据：
- id: 记录ID
```

## 数据结构

### 列表数据格式
```javascript
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": 1,
        "category": "室内分类",
        "entrance_door": "入户门",
        "excluded_items": "不含项目",
        "todo_items": "TODO项目",
        "unit_price": "小计单价",
        "total_amount": "总金额",
        "status": 1
      }
    ],
    "total": 100
  }
}
```

## 技术实现

### 依赖组件
- Element Plus（表格、表单、弹窗等组件）
- Pagination（项目现有分页组件）
- svg-icon（图标组件）

### 工具函数
- `@/utils/request`：HTTP 请求工具
- `@/utils/settings`：设置工具（分页配置）
- `@/utils/screen-height`：屏幕高度计算

### 代码风格
- 使用 Vue 3 Composition API
- 遵循项目现有的命名规范
- 集成现有的错误处理机制
- 使用项目统一的样式规范

## 注意事项

1. 组件依赖 Element Plus，请确保已正确安装和配置
2. 需要配置对应的 API 接口
3. 组件会自动监听 `id` prop 的变化并重新加载数据
4. 如果 API 调用失败，组件会显示模拟数据作为降级处理
5. 表单验证规则可根据实际需求进行调整

## 开发和调试

### 查看示例
访问 `/examples/DecorationItemExample` 路由查看组件使用示例。

### 错误处理
组件包含完善的错误处理机制：
- API 调用失败时显示错误提示
- 表单验证失败时显示验证信息
- 删除操作前显示确认对话框

## 更新日志

### v1.0.0
- 初始版本
- 支持基础的 CRUD 功能
- 集成搜索和分页
- 响应式设计
- 遵循项目现有代码风格
