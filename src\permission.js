import router from '@/router';
import NProgress from 'nprogress';
import getPageTitle from '@/utils/page-title';

import { useUserStoreHook } from '@/store/modules/user';
import { usePermissionStoreHook } from '@/store/modules/permission';
import { translateRouteTitle } from '@/utils/i18n';
import { handleSystemRedirectLogin } from '@/utils/index';

import 'nprogress/nprogress.css';

// 进度条
NProgress.configure({ showSpinner: false });

const userStore = useUserStoreHook();

const permissionStore = usePermissionStoreHook();

// 白名单路由
const whiteList = ['/aiFeedback'];

// 设置标题
function handleUpdateDocumentTitleWithGuard(to) {
  document.title = getPageTitle(translateRouteTitle(to.meta.title));
}

// 从统一登陆登陆
function handleFromThirdLoginWithGuard(to, next) {
  const { systemtoken, redirect = '/' } = to.query;

  const loading = ElLoading.service({
    lock: true, text: '登录中...', background: 'rgba(0, 0, 0, 0.7)',
  });

  userStore.login({ systemtoken })
    .then(() => next({ path: redirect, replace: true }))
    .finally(() => loading.close());
}

// 已登陆守卫
function handleLoginedWithGuard(from, to, next) {
  const hasBindedRoles = userStore.user.roles && userStore.user.roles.length > 0;

  // 已构建过路由
  if (hasBindedRoles) {
    return to.matched.length !== 0 ? next() : (from.name ? next({ name: from.name }) : next('/404'));
  }

  // 首次构建路由
  userStore.userInfo()
    .then(({ menus }) => permissionStore.generateRoutes(menus))
    .then((accessRoutes) => {
      accessRoutes.forEach((route) => router.addRoute(route));
      next({ ...to, replace: true });
    })
    .catch((e) => {
      userStore.resetToken().then(() => handleSystemRedirectLogin(to.fullPath));
    });
}

// 来自白名单守卫
function handleFromWhiteListWithGuard(next) {
  next();
}

router.beforeEach((to, from, next) => {
  NProgress.start()

  // 设置页面标题
  handleUpdateDocumentTitleWithGuard(to);

  // 路由守卫逻辑
  const [hasToken, systemtoken] = [localStorage.getItem('AdminToken'), to.query.systemtoken];

  // 白名单 -> 放行
  if (whiteList.indexOf(to.path) !== -1) {
    return handleFromWhiteListWithGuard(next);
  }

  // 三方 systemtoken 登陆 -> 有值即优先处理
  if (systemtoken) {
    return handleFromThirdLoginWithGuard(to, next);
  }

  // 首次访问 || 主动退出 || 首次构建路由失败 -> 去登陆
  if (!hasToken) {
    return handleSystemRedirectLogin(to.fullPath);
  }

  // 已登陆（客户端标记）
  // 直接访问登陆页面 -> 内部重定向
  if (to.path === '/login') {
    return next('/dashboard');
  }

  // 需求鉴权的其他页面
  handleLoginedWithGuard(from, to, next);


  // if (hasToken) {
  //   if (to.path === '/login') {
  //     // 如果已登录，跳转首页
  //     next({ path: '/' })
  //     NProgress.done()
  //   } else {
  //     const hasRoles = userStore.user.roles && userStore.user.roles.length > 0
  //     if (hasRoles) {
  //       // 未匹配到任何路由，跳转404
  //       if (to.matched.length === 0) {
  //         from.name ? next({ name: from.name }) : next('/404')
  //       } else {
  //         next()
  //       }
  //     } else {
  //       try {
  //         const { menus } = await userStore.userInfo()
  //         const accessRoutes = await permissionStore.generateRoutes(menus)
  //         accessRoutes.forEach((route) => {
  //           router.addRoute(route)
  //         })
  //         next({ ...to, replace: true })
  //       } catch (error) {
  //         // 移除 token 并跳转登录页
  //         await userStore.resetToken()
  //         handleSystemRedirectLogin(to.fullPath);
  //         // location.href = import.meta.env.VITE_APP_LOGIN_URL
  //         NProgress.done()
  //       }
  //     }
  //   }
  // } else {
  //   console.log(to, from)
  
  //   const { systemtoken, redirect = '/' } = to.query
  //   if (systemtoken) {
  //     const loading = ElLoading.service({
  //       lock: true,
  //       text: '登录中...',
  //       background: 'rgba(0, 0, 0, 0.7)'
  //     })
  //     try {
  //       await userStore.login({ systemtoken })
  //       next({ path: redirect, replace: true })
  //     } finally {
  //       loading.close()
  //     }
  //   } else {
  //      if (whiteList.indexOf(to.path) !== -1) {
  //         next()
  //       } else {
  //         handleSystemRedirectLogin(to.fullPath);
  //         // location.href = import.meta.env.VITE_APP_LOGIN_URL
  //       }
  //   }
  // }
})

router.afterEach(() => {
  NProgress.done()
})
