import request from '@/utils/request'
// 会员机会汇总
const url = '/admin/member.Memberscore/'
/**
 * 获取会员积分汇总
 * @param {array} params 请求参数
 */
export function getScore(data) {
    return request({
        url: url + 'info',
        method: 'post',
        data
      })
}


/**
 * 获取置业顾问积分详情列表
 * @param {array} params 请求参数
 */
export function getScoreDetailList(data) {
    return request({
        url: url + 'detailist',
        method: 'post',
        data
      })
}


/**
 * 置业顾问审核
 * @param {array} params 请求参数
 */
export function addScoreDetail(data) {
    return request({
        url: url + 'addscordetail',
        method: 'post',
        data
      })
}

/**
 * 取消置业顾问身份
 * @param {array} params 请求参数
 */
export function identitycancel(data) {
    return request({
        url: url + 'cancel',
        method: 'post',
        data
      })
}