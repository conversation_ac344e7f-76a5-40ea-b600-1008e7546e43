import request from '@/utils/request';
// 预约班车列表
const url = '/admin/suggests.Carhailing/';

/**
 * 预约班车列表
 * @param {array} params 请求参数
 */
export function carhailingList(params) {
    return request({
      url: url + 'index',
      method: 'get',
      params: params
    })
}
/**
 * 修改预约班车信息
 * @param {array} data 请求数据
 */
export function editCarhailing(data) {
  return request({
    url: url + 'edit',
    method: 'post',
    data
  })
}

/**
 * 获取
 * @param {array} data 请求数据
 */
export function info(params) {
  return request({
    url: url + 'info',
    method: 'get',
    params: params
  })
}
