
<template>
  <div class="cp-wrap">
    <input
      ref="excel-upload-input"
      class="excel-upload-input"
      type="file"
      accept=".xlsx"
      @change="handleClick"
    />
    <el-button :loading="loading" @click="handleUpload">{{ title }}</el-button>
    <el-dialog
      v-model="dialogShow"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      top="5vh"
      width="88%"
    >
      <el-table v-loading="loading" :data="excelData.results" :height="height" style="width: 100%">
        <el-table-column
          v-for="item in excelData.header"
          :key="item"
          :prop="item"
          :label="item"
          sortable
          show-overflow-tooltip
        >
          <template v-if="item === '*产品名称'" #default="scope">
            <span v-if="keyExists('productList',scope.row[item])"> {{ scope.row[item] }}</span>
            <span v-else color="red"> {{ scope.row[item] }}</span>
          </template>
          <template v-else-if="item === '位置'" #default="scope">
            <span v-if="keyExists('positionList',scope.row[item])"> {{ scope.row[item] }}</span>
            <span v-else color="red"> {{ scope.row[item] }}</span>
          </template>
          <template v-else-if="item === '类型'" #default="scope">
            <span v-if="keyExists('categoryList',scope.row[item])"> {{ scope.row[item] }}</span>
            <span v-else color="red"> {{ scope.row[item] }}</span>
          </template>
          <template v-else-if="item === '材质'" #default="scope">
            <span v-if="keyExists('materialList',scope.row[item])"> {{ scope.row[item] }}</span>
            <span v-else color="red"> {{ scope.row[item] }}</span>
          </template>
          <template v-else-if="item === '品牌'" #default="scope" >
            <div v-html="brandExists('brandList', scope.row[item])"></div>
          </template>
          <template v-else #default="scope">
            <span > {{ scope.row[item] }}</span>
          </template>
          
        </el-table-column>
      </el-table>
      <div>
          <span color="red">注意：</span>橙色标注为未匹配到现有数据的内容，如确认导入，位置、类型、材质字段将自动创建并填写，但品牌字段无法自动填充，请导入后添加相应品牌后，找到具体条目进行添加。

强烈建议将未入库的品牌建立后再进行导入！强烈建议将未入库的品牌建立后再进行导入！强烈建议将未入库的品牌建立后再进行导入！
        </div>
      <template #footer>
        <el-button :loading="loading" @click="cancel">放弃导入</el-button>
        <el-button :loading="loading" type="primary" @click="submit">导入并覆盖现有数据</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import * as XLSX from 'xlsx'
import screenHeight from '@/utils/screen-height'
import dayjs from 'dayjs'
import { getDetailBatchValidList } from '@/api/intelligent/intelligentevaluation'
// 表格导入
export default {
  props: {
    limitSize: { type: Number, default: 1 },
    title: { type: String, default: '导入' },
    headerRowIndex: {
      type: Number,
      default: 1,
      validator: (value) => value >= 0,
      description: 'Excel中表头行的索引(从0开始计数)，数据将从表头行的下一行开始读取'
    }
  },
  data() {
    return {
      loading: false,
      height: 580,
      dialogTitle: '导入预览',
      dialogShow: false,
      itemParams:[],
      excelData: {
        filename: null,
        header: null,
        results: null
      }
    }
  },
  created() {
    this.height = screenHeight(340)
  },
  async mounted(){
    const res = await getDetailBatchValidList()
    this.itemParams = res.data
  },
  methods: {
    formatTime(date) {
      return dayjs(date).add(1, 'day').format('YYYY-MM-DD')
    },
    cancel() {
      this.dialogShow = false
    },
    submit() {
      this.dialogShow = false
      this.$emit('on-import', this.excelData)
    },
    generateData({ header, results,filename }) {
      console.log(results)

      this.excelData.header = header
      this.excelData.results = results
      this.excelData.filename = filename
      this.dialogShow = true
    },
    handleDrop(e) {
      e.stopPropagation()
      e.preventDefault()
      if (this.loading) return
      const files = e.dataTransfer.files
      if (files.length !== 1) {
        ElMessage.error('只能上传一个文件')
        return
      }
      const rawFile = files[0]

      if (!this.isExcel(rawFile)) {
        ElMessage.error('文件类型仅支持 xlsx')
        return false
      }
      this.upload(rawFile)
      e.stopPropagation()
      e.preventDefault()
    },
    handleUpload() {
      this.$refs['excel-upload-input'].click()
    },
    handleClick(e) {
      const files = e.target.files
      const rawFile = files[0]
      if (!rawFile) return
      this.upload(rawFile)
    },
    keyExists(type, key) {

        return this.itemParams[type].hasOwnProperty(key);
    },
   brandExists(type, key) {
  
        // 检查 key 是否包含 '、' 分隔符，如果是，则将其拆分为数组，否则直接使用 key 作为数组元素
        // 遍历数组中的每个元素，检查是否存在于 this.itemParams[type] 中，如果存在，则将其包裹在 <span> 标签中，否则将其包裹在 <span color="red"> 标签中
        let arr = key.split('、')
        let newarr = []
        arr.forEach(item => {
            if(this.itemParams[type].hasOwnProperty(item)){
                newarr.push("<span>"+item+"</span>")
            }else{
                newarr.push("<span color='red'>"+item+"</span>")
            }
        })
        return newarr.join('、');
    },
    upload(rawFile) {
      this.$refs['excel-upload-input'].value = null

      if (!this.beforeUpload) {
        this.readerData(rawFile)
        return
      }
      const before = this.beforeUpload(rawFile)
      if (before) {
        this.readerData(rawFile)
      }
    },
    beforeUpload(file) {
      const limitSize = this.limitSize
      const fileSize = file.size / 1024 / 1024

      if (fileSize > limitSize) {
        ElMessage.error(`文件大小不能大于 ${limitSize} m`)
        return false
      }

      return true
    },
    readerData(rawFile) {
      const filename = rawFile.name
      this.loading = true
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            const data = e.target.result
            const workbook = XLSX.read(data, { type: 'array', cellDates: true })
            const firstSheetName = workbook.SheetNames[0]
            const worksheet = workbook.Sheets[firstSheetName]
            const header = this.getHeaderRow(worksheet)
            // 设置读取数据的起始行
            const results = XLSX.utils.sheet_to_json(worksheet, {
              defval: '',
              range: this.headerRowIndex + 1  // 从表头行的下一行开始读取数据
            })
            this.generateData({ header, results, filename })
            this.loading = false
            resolve()
          } catch (error) {
            this.loading = false
            ElMessage.error('Excel文件解析失败')
            reject(error)
          }
        }
        reader.onerror = (error) => {
          this.loading = false
          ElMessage.error('文件读取失败')
          reject(error)
        }
        reader.readAsArrayBuffer(rawFile)
      })
    },
    getHeaderRow(sheet) {
      const headers = []
      const range = XLSX.utils.decode_range(sheet['!ref'])
      let C
      // 使用headerRowIndex确定表头行
      const R = this.headerRowIndex
      for (C = range.s.c; C <= range.e.c; ++C) {
        const cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })]
        let hdr = 'UNKNOWN ' + C
        if (cell && cell.t) hdr = XLSX.utils.format_cell(cell)
        headers.push(hdr)
      }
      return headers
    },
    isExcel(file) {
      return /\.(xlsx|xls|csv)$/.test(file.name)
    }
  }
}
</script>

<style scoped>
.cp-wrap {
  margin-left: 10px;
}
.excel-upload-input {
  display: none;
  z-index: -9999;
}
</style>
