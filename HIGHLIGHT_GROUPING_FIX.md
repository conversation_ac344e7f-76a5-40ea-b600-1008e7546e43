# 亮点数据分组提交修复说明

## 需求描述

将选中的亮点数据按照 `scene_type_raw` 字段进行分组，以 `{scene_type_raw:[id1,id2]}` 的结构提交给后端。

## 数据结构说明

### 原始亮点数据结构（getLightList返回）
```javascript
highlightsData = [
  {
    id: 1,
    name: "智能安防",
    scene_type: "安防类",
    scene_type_raw: "security",
    desc: "智能门锁、监控系统",
    product_list: ["智能门锁", "摄像头"]
  },
  {
    id: 2,
    name: "智能照明",
    scene_type: "照明类", 
    scene_type_raw: "lighting",
    desc: "智能开关、调光系统",
    product_list: ["智能开关", "调光器"]
  },
  {
    id: 3,
    name: "智能窗帘",
    scene_type: "照明类",
    scene_type_raw: "lighting", 
    desc: "电动窗帘控制",
    product_list: ["电动窗帘"]
  }
]
```

### 用户选择后的数据
```javascript
// 用户选择了 id=1, id=2, id=3 的亮点
formData.selectedHighlights = [
  {
    id: 1,
    name: "智能安防",
    scene_type_raw: "security",
    // ... 其他字段
  },
  {
    id: 2, 
    name: "智能照明",
    scene_type_raw: "lighting",
    // ... 其他字段
  },
  {
    id: 3,
    name: "智能窗帘", 
    scene_type_raw: "lighting",
    // ... 其他字段
  }
]
```

### 提交给后端的数据结构
```javascript
formData.selectedHighlights = {
  "security": [1],      // 安防类亮点ID数组
  "lighting": [2, 3]    // 照明类亮点ID数组
}
```

## 实现逻辑

### 修复前的处理方式
```javascript
// 只提取ID数组，丢失了分组信息
formData.selectedHighlights = formData.selectedHighlights.map(item => item.id)
// 结果: [1, 2, 3]
```

### 修复后的处理方式
```javascript
// 按scene_type_raw分组处理
const groupedHighlights = {}

formData.selectedHighlights.forEach(item => {
  if (typeof item === 'object' && item.scene_type_raw && item.id) {
    // 如果该scene_type_raw还没有数组，创建一个
    if (!groupedHighlights[item.scene_type_raw]) {
      groupedHighlights[item.scene_type_raw] = []
    }
    // 将id添加到对应的scene_type_raw数组中
    groupedHighlights[item.scene_type_raw].push(item.id)
  }
})

formData.selectedHighlights = groupedHighlights
```

## 数据流程示例

### 1. 用户选择亮点
```
用户勾选表格中的3个亮点:
- 智能安防 (id:1, scene_type_raw:"security")
- 智能照明 (id:2, scene_type_raw:"lighting") 
- 智能窗帘 (id:3, scene_type_raw:"lighting")
```

### 2. 选择变化处理
```javascript
handleHighlightSelectionChange(selection) {
  // selection = [完整的行对象数组]
  formData.selectedHighlights = selection
}
```

### 3. 提交时数据转换
```javascript
// 转换前
formData.selectedHighlights = [
  {id: 1, scene_type_raw: "security", ...},
  {id: 2, scene_type_raw: "lighting", ...},
  {id: 3, scene_type_raw: "lighting", ...}
]

// 转换后
formData.selectedHighlights = {
  "security": [1],
  "lighting": [2, 3]
}
```

### 4. 后端接收的数据
```json
{
  "house_id": 123,
  "count_score": 95,
  "selectedHighlights": {
    "security": [1],
    "lighting": [2, 3]
  },
  // ... 其他字段
}
```

## 关键代码实现

### 分组逻辑
```javascript
const groupedHighlights = {}

formData.selectedHighlights.forEach(item => {
  if (typeof item === 'object' && item.scene_type_raw && item.id) {
    // 初始化数组（如果不存在）
    if (!groupedHighlights[item.scene_type_raw]) {
      groupedHighlights[item.scene_type_raw] = []
    }
    // 添加ID到对应分组
    groupedHighlights[item.scene_type_raw].push(item.id)
  }
})
```

### 错误处理
```javascript
// 确保数据安全性
if (formData.selectedHighlights && Array.isArray(formData.selectedHighlights)) {
  // 执行分组逻辑
} else {
  // 如果不是数组，确保为空对象
  formData.selectedHighlights = {}
}
```

## 测试验证

### 1. 控制台调试信息
```
按scene_type_raw分组后的selectedHighlights: {
  "security": [1],
  "lighting": [2, 3]
}
```

### 2. 网络请求验证
在浏览器开发者工具的 Network 面板中查看提交的数据：
```json
{
  "selectedHighlights": {
    "security": [1],
    "lighting": [2, 3]
  }
}
```

### 3. 不同场景测试

#### 场景1：只选择一个分类的亮点
```javascript
// 只选择照明类
selectedHighlights = {
  "lighting": [2, 3]
}
```

#### 场景2：选择多个分类的亮点
```javascript
// 选择多个分类
selectedHighlights = {
  "security": [1],
  "lighting": [2, 3],
  "hvac": [4, 5]
}
```

#### 场景3：没有选择任何亮点
```javascript
// 空选择
selectedHighlights = {}
```

## 后端接口适配

### 后端期望的数据格式
```php
// PHP后端处理示例
$selectedHighlights = $request->input('selectedHighlights', []);

foreach ($selectedHighlights as $sceneType => $highlightIds) {
    // $sceneType: "security", "lighting" 等
    // $highlightIds: [1], [2, 3] 等ID数组
    
    foreach ($highlightIds as $highlightId) {
        // 处理每个亮点ID
        $this->saveHighlightRelation($intellectId, $sceneType, $highlightId);
    }
}
```

### 数据库存储建议
```sql
-- 亮点关联表
CREATE TABLE intellect_highlights (
    id INT PRIMARY KEY AUTO_INCREMENT,
    intellect_id INT NOT NULL,
    highlight_id INT NOT NULL,
    scene_type_raw VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 注意事项

1. **数据完整性**：确保 `scene_type_raw` 字段在亮点数据中存在且有效
2. **空值处理**：当没有选择任何亮点时，提交空对象 `{}`
3. **类型检查**：确保选中的数据是对象且包含必要字段
4. **向后兼容**：如果后端还需要支持旧格式，可以同时提交两种格式

## 优势

1. **语义清晰**：按分类组织数据，便于后端处理
2. **扩展性好**：支持任意数量的分类和亮点
3. **性能优化**：后端可以按分类批量处理
4. **数据一致性**：保持了亮点与分类的关联关系

现在提交给后端的 `selectedHighlights` 将是按 `scene_type_raw` 分组的对象结构，便于后端按分类处理亮点数据。
