<template>
  <div class="app-container">
    <!-- Query -->
    <div class="bg-[#ffffff] p-[10px] border-rd-10px mb-[10px]">
      <el-row>
        <el-form ref="queryRef" :model="query" :inline="true">
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="query.date_value"
              type="daterange"
              class="ya-date-value"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              :default-time="[new Date(2024, 1, 1), new Date(2024, 1, 1)]"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="query.state" style="width: 140px">
              <el-option
                v-for="states in stateSelectList"
                :key="states.value"
                :label="states.label"
                :value="states.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="search()">查询</el-button>
            <el-button title="重置" @click="refresh()">
              <svg-icon icon-class="refresh" />
            </el-button>
            <el-button type="primary" @click="add()">新建</el-button>

            <excel-import
              title="批量导入"
              @on-import="imports"
              style="display: inline"
              :data-state="'housedeal'"
            />
          </el-form-item>
        </el-form>
      </el-row>
    </div>

    <!-- List -->
    <el-table ref="table" v-loading="loading" :data="data" :height="height" @sort-change="sort">
      <el-table-column :prop="idkey" label="ID" width="80" />
      <el-table-column prop="deal_time" label="创建时间" width="165" />
      <el-table-column prop="new_house_sign_num" label="新房购买套数" min-width="130" />
      <el-table-column prop="new_house_sign_area" label="新房购买面积（万㎡）" min-width="130" />
      <el-table-column prop="new_house_sub_num" label="新房认购套数" min-width="130" />
      <el-table-column prop="new_house_sub_area" label="新房认购面积（万㎡）" min-width="130" />
      <el-table-column prop="new_house_sale_num" label="新房可售套数" min-width="130" />
      <el-table-column prop="new_house_sale_area" label="新房可售面积（万㎡）" min-width="130" />
      <el-table-column prop="old_house_sign_num" label="二手房成交套数" min-width="130" />
      <el-table-column prop="old_house_sign_area" label="二手房成交面积（万㎡）" min-width="130" />

      <el-table-column label="操作" width="160">
        <template #default="scope">
          <el-link type="primary" class="mr-1" :underline="false" @click="edit(scope.row)">
            修改
          </el-link>
          <el-link
            v-if="scope.row.state == 2"
            type="primary"
            :underline="false"
            @click="changeStateButton(scope.row.id, 1)"
          >
            发布
          </el-link>
          <el-link
            v-else-if="scope.row.state == 1"
            type="primary"
            :underline="false"
            @click="changeStateButton(scope.row.id, 2)"
          >
            下架
          </el-link>

          <el-link
            v-if="scope.row.state == 2"
            type="primary"
            :underline="false"
            @click="delHouseDeal(scope.row.id)"
          >
            删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <!-- Pagination -->
    <pagination
      v-show="count > 0"
      v-model:total="count"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="list"
    />
    <!-- Add/Edit Dialog -->
    <el-dialog
      v-model="dialog"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="cancel"
      top="5vh"
      :destroy-on-close="true"
    >
      <el-scrollbar native>
        <el-form ref="formRef" :model="model" label-width="150px">
          <el-form-item label="创建时间" prop="deal_time">
            <el-date-picker
              v-model="model.deal_time"
              type="date"
              placeholder="选择成交时间"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>

          <el-form-item label="新房成交" prop="new_house_sign_num">
            <el-col :span="11">
              <el-input v-model="model.new_house_sign_num" type="number" placeholder="请输入套数" />
            </el-col>
            <el-col :span="1"></el-col>
            <el-col :span="11">
              <el-input
                v-model="model.new_house_sign_area"
                type="number"
                placeholder="请输入面积"
              />
            </el-col>
          </el-form-item>

          <el-form-item label="新房认购" prop="new_house_sign_num">
            <el-col :span="11">
              <el-input v-model="model.new_house_sub_num" type="number" placeholder="请输入套数" />
            </el-col>
            <el-col :span="1"></el-col>
            <el-col :span="11">
              <el-input v-model="model.new_house_sub_area" type="number" placeholder="请输入面积" />
            </el-col>
          </el-form-item>

          <el-form-item label="新房可售" prop="new_house_sign_num">
            <el-col :span="11">
              <el-input v-model="model.new_house_sale_num" type="number" placeholder="请输入套数" />
            </el-col>
            <el-col :span="1"></el-col>
            <el-col :span="11">
              <el-input
                v-model="model.new_house_sale_area"
                type="number"
                placeholder="请输入面积"
              />
            </el-col>
          </el-form-item>

          <el-form-item label="二手房网签" prop="old_house_sign_num">
            <el-col :span="11">
              <el-input v-model="model.old_house_sign_num" type="number" placeholder="请输入套数" />
            </el-col>
            <el-col :span="1"></el-col>
            <el-col :span="11">
              <el-input
                v-model="model.old_house_sign_area"
                type="number"
                placeholder="请输入面积"
              />
            </el-col>
          </el-form-item>
          <el-form-item label="状态">
            <el-radio-group v-model="model.state">
              <el-radio :value="1">发布</el-radio>
              <el-radio :value="2">下架</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </el-scrollbar>
      <template #footer>
        <el-button :loading="loading" @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submit">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, reactive, computed, onMounted, toRefs } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getList,
  addItem,
  updateItem,
  deleteItems,
  changeState,
  onlineSigningImport,
  delHouseDealapi
} from '@/api/project/housedeal'
import screenHeight from '@/utils/screen-height'
import Pagination from '@/components/Pagination/index.vue'
import { arrayColumn } from '@/utils/index'
import { getPageLimit } from '@/utils/settings'

const idkey = 'id'
const height = 680
const loading = ref(false)
const dialog = ref(false)
const dialogTitle = ref('')
const data = ref([])
const count = ref(0)
const formRef = ref(null)
const table = ref(null)
const selectedItems = ref([])
const { proxy } = getCurrentInstance()

const query = reactive({
  page: 1,
  limit: 20,

  date_field: '',
  date_value: [],
  sort: '',
  state: 0
})

const stateSelectList = [
  {
    value: 0,
    label: '全部'
  },
  {
    value: '1',
    label: '发布'
  },
  {
    value: '2',
    label: '下架'
  }
]

const model = ref({
  id: '',
  new_house_sign_num: null,
  new_house_sign_area: null,
  new_house_sub_num: null,
  new_house_sub_area: null,
  new_house_sale_num: null,
  new_house_sale_area: null,
  old_house_sign_num: null,
  old_house_sign_area: null,
  state: 2,
  deal_time: ''
})

const retmodel = {
  id: null,
  new_house_sign_num: null,
  new_house_sign_area: null,
  new_house_sub_num: null,
  new_house_sub_area: null,
  new_house_sale_num: null,
  new_house_sale_area: null,
  old_house_sign_num: null,
  old_house_sign_area: null,
  state: 2,
  deal_time: ''
}

const search = () => {
  query.page = 1
  /* const params = {
        page: query.page,
        limit: query.limit,
        name: query.search_value,
        pageNum: query.page,
        state: query.state,
        startDate: query.date_value
      }; */

  list()
}

const refresh = () => {
  Object.assign(query, {
    page: 1,
    limit: 20,
    search_field: '',
    search_exp: '',
    search_value: '',
    date_field: '',
    date_value: [],
    sort: ''
  })
  list()
}
const list = async () => {
  loading.value = true
  try {
    const params = {
      page: query.page,
      limit: query.limit,
      state: query.state,

      start_date: query.date_value[0],
      end_date: query.date_value[1]
    }

    const res = await getList(params)
    if (res && res.data) {
      data.value = Array.isArray(res.data.list) ? res.data.list : []
      count.value = res.data.count || 0
    } else {
      data.value = []
      count.value = 0
      console.error('Invalid response format:', res)
    }
    await nextTick()
  } catch (error) {
    ElMessage.error('获取列表失败')
    data.value = []
    count.value = 0
  } finally {
    loading.value = false
  }
}

const sort = ({ prop, order }) => {
  query.sort = order ? `${prop},${order}` : ''
  list()
}

const select = (selection) => {
  selectedItems.value = selection
}

const add = () => {
  // model.value = retmodel
  reset()

  dialogTitle.value = '添加'
  dialog.value = true
}

function reset() {
  // model.value = retmodel

  Object.assign(model.value, retmodel)

  /* if (proxy.$refs['formRef'] !== undefined) {
      try {
        proxy.$refs['formRef'].resetFields()
        proxy.$refs['formRef'].clearValidate()
      } catch (error) {
        console.log(error)
      }
  } */
  // model.deal_time=""
  // model.new_house_sign_num=null
}

const edit = (row) => {
  Object.assign(model.value, row)

  if (row.deal_time) {
    const date = new Date(row.deal_time)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    model.value.deal_time = `${year}-${month}-${day}`
  }

  dialogTitle.value = '修改'
  dialog.value = true
}

const submit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (valid) {
      loading.value = true

      console.log(model.value)

      if (model.value['id']) {
        await updateItem(model.value)
        reset()
        ElMessage.success('修改成功')
      } else {
        await addItem(model.value)
        reset()
        ElMessage.success('添加成功')
      }
      dialog.value = false
      await list()
    }
  } catch (error) {
    ElMessage.error('提交失败')
  } finally {
    loading.value = false
  }
}

const cancel = () => {
  dialog.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const selectOpen = (type, rows = []) => {
  const items = rows.length > 0 ? rows : selectedItems.value
  if (items.length === 0) {
    return ElMessage.warning('请选择要操作的数据')
  }
  if (type === 'dele') {
    ElMessageBox.confirm('确定要删除选中的数据吗？', '提示', {
      type: 'warning'
    })
      .then(() => {
        deleteSelected(items)
      })
      .catch(() => {})
  }
}

const deleteSelected = async (items) => {
  loading.value = true
  try {
    await deleteItems(items.map((item) => item[idkey]))
    ElMessage.success('删除成功')
    list()
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  } finally {
    loading.value = false
  }
}
const changeStateButton = async (id, state) => {
  loading.value = true
  try {
    await changeState({ id: id, state: state })
    ElMessage.success('编辑成功')
    list()
  } catch (error) {
    ElMessage.error('删除失败')
  } finally {
    loading.value = false
  }
}

const delHouseDeal = (id) => {
  loading.value = true
  try {
    ElMessageBox.confirm('确定要删除选中的数据吗？', '提示', {
      type: 'warning'
    })
      .then(() => {
        delHouseDealapi({ id: id })
          .then(() => {
            ElMessage.success('删除成功')
            list()
          })
          .catch((e) => {
            console.log(e)
          })
      })
      .catch((e) => {
        console.log(e)
      })
  } catch (error) {
    ElMessage.error('删除失败')
  } finally {
    loading.value = false
  }
}
// 导入，results数据，header表头
const imports = async ({ results, header, state }) => {
  loading.value = true
  onlineSigningImport({
    import: results,
    state
  })
    .then((res) => {
      list()
      ElMessage.success(res.msg)
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}
onMounted(() => {
  nextTick(() => {
    console.log('Component mounted, formRef:', formRef.value)
  })
  list()
})
</script>