import request from '@/utils/request'
// 成交数据
const url = '/admin/house.HouseHot/'
/**
 * 热推专题列表
 * @param {array} params 请求参数
 */
export function getList(data) {
  return request({
    url: url + 'list',
    method: 'post',
    data
  })
}
/**
 * 热推详情
 * @param {array} params 请求参数
 */
export function info(data) {
  return request({
    url: url + 'info',
    method: 'post',
    data
  })
}
/**
 * 热推添加
 * @param {array} data 请求数据
 */
export function addItem(data) {
  return request({
    url: url + 'save',
    method: 'post',
    data
  })
}
/**
 * 热推修改
 * @param {array} data 请求数据
 */
export function updateItem(data) {
  return request({
    url: url + 'edit',
    method: 'post',
    data
  })
}
/**
 * 成交数据删除
 * @param {array} data 请求数据
 */
export function deleteItems(data) {
  return request({
    url: url + 'del',
    method: 'post',
    data
  })
}
/**
 * 成交数据删除
 * @param {array} data 请求数据
 */
export function changeState(data) {
  return request({
    url: url + 'release',
    method: 'post',
    data
  })
}
/**
 * 面积数据
 * @param {array} data 请求数据
 */
export function getAreaOptions(data) {
  return request({
    url: url + 'priceareas',
    method: 'post',
    data
  })
}
/**
 * 获取其他数据
 * @param {array} data 请求数据
 */
export function getMores(data) {
  return request({
    url: url + 'mores',
    method: 'post',
    data
  })
}
/**
 * 区域数据
 * @param {array} data 请求数据
 */
export function getregionsOptions(data) {
  return request({
    url: url + 'regions',
    method: 'post',
    data
  })
}
