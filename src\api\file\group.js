import request from '@/utils/request'
// 文件分组
const url = '/admin/file.Group/'
/**
 * 文件分组列表
 * @param {array} params 请求参数
 */
export function list(params) {
  return request({
    url: url + 'list',
    method: 'get',
    params: params
  })
}
/**
 * 文件分组信息
 * @param {array} params 请求参数
 */
export function info(params) {
  return request({
    url: url + 'info',
    method: 'get',
    params: params
  })
}
/**
 * 文件分组添加
 * @param {array} data 请求数据
 */
export function add(data) {
  return request({
    url: url + 'add',
    method: 'post',
    data
  })
}
/**
 * 文件分组修改
 * @param {array} data 请求数据
 */
export function edit(data) {
  return request({
    url: url + 'edit',
    method: 'post',
    data
  })
}
/**
 * 文件分组删除
 * @param {array} data 请求数据
 */
export function dele(data) {
  return request({
    url: url + 'dele',
    method: 'post',
    data
  })
}
/**
 * 文件分组是否禁用
 * @param {array} data 请求数据
 */
export function disable(data) {
  return request({
    url: url + 'disable',
    method: 'post',
    data
  })
}
/**
 * 文件分组文件
 * @param {array} params 请求参数
 */
export function file(params) {
  return request({
    url: url + 'file',
    method: 'get',
    params: params
  })
}
/**
 * 文件分组文件解除
 * @param {array} data 请求数据
 */
export function fileRemove(data) {
  return request({
    url: url + 'fileRemove',
    method: 'post',
    data
  })
}
