import request from '@/utils/request'

// 装修条目标准管理
const url = '/admin/decoration.Item/'

/**
 * 装修条目列表
 * @param {Object} params 请求参数
 */
export function list(params) {
  return request({
    url: url + 'list',
    method: 'get',
    params: params
  })
}

/**
 * 装修条目详情
 * @param {Object} params 请求参数
 */
export function info(params) {
  return request({
    url: url + 'info',
    method: 'get',
    params: params
  })
}

/**
 * 添加装修条目
 * @param {Object} data 请求数据
 */
export function add(data) {
  return request({
    url: url + 'add',
    method: 'post',
    data
  })
}

/**
 * 编辑装修条目
 * @param {Object} data 请求数据
 */
export function edit(data) {
  return request({
    url: url + 'edit',
    method: 'post',
    data
  })
}

/**
 * 删除装修条目
 * @param {Object} data 请求数据
 */
export function remove(data) {
  return request({
    url: url + 'delete',
    method: 'post',
    data
  })
}

/**
 * 批量删除装修条目
 * @param {Object} data 请求数据
 */
export function batchRemove(data) {
  return request({
    url: url + 'batchDelete',
    method: 'post',
    data
  })
}

/**
 * 更改装修条目状态
 * @param {Object} data 请求数据
 */
export function changeStatus(data) {
  return request({
    url: url + 'changeStatus',
    method: 'post',
    data
  })
}

/**
 * 导出装修条目
 * @param {Object} params 请求参数
 */
export function exportData(params) {
  return request({
    url: url + 'export',
    method: 'get',
    params: params,
    responseType: 'blob'
  })
}

/**
 * 导入装修条目
 * @param {Object} data 请求数据
 */
export function importData(data) {
  return request({
    url: url + 'import',
    method: 'post',
    data
  })
}
