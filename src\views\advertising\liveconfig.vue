<template>
  <div class="app-container">
    <el-form ref="queryRef" :model="query" :inline="true" label-width="90px">
      <el-form-item label="名称：" prop="name">
        <el-input
          v-model="query.name"
          maxlength="20"
          placeholder="请输入名称"
          style="width: 140px"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
        <el-button @click="handleAdd">添加</el-button>
      </el-form-item>
    </el-form>

    <el-table ref="table" v-loading="loading" :data="data" :height="600">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="名称" min-width="150" show-overflow-tooltip />
      <el-table-column prop="type" label="类型" width="120">
        <template #default="{ row }">
          <el-tag>{{ getTypeText(row.type) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="title" label="标题" min-width="150" show-overflow-tooltip />
      <el-table-column label="状态" width="80">
        <template #default="{ row }">
          <el-tag :type="row.state === 1 ? 'success' : 'info'">
            {{ row.state === 1 ? '有效' : '无效' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="create_at" label="创建时间" min-width="150" />
      <el-table-column prop="update_at" label="更新时间" min-width="150" />
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="scope">
          <el-link type="primary" class="mr-1" :underline="false" @click="handleEdit(scope.row)">
            编辑
          </el-link>
          <el-popconfirm title="确认删除该条数据吗？" @confirm="handleDelete(scope.row)">
            <template #reference>
              <el-link class="mr-1" type="danger" :underline="false">删除</el-link>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="count > 0"
      v-model:total="count"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="getList"
    />

    <el-dialog
      v-model="dialogshow"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="dialogcancel"
      width="700px"
      top="5vh"
    >
      <el-form ref="formRef" :model="model" :rules="rules" label-width="120px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="model.name" placeholder="请输入名称" clearable />
        </el-form-item>

        <el-form-item label="类型" prop="type">
          <el-select v-model="model.type" placeholder="请选择类型">
            <el-option label="视频号直播" :value="1" />
            <el-option label="视频号视频" :value="2" />
            <el-option label="上传视频" :value="3" />
          </el-select>
        </el-form-item>

        <!-- 视频号直播相关字段 -->
        <template v-if="model.type === 1">
          <el-form-item label="视频号ID" prop="video_num_id">
            <el-input v-model="model.video_num_id" placeholder="请输入视频号ID" clearable />
          </el-form-item>
          <el-form-item label="开始时间" prop="start_time">
            <el-date-picker
              v-model="model.start_time"
              type="datetime"
              placeholder="请选择开始时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              timezone="Asia/Shanghai"
            />
          </el-form-item>
          <el-form-item label="结束时间" prop="end_time">
            <el-date-picker
              v-model="model.end_time"
              type="datetime"
              placeholder="请选择结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              timezone="Asia/Shanghai"
            />
          </el-form-item>
        </template>

        <!-- 视频号视频相关字段 -->
        <template v-if="model.type === 2">
          <el-form-item label="视频号ID" prop="video_num_id">
            <el-input v-model="model.video_num_id" placeholder="请输入视频号ID" clearable />
          </el-form-item>
          <el-form-item label="是否同主体" prop="is_same_subject">
            <el-radio-group v-model="model.is_same_subject">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="视频ID" prop="video_id">
            <el-input
              v-model="model.video_id"
              placeholder="视频id或feed-token（非同主体）"
              clearable
            />
          </el-form-item>
        </template>

        <!-- 上传视频相关字段 -->
        <template v-if="model.type === 3">
          <el-form-item label="标题" prop="title">
            <el-input v-model="model.title" placeholder="请输入标题" clearable />
          </el-form-item>
          <el-form-item label="是否添加水印" prop="is_watermark">
            <el-radio-group v-model="model.is_watermark" style="margin-left: 10px">
              <el-radio label="否" :value="0"></el-radio>
              <el-radio label="是" :value="1"></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="视频封面" prop="cover_url">
            {{ is_watermark }}
            <div class="upload-container">
              <ImgUploadsHouseType
                v-model="model.cover_url"
                :upload-btn="model.cover_url?.length ? '' : '上传封面'"
                file-type="image"
                :height="200"
                @update:modelValue="handleCoverChange"
                :isWatermark="model.is_watermark"
                :source="model.source"
              />
            </div>
          </el-form-item>
          <el-form-item label="上传视频" prop="video_url">
            <div class="upload-container">
              <ImgUploadsHouseType
                v-model="model.video_url"
                :upload-btn="model.video_url?.length ? '' : '上传视频'"
                file-type="video"
                :height="200"
                @update:modelValue="handleVideoChange"
              />
            </div>
          </el-form-item>
        </template>

        <el-form-item label="状态" prop="state">
          <el-radio-group v-model="model.state">
            <el-radio :label="1">有效</el-radio>
            <el-radio :label="2">无效</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogcancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submit">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import ImgUploadsHouseType from '@/components/FileManage/ImgUploadsHouseType.vue'
import { getPageLimit } from '@/utils/settings'
import Pagination from '@/components/Pagination/index.vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

const loading = ref(false)
const dialogshow = ref(false)
const dialogTitle = ref('添加')
const data = ref([])
const formRef = ref(null)

// 初始数据模型
const initModel = {
  name: '',
  type: undefined,
  video_num_id: '',
  video_id: '',
  start_time: '',
  end_time: '',
  cover_url: [],
  video_url: [],
  state: 1,
  is_same_subject: 1,
  is_watermark: 0,
  source: 3
}

// 表单数据和验证规则
const model = ref({ ...initModel })
const rules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择类型', trigger: 'change' }],
  // 视频号直播相关验证
  video_num_id: [
    {
      required: true,
      message: '请输入视频号ID',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (model.value.type === 1 || model.value.type === 2) {
          if (!value) {
            callback(new Error('请输入视频号ID'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }
    }
  ],
  // 视频号视频相关验证
  video_id: [
    {
      required: true,
      message: '请输入视频ID',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (model.value.type === 2) {
          if (!value) {
            callback(new Error('请输入视频ID'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }
    }
  ],
  // 上传视频相关验证
  title: [
    {
      required: true,
      message: '请输入标题',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (model.value.type === 3) {
          if (!value) {
            callback(new Error('请输入标题'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }
    }
  ],
  cover_url: [
    {
      required: true,
      message: '请上传视频封面',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (model.value.type === 3) {
          if (!value || value.length === 0) {
            callback(new Error('请上传视频封面'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }
    }
  ],
  video_url: [
    {
      required: true,
      message: '请上传视频',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (model.value.type === 3) {
          if (!value || value.length === 0) {
            callback(new Error('请上传视频'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }
    }
  ]
}

const query = ref({
  page: 1,
  limit: getPageLimit(),
  name: ''
})

const count = ref(0)
const queryRef = ref(null)

// 处理文件变化的函数
const handleCoverChange = (files) => {
  if (files && files.length) {
    model.value.cover_url = [files[files.length - 1]]
  }
}

const handleVideoChange = (files) => {
  if (files && files.length) {
    model.value.video_url = [files[files.length - 1]]
  }
}

// 监听文件变化
watch(
  () => [model.value.cover_url, model.value.video_url],
  ([newCover, newVideo]) => {
    if (newCover && newCover.length > 1) {
      model.value.cover_url = [newCover[newCover.length - 1]]
    }
    if (newVideo && newVideo.length > 1) {
      model.value.video_url = [newVideo[newVideo.length - 1]]
    }
  },
  { deep: true }
)

// 获取类型文本
const getTypeText = (type) => {
  const types = {
    1: '视频号直播',
    2: '视频号视频',
    3: '上传视频'
  }
  return types[type] || '--'
}

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const res = await request({
      url: '/admin/advertise.RecLiveConfig/list',
      method: 'get',
      params: query.value
    })
    if (res.code === 200) {
      data.value = res.data.list || []
      count.value = res.data.count || 0
    } else {
      ElMessage.error(res.message || '获取列表失败')
    }
  } catch (error) {
    console.error('获取列表失败:', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

// 删除
const handleDelete = async (row) => {
  try {
    const res = await request({
      url: '/admin/advertise.RecLiveConfig/del',
      method: 'post',
      data: { id: row.id }
    })
    if (res.code === 200) {
      ElMessage.success('删除成功')
      getList()
    } else {
      ElMessage.error(res.message || '删除失败')
    }
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  }
}

// 关闭弹窗
const dialogcancel = () => {
  dialogshow.value = false
  formRef.value?.resetFields()
  model.value = { ...initModel }
}

// 重置查询
const resetQuery = () => {
  queryRef.value.resetFields()
  handleQuery()
}

// 查询
const handleQuery = () => {
  query.value.page = 1
  getList()
}

// 添加
const handleAdd = () => {
  dialogTitle.value = '添加'
  model.value = { ...initModel }
  dialogshow.value = true
}

// 编辑
const handleEdit = async (row) => {
  dialogTitle.value = '编辑'
  try {
    const res = await request({
      url: `/admin/advertise.RecLiveConfig/info?id=${row.id}`,
      method: 'get'
    })
    if (res.code === 200) {
      const start_time = res.data.start_time === '0000-00-00 00:00:00' ? '' : res.data.start_time
      const end_time = res.data.end_time === '0000-00-00 00:00:00' ? '' : res.data.end_time

      // 确保数据类型正确
      model.value = {
        ...initModel, // 先设置初始值
        ...res.data, // 再覆盖返回的数据
        type: Number(res.data.type), // 确保类型是数字
        state: Number(res.data.state),
        is_same_subject: Number(res.data.is_same_subject), // 确保是否同
        cover_url: res.data.cover_url ? [{ file_url: res.data.cover_url }] : [],
        video_url: res.data.video_url ? [{ file_url: res.data.video_url }] : [],
        start_time, // 使用处理后的时间
        end_time // 使用处理后的时间
      }
      dialogshow.value = true
    } else {
      ElMessage.error(res.msg || '获取详情失败')
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  }
}

// 提交
const submit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const submitData = {
          ...model.value,
          cover_url: model.value.cover_url?.[0]?.file_url || '',
          video_url: model.value.video_url?.[0]?.file_url || ''
        }

        if (submitData.type !== 3) {
          submitData.cover_url = ''
          submitData.video_url = ''
        }
        if (submitData.type !== 1) {
          submitData.start_time = ''
          submitData.end_time = ''
        }
        if (submitData.type !== 2) {
          submitData.is_same_subject = undefined
        }

        const res = await request({
          url: '/admin/advertise.RecLiveConfig/save',
          method: 'post',
          data: submitData
        })
        if (res.code === 200) {
          ElMessage.success('保存成功')
          dialogshow.value = false
          getList()
        } else {
          ElMessage.error(res.message || '保存失败')
        }
      } catch (error) {
        console.error('保存失败:', error)
        ElMessage.error('保存失败')
      } finally {
        loading.value = false
      }
    }
  })
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.el-form {
  padding: 0 20px;
}

.upload-container {
  width: 100%;
  margin: 10px 0;

  :deep(.el-upload) {
    width: 100%;
  }
}

:deep(.el-dialog) {
  .el-dialog__body {
    padding: 20px;
  }
}

:deep(.el-upload__tip) {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}
</style>
