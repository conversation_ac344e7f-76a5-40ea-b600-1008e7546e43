export default {
  // 会员
  member: {
    'Member statistic': '会员统计'
  },
  // 内容
  content: {
    content: '内容',
    category: '分类',
    tag: '标签',
    'Content statistic': '内容统计'
  },
  // 文件
  file: {
    file: '文件',
    group: '分组',
    tag: '标签',
    'File statistic': '文件统计',
    'file type': '文件类型'
  },
  // 公共
  common: {
    Notice: '公告',
    view: '查看',
    close: '关闭',
    tip: '提示',
    'Don not prompt again': '不再提示',
    'Please enter': '请输入',
    day: '日',
    month: '月',
    'Start date': '开始日期',
    'End date': '结束日期',
    'Count statistic': '总数统计',
    'Are you sure you want to exit the system?': '确定要退出系统吗？'
  },
  // 路由
  route: {
    dashboard: '首页',
    'System setting': '系统设置'
  },
  // 登录页面
  login: {
    username: '账号',
    password: '密码',
    captchaCode: '验证码',
    login: '登录',
    'Please enter username': '请输入账号',
    'Please enter password': '请输入密码',
    'Please enter captcha code': '请输入验证码',
    'Click to refresh the captcha code': '点击刷新验证码'
  },
  // 导航栏
  navbar: {
    'Clear Cache': '清除缓存',
    'full screen': '全屏切换',
    'Language switching': '语言切换',
    'Theme switching': '主题切换',
    dashboard: '首页',
    userCenter: '个人中心',
    logout: '退出系统'
  },
  // 标签栏
  TagsView: {
    Refresh: '刷新',
    Close: '关闭',
    'Close Other': '关闭其它',
    'Close left side': '关闭左侧',
    'Close right side': '关闭右侧',
    'Close all': '关闭所有'
  },
  // 行为验证码
  AjCaptch: {
    Refresh: '刷新',
    Close: '关闭',
    'Please complete security verification': '请完成安全验证',
    'Swipe right to complete verification': '向右滑动完成验证',
    'Verification successful': '验证成功',
    'Validation failed': '验证失败',
    'Please click on them one by one': '请依次点击'
  }
}
