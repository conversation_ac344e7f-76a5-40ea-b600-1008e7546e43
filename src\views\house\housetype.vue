<template>
  <div class="app-container">
    <!-- 查询 -->
     <div class="bg-[#ffffff] p-[10px] border-rd-10px mb-[10px]">
    <el-form ref="queryRef" :model="query" :inline="true" label-width="90px">
      <el-form-item label="户型名称：">
        <el-input v-model="query.name" class="ya-search-value" placeholder="户型名称" clearable />
      </el-form-item>
      <el-form-item label="所属楼盘：">
        <el-select
          v-model="query.house_id"
          class="w-full ya-search-value"
          clearable
          filterable
          remote
          :remote-method="houselist"
          :loading="loading"
        >
          <el-option
            v-for="item in search_houselist"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="评测展示：">
        <el-select v-model="query.show_rating" style="width: 120px" clearable>
          <el-option key="1" label="是" value="1" />
          <el-option key="0" label="否" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="发布状态：">
        <el-select v-model="query.release_status" style="width: 120px" clearable placeholder="全部">
          <el-option key="1" label="已发布" value="1" />
          <el-option key="0" label="待发布" value="0" />
        </el-select>
      </el-form-item>
    </el-form>
    <el-row>
      <el-col class="mb-2">
        <el-select v-model="query.date_field" class="ya-date-field" placeholder="时间类型">
          <el-option value="create_time" label="添加时间" />
          <el-option value="update_time" label="修改时间" />
        </el-select>
        <el-date-picker
          v-model="query.date_value"
          type="datetimerange"
          class="ya-date-value"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="[new Date(2024, 1, 1, 0, 0, 0), new Date(2024, 1, 1, 23, 59, 59)]"
        />
        &nbsp;&nbsp;
        <el-button type="primary" @click="search()">查询</el-button>
        <el-button title="重置" @click="refresh()">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" @click="add()">添加</el-button>
      </el-col>
    </el-row>
    <!-- 操作 -->
    <el-row>
      <el-col>
        <el-button title="删除" @click="selectOpen('dele')">删除</el-button>
        <el-button title="是否禁用" @click="selectOpen('disable')">禁用</el-button>
        <el-button title="发布时间" @click="selectOpen('release')">发布时间</el-button>
      </el-col>
    </el-row>
       </div>
    <el-dialog
      v-model="selectDialog"
      :title="selectTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      top="20vh"
    >
      <el-form ref="selectRef" label-width="120px">
        <el-form-item v-if="selectType === 'disable'" label="是否禁用">
          <el-switch v-model="is_disable" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item v-else-if="selectType === 'release'" label="发布时间">
          <el-date-picker
            v-model="release_time"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item v-else-if="selectType === 'dele'">
          <span class="c-red">确定要删除选中的{{ name }}吗？</span>
        </el-form-item>
        <el-form-item :label="name + 'ID'">
          <el-input v-model="selectIds" type="textarea" autosize disabled />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button :loading="loading" @click="selectCancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="selectSubmit">提交</el-button>
      </template>
    </el-dialog>
    <!-- 列表 -->
    <el-table
      ref="table"
      v-loading="loading"
      :data="data"
      :height="height"
      @sort-change="sort"
      @selection-change="select"
    >
      <el-table-column type="selection" width="42" title="全选/反选" />
      <el-table-column :prop="idkey" label="ID" width="80" sortable="custom" />
      <el-table-column prop="name" label="户型名称" min-width="105" show-overflow-tooltip />
      <el-table-column
        prop="build_area"
        label="建筑面积(㎡)"
        min-width="85"
        show-overflow-tooltip
      />
      <el-table-column
        prop="house_type_text"
        label="居室类型"
        min-width="85"
        show-overflow-tooltip
      />
      <el-table-column prop="house_name" label="所属楼盘" min-width="105" show-overflow-tooltip>
        <template #default="scope">
          <span v-text="search_params['houses'][scope.row.house_id]"></span>
        </template>
      </el-table-column>
      <el-table-column
        prop="user_rate"
        label="户型评分"
        min-width="85"
        show-overflow-tooltip
        sortable="custom"
      />

      <el-table-column prop="update_time" label="更新时间" width="165" sortable="custom" />
      <el-table-column prop="show_rating" label="评测展示" min-width="85">
        <template #default="scope">
          <span v-if="scope.row.show_rating == 1">是</span>
          <span v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column prop="release_status" label="发布状态" min-width="85">
        <template #default="scope">
          <span v-text="search_params['releaseStatus'][scope.row.release_status]"></span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="95">
        <template #default="scope">
          <el-link type="success" class="mr-1" :underline="false" @click="edit(scope.row)">
            修改
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="count > 0"
      v-model:total="count"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="list"
    />
    <!-- 添加修改 -->
    <el-dialog
      v-model="dialog"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="cancel"
      destroy-on-close
      width="1400px"
      top="5vh"
    >
      <el-form ref="ref" :rules="rules" :model="model" label-width="100px">
        <el-tabs>
          <el-tab-pane label="基础信息">
            <el-scrollbar native :height="height - 80">
              <el-row>
                <el-col :span="11">
                  <el-divider>
                    <h3 class="bold">基础信息</h3>
                  </el-divider>
                  <el-form-item label="发布状态" prop="scoreshow">
                    <el-radio-group v-model="model.release_status">
                      <el-radio :value="1">已发布</el-radio>
                      <el-radio :value="0">不发布</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="名称" prop="name">
                    <el-input v-model="model.name" placeholder="输入户型名称" clearable> </el-input>
                  </el-form-item>

                  <el-form-item label="居室" placeholder="室">
                    <el-col :span="4">
                      <el-select v-model="model.room" class="w-full" filterable>
                        <el-option v-for="item in 9" :key="item" :label="item" :value="item" />
                      </el-select>
                    </el-col>
                    <el-col :span="2" :offset="1"> 室 </el-col>
                    <el-col :span="4">
                      <el-select v-model="model.parlor" class="w-full" filterable>
                        <el-option v-for="item in 9" :key="item" :label="item" :value="item" />
                      </el-select>
                    </el-col>
                    <el-col :span="2" :offset="1"> 厅 </el-col>
                    <el-col :span="4">
                      <el-select v-model="model.toilet" class="w-full" filterable>
                        <el-option v-for="item in 9" :key="item" :label="item" :value="item" />
                      </el-select>
                    </el-col>
                    <el-col :span="2" :offset="1"> 卫 </el-col>
                  </el-form-item>
                  <el-form-item label="所属楼盘" prop="house_id" placeholder="请选择所属楼盘">
                    <el-select
                      v-model="model.house_id"
                      class="w-full"
                      clearable
                      filterable
                      remote
                      :remote-method="houselist"
                      :loading="loading"
                      @change="houseChange"
                    >
                      <el-option
                        v-for="item in search_houselist"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="销售状态" prop="sale_status">
                    <el-radio-group v-model="model.sale_status">
                      <el-radio :value="0">待售</el-radio>
                      <el-radio :value="1">在售</el-radio>
                      <el-radio :value="2">售罄</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="物业类型" prop="property_type">
                    <el-select v-model="model.property_type" class="w-full" filterable>
                      <el-option
                        v-for="(value, key) in search_params['propertyType']"
                        :key="key"
                        :label="value"
                        :value="key"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="建筑类型" prop="build_type">
                    <el-select v-model="model.build_type" class="w-full" filterable>
                      <el-option
                        v-for="(value, key) in search_params['buildingType']"
                        :key="key"
                        :label="value"
                        :value="key"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="朝向" prop="toward">
                    <el-select v-model="model.toward" class="w-full" clearable filterable>
                      <el-option
                        v-for="(value, key) in search_params['towards']"
                        :key="key"
                        :label="value"
                        :value="key"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    label="朝向偏离角度"
                    prop="toward_angle"
                    v-if="model.toward === 2 || model.toward === 4"
                  >
                    <el-col>
                      <el-input
                        v-model="model.toward_angle"
                        type="number"
                        min="0"
                        max="90"
                        placeholder="输入朝向偏离角度"
                        @input="checkTowardAngle"
                      >
                        <template #append>°</template>
                      </el-input>
                    </el-col>
                  </el-form-item>
                  <el-form-item label="建筑面积" prop="build_area">
                    <el-col>
                      <el-input
                        v-model="model.build_area"
                        type="number"
                        min="1"
                        placeholder="输入建筑面积"
                      >
                        <template #append>㎡</template>
                      </el-input>
                    </el-col>
                  </el-form-item>
                  <el-form-item label="套内面积" prop="inside_area">
                    <el-col>
                      <el-input
                        v-model="model.inside_area"
                        type="number"
                        min="1"
                        placeholder="输入套内面积"
                      >
                        <template #append>㎡</template>
                      </el-input>
                    </el-col>
                  </el-form-item>
                  <el-form-item label="赠送面积" prop="donate_area">
                    <el-col>
                      <el-input
                        v-model="model.donate_area"
                        type="number"
                        min="1"
                        placeholder="输入赠送面积"
                      >
                        <template #append>㎡</template>
                      </el-input>
                    </el-col>
                  </el-form-item>

                  <el-form-item label="绑定楼栋" prop="bind_builds">
                    <el-select
                      v-model="model.bind_builds"
                      class="w-full"
                      multiple
                      clearable
                      filterable
                      @change="bindBuildsChange"
                    >
                      <el-option
                        v-for="(value, key) in houseBuilds"
                        :key="value.buildnum"
                        :label="value.buildnum"
                        :value="value.buildnum"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="绑定住户" prop="holdsList">
                    <el-cascader
                      v-model="model.households"
                      :props="holdsProps"
                      :options="bindholdslist"
                      clearable
                    ></el-cascader>
                  </el-form-item>
                  <el-form-item label="户型标签" prop="tags">
                    <el-col :span="20">
                      <el-select
                        v-model="model.tags"
                        class="w-full"
                        multiple
                        allow-create
                        clearable
                        filterable
                      >
                        <el-option
                          v-for="(value, key) in tagsBasics"
                          :key="value.id"
                          :label="value.name"
                          :value="value.id"
                        />
                      </el-select>
                    </el-col>
                    <el-col :span="3" :offset="1">
                      <el-button @click="addOption">添加</el-button>
                    </el-col>
                  </el-form-item>
                  <el-form-item label="主力户型" prop="main_housetype">
                    <el-radio-group v-model="model.main_housetype">
                      <el-radio :value="1">是</el-radio>
                      <el-radio :value="0">否</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-divider>
                    <h3 class="bold">价格信息</h3>
                  </el-divider>
                  <el-form-item label="获取方式" prop="price_get">
                    <el-select
                      v-model="model.price_get"
                      class="w-full"
                      clearable
                      filterable
                      @change="priceGetChage"
                      placeholder="请选择价格获取方式"
                    >
                      <el-option key="0" label="手动维护" value="0" />
                      <el-option key="1" label="自动获取" value="1" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="绑定信息" prop="bind_hilds" v-if="model.price_get == 1">
                    <el-select
                      v-model="model.bind_holds"
                      class="w-full"
                      clearable
                      multiple
                      filterable
                      placeholder="请选择绑定住户"
                      @change="bindHoldsChange"
                    >
                      <el-option-group
                        v-for="(value, key) in houseHolds"
                        :key="key"
                        :label="'楼栋号：' + key"
                      >
                        <el-option
                          v-for="(val, k) in value"
                          :key="key + '#' + k"
                          :label="key + '#' + k"
                          :value="key + '#' + k"
                        >
                        </el-option>
                      </el-option-group>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="总价" prop="total_price">
                    <el-col>
                      <el-input
                        v-model="model.total_price"
                        type="number"
                        min="1"
                        placeholder="输入总价起价"
                      >
                        <template #append>万元</template>
                      </el-input>
                    </el-col>
                  </el-form-item>
                  <el-form-item label="最低总价" prop="min_total_price">
                    <el-col>
                      <el-input
                        v-model="model.min_total_price"
                        type="number"
                        min="1"
                        placeholder="输入总价起价"
                      >
                        <template #append>万元</template>
                      </el-input>
                    </el-col>
                  </el-form-item>
                  <el-form-item label="最高总价" prop="max_total_price">
                    <el-col>
                      <el-input
                        v-model="model.max_total_price"
                        type="number"
                        :min="1"
                        placeholder="输入总价最高价"
                        :aria-valuemin="1"
                      >
                        <template #append>万元</template>
                      </el-input>
                    </el-col>
                  </el-form-item>
                </el-col>
                <el-col :offset="1" :span="11">
                  <el-divider>
                    <h3 class="bold">图片信息</h3>
                  </el-divider>
                  <el-form-item label="图片描述" prop="tu_name">
                    <el-input v-model="model.tu_name" placeholder="图片描述" clearable />
                  </el-form-item>
                  <el-form-item label="是否添加水印" prop="is_watermark">
                    <el-radio-group v-model="model.is_watermark" style="margin-left: 10px">
                      <el-radio label="否" :value="0"></el-radio>
                      <el-radio label="是" :value="1"></el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="户型图" prop="unit_photo">
                    <ImgUploadsHouseType
                      v-model="model.unit_photo"
                      upload-btn="上传图片"
                      file-type="image"
                      file-tip=""
                      :isWatermark="model.is_watermark"
                      :source="source"
                    />
                  </el-form-item>
                  <el-form-item label="视频" prop="video">
                    <ImgUpload
                      v-model:file-url="model.video"
                      file-type="video"
                      :height="100"
                      :width="160"
                      upload
                    />
                    <span>只能上传50M以内的视频</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-scrollbar>
          </el-tab-pane>

          <el-tab-pane label="户型评测" v-if="model.id">
            <el-scrollbar native :height="height - 80">
              <el-row>
                <el-col :span="11">
                  <el-divider>
                    <h3 class="bold">展示控制</h3>
                  </el-divider>
                  <el-form-item label="是否展示" prop="show_rating">
                    <el-radio-group v-model="model.show_rating" @change="changePcShow">
                      <el-radio :value="1">是</el-radio>
                      <el-radio :value="0">否</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-divider>
                    <h3 class="bold">基础评分补充数据</h3>
                  </el-divider>
                  <el-form-item label="电梯数" prop="lifts">
                    <el-col>
                      <el-input
                        v-model="model.lifts"
                        type="number"
                        min="1"
                        placeholder="输入电梯数"
                      >
                        <template #append>部</template>
                      </el-input>
                    </el-col>
                  </el-form-item>

                  <el-form-item label="户数" prop="holds">
                    <el-col>
                      <el-input v-model="model.holds" type="number" min="1" placeholder="输入户数">
                        <template #append>户</template>
                      </el-input>
                    </el-col>
                  </el-form-item>

                  <el-form-item label="朝向分值" prop="toward_rate">
                    <el-col>
                      <el-input
                        v-model="model.toward_rate"
                        type="number"
                        min="1"
                        placeholder="朝向分值(百分制)"
                      >
                        <template #append>百分制</template>
                      </el-input>
                    </el-col>
                  </el-form-item>
                  <el-form-item label="无外窗" prop="unflow_room">
                    <el-col>
                      <el-input
                        v-model="model.unflow_room"
                        type="number"
                        min="1"
                        placeholder="无外窗房间数"
                      >
                        <template #append>不对流通风房间or无外窗空间总数</template>
                      </el-input>
                    </el-col>
                  </el-form-item>
                  <el-form-item label="户型层高" prop="floor_height">
                    <el-col>
                      <el-input
                        v-model="model.floor_height"
                        type="number"
                        min="1"
                        placeholder="户型层高"
                      >
                        <template #append>m</template>
                      </el-input>
                    </el-col>
                  </el-form-item>
                  <el-form-item label="户型面宽" prop="face_width">
                    <el-col>
                      <el-input
                        v-model="model.face_width"
                        type="number"
                        min="1"
                        placeholder="户型面宽"
                      >
                        <template #append>m</template>
                      </el-input>
                    </el-col>
                  </el-form-item>
                  <el-form-item label="户型进深" prop="face_depth">
                    <el-col>
                      <el-input
                        v-model="model.face_depth"
                        type="number"
                        min="1"
                        placeholder="户型进深"
                      >
                        <template #append>m</template>
                      </el-input>
                    </el-col>
                  </el-form-item>

                  <el-form-item label="评分描述" prop="basic_rate_desc">
                    <el-input
                      type="textarea"
                      :rows="4"
                      v-model="model.basic_rate_desc"
                      placeholder="输入基础评分描述"
                      clearable
                    />
                  </el-form-item>
                  <br />
                  <el-divider>
                    <h3 class="bold">空间评测数据补充</h3>
                  </el-divider>
                  <el-form-item
                    label="空间效率手动评分(0-100)"
                    prop="space_eff"
                    disabled="fiex"
                    align-items="center"
                  >
                    <el-input
                      type="number"
                      v-model="model.space_eff"
                      max="100"
                      placeholder="输入空间效率手动评分"
                      clearable
                      style="margin-bottom: 20px"
                    />
                  </el-form-item>
                  <el-form-item label="附属功能" prop="extra_use">
                    <el-checkbox-group v-model="model.extra_use">
                      <el-checkbox
                        v-for="(value, key) in search_params['extraUse']"
                        :label="value"
                        :value="key"
                        :key="key"
                      />
                    </el-checkbox-group>
                  </el-form-item>
                  <el-form-item label="动静分区" prop="act_static">
                    <el-radio-group v-model="model.act_static">
                      <el-radio :value="1">合理</el-radio>
                      <el-radio :value="0">不合理</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="低效空间" prop="useless_area">
                    <el-col>
                      <el-input
                        v-model="model.useless_area"
                        type="number"
                        min="1"
                        placeholder="交通空间&低效空间面积（㎡）"
                      >
                        <template #append>交通空间&低效空间面积（㎡）</template>
                      </el-input>
                    </el-col>
                  </el-form-item>
                  <el-form-item label="可变项数量" prop="variable_count">
                    <el-col>
                      <el-input
                        v-model="model.variable_count"
                        type="number"
                        min="1"
                        placeholder="可变项数量"
                      >
                        <template #append>个</template>
                      </el-input>
                    </el-col>
                  </el-form-item>
                  <el-form-item label="风水减分项" prop="wind_short">
                    <el-checkbox-group v-model="model.wind_short">
                      <el-checkbox
                        v-for="(value, key) in search_params['windShort']"
                        :label="value"
                        :value="key"
                        :key="key"
                      />
                    </el-checkbox-group>
                  </el-form-item>
                  <el-form-item label="评测描述" prop="space_rate_desc">
                    <el-input
                      type="textarea"
                      :rows="4"
                      v-model="model.space_rate_desc"
                      placeholder="输入空间评测描述"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :offset="1" :span="11">
                  <el-divider>
                    <h3 class="bold">
                      细节评测数据（{{ model.inside_area }}/{{ model.build_area }}㎡，{{
                        model.room
                      }}室{{ model.parlor }}厅{{ model.toilet }}卫-{{ model.room_json.length }}/{{
                        model.room + model.parlor + model.toilet
                      }}）
                    </h3>
                  </el-divider>
                  <el-col v-if="model.room_json.length == 0">
                    <div class="grid-content bordered-box">
                      <el-divider>
                        <h5 class="bold">房间信息0</h5>
                      </el-divider>

                      <el-form-item>
                        <el-col :span="14"> </el-col>
                        <el-col :span="10" :offset="7">
                          <el-button @click="addRooms(0)" type="primary">新增房间信息</el-button>
                        </el-col>
                      </el-form-item>
                    </div>
                  </el-col>
                  <el-col v-for="(item, idx) in model.room_json">
                    <div class="grid-content bordered-box">
                      <el-divider>
                        <h5 class="bold">房间信息{{ idx + 1 }}</h5>
                      </el-divider>
                      <el-form-item label="类型" prop="">
                        <el-col :span="15">
                          <el-select
                            v-model="item.room_type"
                            class="w-full"
                            filterable
                            placeholder="房间类型"
                            @change="changeRoomType(idx)"
                            :disabled="!item.room_newadd"
                          >
                            <el-option
                              v-for="(value, key) in search_params['roomTypes']"
                              :key="key"
                              :label="value"
                              :value="key"
                            />
                          </el-select>
                        </el-col>
                      </el-form-item>
                      <el-form-item
                        label="房间名称"
                        :prop="'room_json[' + idx + '].room_name'"
                        :rules="rules.room_name"
                      >
                        <el-col :span="15">
                          <el-input v-model="item.room_name" placeholder="输入房间名称"> </el-input>
                        </el-col>
                      </el-form-item>
                      <el-form-item
                        label="房间面积"
                        :prop="'room_json[' + idx + '].room_area'"
                        :rules="rules.room_area"
                      >
                        <el-col :span="15">
                          <el-input
                            v-model="item.room_area"
                            type="number"
                            min="1"
                            placeholder="输入房间面积"
                          >
                            <template #append>㎡</template>
                          </el-input>
                        </el-col>
                      </el-form-item>
                      <el-form-item
                        label="房间面宽"
                        :prop="'room_json[' + idx + '].room_face_width'"
                        :rules="rules.room_face_width"
                      >
                        <el-col :span="15">
                          <el-input
                            v-model="item.room_face_width"
                            type="number"
                            min="1"
                            placeholder="房间面宽"
                          >
                            <template #append>m</template>
                          </el-input>
                        </el-col>
                      </el-form-item>
                      <el-form-item
                        label="房间进深"
                        :prop="'room_json[' + idx + '].room_face_depth'"
                        :rules="rules.room_face_depth"
                      >
                        <el-col :span="15">
                          <el-input
                            v-model="item.room_face_depth"
                            type="number"
                            min="1"
                            placeholder="房间进深"
                          >
                            <template #append>m</template>
                          </el-input>
                        </el-col>
                      </el-form-item>
                      <el-form-item
                        label="有明窗"
                        prop="has_out_window"
                        v-if="[3, 5, 6].includes(item.room_type)"
                      >
                        <el-col :span="15">
                          <el-radio-group v-model="item.has_out_window">
                            <el-radio :value="1">是</el-radio>
                            <el-radio :value="0">否</el-radio>
                          </el-radio-group>
                        </el-col>
                      </el-form-item>
                      <el-form-item
                        label="是否通风"
                        prop="has_air_inout"
                        v-if="item.room_type == 3"
                      >
                        <el-col :span="15">
                          <el-radio-group v-model="item.has_air_inout">
                            <el-radio :value="1">是</el-radio>
                            <el-radio :value="0">否</el-radio>
                          </el-radio-group>
                        </el-col>
                      </el-form-item>
                      <el-form-item
                        label="房间功能项"
                        :prop="'room_json[' + idx + '].room_func'"
                        :rules="rules.room_func"
                        v-if="item.room_type == 0 || item.room_type == 1"
                      >
                        <el-col :span="15">
                          <el-checkbox-group v-model="item.room_func" @change="roomFuncChange(idx)">
                            <el-checkbox
                              v-for="(value, key) in search_params['roomFunc']['bedroom']"
                              :label="value"
                              :value="key"
                              :key="key"
                            />
                          </el-checkbox-group>
                        </el-col>
                      </el-form-item>
                      <el-form-item
                        label="房间功能项"
                        :prop="'room_json[' + idx + '].room_func'"
                        :rules="rules.room_func"
                        v-else-if="item.room_type == 4"
                      >
                        <el-col :span="15">
                          <el-checkbox-group v-model="item.room_func" @change="roomFuncChange(idx)">
                            <el-checkbox
                              v-for="(value, key) in search_params['roomFunc']['cook']"
                              :label="value"
                              :value="key"
                              :key="key"
                            />
                          </el-checkbox-group>
                        </el-col>
                      </el-form-item>
                      <el-form-item
                        v-if="item.room_type == 0 || item.room_type == 1"
                        label="家居支持项"
                        prop="room_support"
                      >
                        <el-col :span="3">
                          <el-select v-model="item.room_support.bed1800" class="w-full" filterable>
                            <el-option
                              v-for="num in 5"
                              :key="num - 1"
                              :label="num - 1"
                              :value="num - 1"
                            />
                          </el-select>
                        </el-col>
                        <el-col :span="3"> &nbsp;1.8m床 </el-col>
                        <el-col :span="3">
                          <el-select v-model="item.room_support.bed1500" class="w-full" filterable>
                            <el-option
                              v-for="num in 5"
                              :key="num - 1"
                              :label="num - 1"
                              :value="num - 1"
                            />
                          </el-select>
                        </el-col>
                        <el-col :span="3"> &nbsp;1.5m床 </el-col>
                        <el-col :span="3">
                          <el-select v-model="item.room_support.bedtable" class="w-full" filterable>
                            <el-option
                              v-for="num in 5"
                              :key="num - 1"
                              :label="num - 1"
                              :value="num - 1"
                            />
                          </el-select>
                        </el-col>
                        <el-col :span="3"> &nbsp;床头柜 </el-col>
                      </el-form-item>
                      <el-form-item
                        v-if="item.room_type == 0 || item.room_type == 1"
                        label=""
                        prop=""
                      >
                        <el-col :span="3">
                          <el-select v-model="item.room_support.wardrobe" class="w-full" filterable>
                            <el-option
                              v-for="num in 5"
                              :key="num - 1"
                              :label="num - 1"
                              :value="num - 1"
                            />
                          </el-select>
                        </el-col>
                        <el-col :span="3"> &nbsp;衣柜 </el-col>
                        <el-col :span="3">
                          <el-select v-model="item.room_support.tv_stand" class="w-full" filterable>
                            <el-option
                              v-for="num in 5"
                              :key="num - 1"
                              :label="num - 1"
                              :value="num - 1"
                            />
                          </el-select>
                        </el-col>
                        <el-col :span="3"> &nbsp;电视柜 </el-col>
                        <el-col :span="3">
                          <el-select v-model="item.room_support.tv_wall" class="w-full" filterable>
                            <el-option
                              v-for="num in 5"
                              :key="num - 1"
                              :label="num - 1"
                              :value="num - 1"
                            />
                          </el-select>
                        </el-col>
                        <el-col :span="3"> &nbsp;壁挂电视 </el-col>
                      </el-form-item>
                      <el-form-item
                        label="优缺点"
                        v-for="(item2, indx) in item.pros_cons"
                        :prop="'room_json[' + idx + '].pros_cons[' + indx + '].desc'"
                        :rules="rules.desc"
                      >
                        <el-col :span="4">
                          <el-select v-model="item2.type" class="w-full" filterable>
                            <el-option key="good" label="优点" value="good" />
                            <el-option key="bad" label="缺点" value="bad" />
                          </el-select>
                        </el-col>
                        <el-col :span="10">
                          <el-input v-model="item2.desc" placeholder="输入描述（15个汉字以内）" />
                        </el-col>

                        <el-col :span="5" :offset="1">
                          <el-button @click="addPoints(idx)" type="primary">+</el-button>
                          <el-button @click="cancelPoints(idx, indx)" v-if="indx > 0">-</el-button>
                        </el-col>
                      </el-form-item>
                      <el-form-item>
                        <el-col :span="14"> </el-col>
                        <el-col :span="10" :offset="1">
                          <el-button @click="addRooms(idx + 1)" type="primary"
                            >新增房间信息</el-button
                          >
                          <el-button @click="cancelRooms(idx)">删除</el-button>
                        </el-col>
                      </el-form-item>
                    </div>
                  </el-col>
                  <el-col>
                    <el-divider> </el-divider>
                    <el-form-item label="重新算分">
                      <el-col :span="15">
                        <el-checkbox v-model="model.caculate">是</el-checkbox>
                      </el-col>
                    </el-form-item>
                  </el-col>
                </el-col>
              </el-row>
            </el-scrollbar>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <el-button :loading="loading" @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submit">提交</el-button>
      </template>
    </el-dialog>

    <el-dialog
      v-model="addoptionDialog"
      title="添加户型标签"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      top="20vh"
      width="20%"
    >
      <el-form ref="addoptionRef" label-width="120px">
        <el-form-item label="标签名称">
          <el-input v-model="tagmodel.name" placeholder="输入标签名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button :loading="loading" @click="addoptionCancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="addoptionSubmit">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import screenHeight from '@/utils/screen-height'
import Pagination from '@/components/Pagination/index.vue'
import { arrayColumn } from '@/utils/index'
import { getPageLimit } from '@/utils/settings'
import {
  list,
  info,
  add,
  edit,
  dele,
  housebuilds,
  householdlist,
  disable,
  housetypeholds,
  release
} from '@/api/project/housetype'
import { tagsbygroup, addTag } from '@/api/project/tag'

import { houseList } from '@/api/project/house'
import { ElMessage } from 'element-plus'

const tagGroupId = 7
export default {
  name: 'HouseTypeList',
  components: { Pagination },

  data() {
    return {
      name: '户型',
      height: 680,
      loading: false,
      idkey: 'id',
      search_params: {},
      search_houselist: [],
      exps: [{ exp: 'like', name: '包含' }],
      query: {
        page: 1,
        show_rating: null,
        release_status: null,
        name: null,
        house_id: null,
        limit: getPageLimit(),
        date_field: 'create_time'
      },
      holdsProps: { multiple: true },
      data: [],
      count: 0,
      dialog: false,
      dialogTitle: '',
      tagmodel: {
        group_id: tagGroupId,
        name: ''
      },
      bindholdslist: [],
      model: {
        id: '',
        name: '',
        property_type: null,
        build_type: null,
        bind_builds: [],
        // 绑定房号列表
        bind_holds: [],
        build_area: '',
        donate_area: '',
        useless_area: '',
        main_housetype: 0,
        room: 1,
        parlor: 1,
        toilet: 1,
        video: null,
        house_id: null,
        basic_rate_desc: null,
        space_rate_desc: null,
        toward_rate: 0.1,
        salestate: '',
        price_get: '0',
        min_total_price: '',
        max_total_price: '',
        lifts: 1,
        holds: 1,
        show_rating: 0,
        unflow_room: 0,
        floor_height: 3,
        face_depth: 0,
        extra_use: [],
        act_static: 0,
        variable_count: 0,
        user_rate: 0,
        release_status: 0,
        total_price: '',
        tags: [],
        room_json: [],
        unit_photo: [],
        wind_short: [], // 风水减分项
        caculate: false, // 重新计算评分
        is_watermark: 0,
        source: 2
      },
      rules: {},
      selection: [],
      selectIds: '',
      selectTitle: '操作',
      selectDialog: false,
      addoptionDialog: false,
      selectType: '',
      is_hot: 0,
      is_rec: 0,
      is_disable: 0,
      release_time: '',
      tagsBasics: [], // 户型基础数据那里的标签
      tagsPingce: [], // 户型评测那里的标签
      houseBuilds: [], // 当前楼盘所有的楼栋列表
      houseHolds: [], // 当前所有的居室列表
      validRules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        // total_price: [{ required: true, message: '请输入总价', trigger: 'blur' }],
        // min_total_price: [{ required: true, message: '请输入最低总价', trigger: 'blur' }],
        house_id: [{ required: true, message: '请选择所属楼盘', trigger: 'blur' }],
        property_type: [{ required: true, message: '请选择物业类型', trigger: 'blur' }],
        build_type: [{ required: true, message: '请选择建筑类型', trigger: 'blur' }],
        toward: [{ required: true, message: '请选择户型朝向', trigger: 'blur' }],
        build_area: [{ required: true, message: '请填写建筑面积', trigger: 'blur' }],
        inside_area: [{ required: true, message: '请填写套内面积', trigger: 'blur' }],
        room: [{ required: true, message: '请填写选择居室数量', trigger: 'blur' }],
        sale_status: [{ required: true, message: '请填写销售状态', trigger: 'blur' }],
        // max_total_price: [
        // 	{
        // 		required: true,
        // 		message: '请输入最高总价”',
        // 		 trigger: 'blur' ,
        // 	 }
        // ],
        room_json: []
      },
      validPcRules: {
        lifts: [{ required: true, message: '请输入电梯数', trigger: 'blur' }],
        holds: [{ required: true, message: '请输入住户数', trigger: 'blur' }],
        toward_rate: [{ required: true, message: '请输入户型评测-朝向分值', trigger: 'blur' }],
        unflow_room: [{ required: true, message: '请输入无外窗房间数', trigger: 'blur' }],
        floor_height: [{ required: true, message: '请输入户型层高', trigger: 'blur' }],
        face_width: [{ required: true, message: '请输入户型面宽', trigger: 'blur' }],
        face_depth: [{ required: true, message: '请输入户型进深', trigger: 'blur' }],
        useless_area: [{ required: true, message: '请输入低效空间面积', trigger: 'blur' }],
        variable_count: [{ required: true, message: '请输入可变项数量', trigger: 'blur' }]
      },
      // 验证房间
      validRoomRules: {
        room_face_width: [{ required: true, message: '请输入房间面宽', trigger: 'blur' }],
        room_face_depth: [{ required: true, message: '请输入房间进深', trigger: 'blur' }],
        room_name: [{ required: true, message: '请填写房间名称', trigger: 'blur' }],
        room_area: [{ required: true, message: '请填写房间面积', trigger: 'blur' }],
        desc: [{ required: true, message: '请填写优缺点详情', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.height = screenHeight()
    this.rules = this.validRules
    if (this.$route.query.house_id) {
      this.query.house_id = parseInt(this.$route.query.house_id)
    }
    this.list()
    this.basicTags()
    this.pinCeTags()
    if (this.query.house_id) {
      this.houselistbyid(this.query.house_id)
    }
  },
  methods: {
    checkTowardAngle() {
      if (this.model.toward_angle > 90 || this.model.toward_angle <= 0) {
        this.model.toward_angle = 1
        ElMessage({
          message: '朝向偏离角度不能小于0°且不超过90°',
          type: 'warning'
        })
      }
    },
    addRooms(index) {
      // 房间对象
      const roomsJson = {
        room_key: 0, // 房间标识
        room_name: '', // 房间标识
        room_newadd: 1, // 房间标识
        room_type: 0, // 房间类型 0 ：主卧
        room_area: 0, // 面积 0 ：
        room_face_width: 0, // 面宽 0 ：
        room_face_depth: 0, // 房间进深 0 ：
        has_out_window: 0, // 是否有明窗
        has_air_inout: 0, // 是否通风
        room_func: [], // 卧室功能
        room_func_desc: [], // 卧室功能
        room_support: {
          bed1800: 0, // 1.8米床个数
          bed1800_desc: '1.8米床', // 1.8米床个数
          bed1500: 0, // 1.5米床个数
          bed1500_desc: '1.5米床', // 1.5米床个数
          bedtable: 0, // 床头柜个数
          bedtable_desc: '床头柜', // 床头柜个数
          wardrobe: 0, // 衣服柜个数
          wardrobe_desc: '衣服柜', // 衣服柜个数
          tv_stand: 0, // 电视柜个数
          tv_stand_desc: '电视柜', // 电视柜个数
          tv_wall: 0, // 壁挂电视个数
          tv_wall_desc: '壁挂电视' // 壁挂电视个数
        },
        pros_cons: [
          { type: 'good', desc: '' } // 优点
          // {"type":"bad","desc":""}, // 缺点
        ]
      }
      var timestamp1 = new Date().getTime()

      var room_key = timestamp1 + '' + Math.floor(Math.random() * 10)
      roomsJson['room_key'] = String(room_key)

      this.model.room_json.push(roomsJson)
      var room_rules = {}
      this.initRoomRUles()
      length = this.model.room_json.length

      this.changeRoomType(length - 1)
      // console.log(room_rules)

      // this.rules = {...this.rules,...room_rules};
      // this.rules.room_json.push(room_rules);
      // console.log(this.rules)
      // validRoomRules
    },
    initRoomRUles() {
      if (this.model.room_json.length > 0) {
        for (let key in this.validRoomRules) {
          if (!this.rules.hasOwnProperty(key)) {
            this.rules[key] = this.validRoomRules[key]
          }
        }
      }
    },
    cancelRooms(index) {
      this.model.room_json.splice(index, 1)
    },
    addPoints(index) {
      const prosCons = { type: 'good', desc: '' } // 优点
      this.model.room_json[index].pros_cons.push(prosCons)
    },
    cancelPoints(index, index2) {
      this.model.room_json[index].pros_cons.splice(index2, 1)
    },
    changePcShow() {
      if (this.model.show_rating == 1) {
        this.rules = { ...this.validRules, ...this.validPcRules }
      } else {
        this.rules = this.validRules
      }
    },
    priceGetChage() {
      if (this.model.price_get == 1) {
        this.bindBuildsChange()
      }
    },
    // 修改房间功能项
    roomFuncChange(index) {
      this.model.room_json[index].room_func_desc = []
      if (
        this.model.room_json[index].room_type == 0 ||
        this.model.room_json[index].room_type == 1
      ) {
        this.model.room_json[index].room_func.forEach((funcItem) => {
          this.model.room_json[index].room_func_desc.push(
            this.search_params['roomFunc']['bedroom'][funcItem]
          )
        })
      } else if (this.model.room_json[index].room_type == 4) {
        this.model.room_json[index].room_func.forEach((funcItem) => {
          this.model.room_json[index].room_func_desc.push(
            this.search_params['roomFunc']['cook'][funcItem]
          )
        })
      }
    },
    // 新增标签
    addOption() {
      this.addoptionDialog = true
    },
    // 取消新增标签
    addoptionCancel() {
      this.addoptionDialog = false
      this.tagmodel.name = ''
    },
    // 提交新增标签
    addoptionSubmit() {
      if (this.tagmodel.name == '') {
        ElMessage.error('请输入标签名称')
        return
      }
      var is_exist = 0
      this.tagsBasics.forEach((item) => {
        if (item.name == this.tagmodel.name) {
          is_exist = 1
          return
        }
      })
      if (is_exist == 1) {
        ElMessage.error('标签已经存在')
        return
      }

      addTag(this.tagmodel)
        .then((res) => {
          var mm = { id: res.data, name: this.tagmodel.name }
          this.tagsBasics.push(mm)
          this.model.tags.push(res.data)
          ElMessage.success('添加标签成功')
          this.addoptionCancel()
        })
        .catch(() => {
          ElMessage.error('添加标签失败')
          this.addoptionDialog = false
          return
        })
      this.addoptionDialog = false
    },
    // 修改绑定楼栋
    bindBuildsChange() {
      householdlist({ house_id: this.model.house_id, build_nums: this.model.bind_builds })
        .then((res) => {
          this.houseHolds = res.data
        })
        .catch(() => {
          this.houseHolds = []
        })
    },
    // 绑定居室
    bindHoldsChange() {
      var minTotalPric = 0
      var maxTotalPric = 0
      var avgTotalPric = 0
      var totalPric = 0
      var nums = 0
      for (let key in this.houseHolds) {
        for (let k in this.houseHolds[key]) {
          var kname = key + '#' + k
          if (this.model.bind_holds.includes(kname)) {
            var item = this.houseHolds[key][k]
            if (nums == 0) {
              nums += item['num']
            }
            if (totalPric == 0) {
              totalPric += item['totalPrice']
            }
            if (maxTotalPric == 0) {
              maxTotalPric = item['maxprice']
            } else {
              if (maxTotalPric < item['maxprice']) {
                maxTotalPric = item['maxprice']
              }
            }
            if (minTotalPric == 0) {
              minTotalPric = item['minprice']
            } else {
              if (minTotalPric > item['minprice']) {
                minTotalPric = item['minprice']
              }
            }
          }
        }
      }
      avgTotalPric = (totalPric / nums).toFixed(2)
      this.model.total_price = avgTotalPric
      this.model.max_total_price = maxTotalPric
      this.model.min_total_price = minTotalPric
    },
    // 验证最大总价是否大于最小总价
    validMaxTotalPrice() {
      return this.model.max_total_price >= this.model.min_total_price
    },
    // 列表
    list() {
      this.loading = true
      list(this.query)
        .then((res) => {
          this.data = res.data.list
          this.count = res.data.count
          this.exps = res.data.exps
          this.loading = false
          this.search_params = res.data.search_params
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 户型基础信息标签
    basicTags() {
      tagsbygroup({ groupid: tagGroupId })
        .then((res) => {
          this.tagsBasics = res.data
        })
        .catch(() => {})
    },
    // 户型评测标签获取
    pinCeTags() {
      tagsbygroup({ groupid: 8 })
        .then((res) => {
          this.tagsPingce = res.data
        })
        .catch(() => {})
    },
    // 获取楼盘所有的楼栋
    housebuilds() {
      housebuilds({ house_id: this.model.house_id })
        .then((res) => {
          this.houseBuilds = res.data
        })
        .catch(() => {})
    },
    // 获取楼盘所有的楼栋,房间
    houseBindHolds() {
      housetypeholds({ house_id: this.model.house_id, housetype_id: this.model.id })
        .then((res) => {
          this.bindholdslist = res.data
          // console.log(this.bindholdslist)
        })
        .catch(() => {})
    },
    // 根据楼盘名称获取楼盘列表
    houselist(keywords) {
      // if(keywords==""){
      // 	return
      // }
      houseList({ name: keywords })
        .then((res) => {
          this.search_houselist = res.data.list
        })
        .catch((error) => {})
    },
    // 根据楼盘名称获取楼盘列表
    houselistbyid(id) {
      houseList({ id: id })
        .then((res) => {
          this.search_houselist = res.data.list
        })
        .catch((error) => {})
    },
    houseChange() {
      this.model.bind_builds = []
      this.houseBuilds = []
      this.houseHolds = []
      this.model.bind_holds = []

      if (this.model.house_id == undefined) {
        return
      }
      let resultArr = this.search_houselist.find((item) => {
        return item.id == this.model.house_id
      })
      // alert(JSON.stringify(resultArr))
      this.model.property_type = resultArr.property_type[0]
      this.model.build_type = resultArr.build_type[0]

      this.housebuilds()
      this.houseBindHolds()
    },
    // 添加修改
    add() {
      this.dialog = true
      this.dialogTitle = this.name + '添加'
      this.reset()
    },
    edit(row) {
      this.dialog = true
      this.dialogTitle = this.name + '修改：' + row[this.idkey]
      var id = {}
      id[this.idkey] = row[this.idkey]
      info(id)
        .then((res) => {
          res.data.is_watermark = 0
          this.reset(res.data)
          // this.houselist(res.data.house_name)
          this.houselistbyid(res.data.house_id)
          this.housebuilds()
          this.priceGetChage()
          this.changePcShow()
          this.initRoomRUles()
          this.houseBindHolds()
        })
        .catch(() => {})
    },
    cancel() {
      this.dialog = false
      this.reset()
    },
    submit() {
      // if (this.model.room_json.length > 0) {
      //  this.model.room_json.forEach((item) => {
      //   if(item.room_face_width < 1){
      // 	  ElMessage.success("请先补充")
      //   }
      //  })

      // }
      this.$refs['ref'].validate((valid) => {
        if (valid) {
          if (this.model[this.idkey]) {
            // console.log(this.model["room_json"][0])
            // this.model["room_json"].forEach(value,index){
            // 	console.log(value)
            // }

            edit(this.model)
              .then((res) => {
                this.list()
                this.dialog = false
                ElMessage.success(res.msg)
              })
              .catch(() => {})
          } else {
            add(this.model)
              .then((res) => {
                this.list()
                this.dialog = false
                ElMessage.success(res.msg)
              })
              .catch(() => {})
          }
        } else {
          // const errors = this.$refs.myForm.errors;
          // Object.values(errors)[0];
          // console.log(errors)
          ElMessage.error('请完善必填项（带红色星号*）')
        }
      })
    },
    // 重置
    reset(row) {
      if (row) {
        this.model = row
        this.model.caculate = false
      } else {
        console.log(this.$options.data())
        this.model = this.$options.data().model
        this.model.caculate = false
        this.model.room_json = []
      }
      // if(this.model.room_json.length == 0){
      // 	this.model.room_json.push(roomsJson)
      // }
      if (this.$refs['ref'] !== undefined) {
        try {
          this.$refs['ref'].resetFields()
          this.$refs['ref'].clearValidate()
        } catch (error) {
          console.log(error)
        }
      }
    },
    // 查询
    search() {
      this.query.page = 1
      this.list()
    },
    // 刷新
    refresh() {
      const limit = this.query.limit
      this.query = this.$options.data().query
      this.$refs['table'].clearSort()
      this.query.limit = limit
      this.list()
    },
    // 排序
    sort(sort) {
      this.query.sort_field = sort.prop
      this.query.sort_value = ''
      if (sort.order === 'ascending') {
        this.query.sort_value = 'asc'
        this.list()
      }
      if (sort.order === 'descending') {
        this.query.sort_value = 'desc'
        this.list()
      }
    },
    // 操作
    select(selection) {
      this.selection = selection
      this.selectIds = this.selectGetIds(selection).toString()
    },
    selectGetIds(selection) {
      return arrayColumn(selection, this.idkey)
    },
    selectAlert() {
      ElMessageBox.alert('请选择需要操作的' + this.name, '提示', {
        type: 'warning',
        callback: () => {}
      })
    },
    selectOpen(selectType, selectRow = '') {
      if (selectRow) {
        this.$refs['table'].clearSelection()
        const selectRowLen = selectRow.length
        for (let i = 0; i < selectRowLen; i++) {
          this.$refs['table'].toggleRowSelection(selectRow[i], true)
        }
      }
      if (!this.selection.length) {
        this.selectAlert()
      } else {
        this.selectTitle = '操作'
        if (selectType === 'disable') {
          this.selectTitle = this.name + '是否禁用'
        } else if (selectType === 'release') {
          this.selectTitle = this.name + '发布时间'
        } else if (selectType === 'dele') {
          this.selectTitle = this.name + '删除'
        }
        this.selectDialog = true
        this.selectType = selectType
      }
    },
    selectCancel() {
      this.selectDialog = false
    },
    selectSubmit() {
      if (!this.selection.length) {
        this.selectAlert()
      } else {
        const selectType = this.selectType
        if (selectType === 'disable') {
          this.disable(this.selection, true)
        } else if (selectType === 'release') {
          this.release(this.selection, true)
        } else if (selectType === 'dele') {
          this.dele(this.selection)
        }
        this.selectDialog = false
      }
    },

    // 是否禁用
    disable(row, select = false) {
      if (!row.length) {
        this.selectAlert()
      } else {
        this.loading = true
        var is_disable = row[0].is_disable
        if (select) {
          is_disable = this.is_disable
        }
        disable({
          ids: this.selectGetIds(row),
          is_disable: is_disable
        })
          .then((res) => {
            this.list()
            ElMessage.success(res.msg)
          })
          .catch(() => {
            this.list()
          })
      }
    },
    // 发布时间
    release(row) {
      if (!row.length) {
        this.selectAlert()
      } else {
        this.loading = true
        release({
          ids: this.selectGetIds(row),
          release_time: this.release_time
        })
          .then((res) => {
            this.list()
            ElMessage.success(res.msg)
          })
          .catch(() => {
            this.list()
          })
      }
    },
    // 修改房间类型
    changeRoomType(idx) {
      var index = this.model.room_json[idx].room_type
      this.model.room_json[idx].room_name = this.search_params['roomTypes'][index]
    },
    // 删除
    dele(row) {
      if (!row.length) {
        this.selectAlert()
      } else {
        this.loading = true
        dele({
          ids: this.selectGetIds(row)
        })
          .then((res) => {
            this.list()
            ElMessage.success(res.msg)
          })
          .catch(() => {
            this.loading = false
          })
      }
    }
  }
}
</script>
<style>
.bordered-box {
  border: 1px solid #d3dce6; /* 设置边框样式 */
  border-radius: 4px; /* 可选：设置圆角 */
  padding: 10px; /* 内边距 */
  box-sizing: border-box; /* 确保边框不影响内部内容的宽度 */
  margin-top: 10px;
}
</style>
