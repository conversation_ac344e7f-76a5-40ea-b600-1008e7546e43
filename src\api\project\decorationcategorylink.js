import request from '@/utils/request'
// 装修分类关联管理
const url = '/admin/furnish.DecorationCategoryLink/'

/**
 * 关联列表
 * @param {array} params 请求参数
 */
export function list(params) {
  return request({
    url: url + 'list',
    method: 'get',
    params: params
  })
}

/**
 * 创建关联
 * @param {array} data 请求数据
 */
export function create(data) {
  return request({
    url: url + 'create',
    method: 'post',
    data
  })
}

/**
 * 更新关联
 * @param {array} data 请求数据
 */
export function update(data) {
  return request({
    url: url + 'update',
    method: 'post',
    data
  })
}

/**
 * 删除关联
 * @param {array} params 请求参数
 */
export function deleteItem(params) {
  return request({
    url: url + 'delete',
    method: 'get',
    params: params
  })
}

/**
 * 变更关联状态
 * @param {array} data 请求数据
 */
export function changeStatus(data) {
  return request({
    url: url + 'changeStatus',
    method: 'post',
    data
  })
}