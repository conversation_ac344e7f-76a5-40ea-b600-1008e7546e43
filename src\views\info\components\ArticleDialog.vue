<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    custom-class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
    width="900px"
    :z-index="1000"
    top="2vh"
  >
    <el-scrollbar native :height="600">
      <el-form class="form-box" ref="formRef" :rules="rules" :model="model" label-width="120px">
        <el-form-item label="分享标题" prop="title" required>
          <el-input v-model="model.title" placeholder="请输入分享名称" clearable />
        </el-form-item>
        <el-form-item label="首页区块标题1" prop="title1" required>
          <el-input
            v-model="model.title1"
            placeholder="请输入标题"
            clearable
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="首页区块标题2" prop="title2">
          <el-input v-model="model.title2" placeholder="请输入标题" clearable />
        </el-form-item>
        <el-form-item label="首页区块标题3" prop="title3">
          <el-input v-model="model.title3" placeholder="请输入标题" clearable />
        </el-form-item>

        <el-form-item label="分享描述" prop="describe" required>
          <el-input v-model="model.describe" placeholder="请输入分享描述" clearable />
        </el-form-item>
        <el-form-item label="发布时间" prop="publishtime" required>
          <el-date-picker
            v-model="model.publishtime"
            type="date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            placeholder="请选择时间"
          />
        </el-form-item>
        <el-form-item label="是否添加水印" prop="is_watermark">
          <el-radio-group v-model="model.is_watermark" style="margin-left: 10px">
            <el-radio label="否" :value="0"></el-radio>
            <el-radio label="是" :value="1"></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="分享图片" prop="shareimg_id" required>
          <ImgUpload
            :isWatermark="model.is_watermark"
            :source="model.source"
            v-model="model.shareimg_id"
            v-model:file-url="model.shareimg_url"
            file-type="image"
            :height="100"
            upload
          />
        </el-form-item>
        <el-form-item label="早报内容" prop="content" required>
          <Editor v-if="isEtitShow" ref="editorRef" v-model="model.content" :init="tinymceInit"
        /></el-form-item>
      </el-form>
    </el-scrollbar>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleBeforeClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading"> 提交 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, reactive, ref, onBeforeUnmount } from 'vue'
import { tinymceInit } from './tinymceInit'
import Editor from '@tinymce/tinymce-vue'
import { onBeforeRouteLeave } from 'vue-router'
import { add, edit, getInfo, getTemplate } from '@/api/info/zaobao'
import { ElMessage } from 'element-plus'
import { template } from './template'
const FORM_DATA = {
  title: '',
  describe: '',
  shareimg_id: 0,
  shareimg_url: '',
  content: '',
  publishtime: '',
  title1: '',
  title2: '',
  title3: '',
  is_watermark: 0,
  source: 3
}

const emits = defineEmits(['close', 'update'])

const visible = defineModel('visible', {
  type: Boolean,
  default: false
})

const props = defineProps({
  id: [String, Number]
})

const formRef = ref(null)

const model = ref({ ...FORM_DATA })

const loading = ref(false)

const rules = reactive({
  title: [{ required: true, message: '请填写分享标题', trigger: ['blur', 'change'] }],
  title1: [
    { required: true, message: '请填写首页区块标题1', trigger: ['blur', 'change'] },
    { max: 100, message: '标题长度不能超过100个字符', trigger: ['blur', 'change'] }
  ],
  describe: [{ required: true, message: '请填写分享描述', trigger: ['blur', 'change'] }],
  shareimg_id: [{ required: true, message: '请上传分享图片', trigger: ['blur', 'change'] }],
  content: [{ required: true, message: '请填写早报内容', trigger: ['blur', 'change'] }],
  publishtime: [{ required: true, message: '请选择发布时间', trigger: ['blur', 'change'] }]
})

const isEdit = computed(() => !!props.id || props.id === 0)

const dialogTitle = computed(() => (isEdit.value ? '编辑早报' : '创建早报'))

const handleBeforeClose = () => {
  visible.value = false
}

const isEtitShow = ref(true)

const editorRef = ref(null)

// 解决富文本离开页面时候样式丢失问题
onBeforeRouteLeave(() => {
  isEtitShow.value = false
})

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  isEtitShow.value = false
})

const getContent = async () => {
  try {
    // let api = isEdit.value ? getInfo : getTemplate
    const res = await getInfo({ id: props.id })

    const obj = {}
    for (let key in FORM_DATA) {
      if (res.data[key]) {
        obj[key] = res.data[key]
      }
    }
    model.value = { ...obj, id: res.data.id }
    model.value.is_watermark = 0
  } finally {
  }
}
// getTemplate
const getDefault = async () => {
  try {
    const res = await getTemplate()
    console.log(res.data)
    // if (!Object.keys(res.data).length) {
      model.value.content = template
      return
    // }
    const obj = {}
    for (let key in FORM_DATA) {
      if (res.data[key]) {
        obj[key] = res.data[key]
      }
    }
    model.value = { ...obj }
    model.value.is_watermark = 0
  } finally {
  }
}

const handleOpen = () => {
  if (isEdit.value) {
    getContent()
  } else {
    getDefault()
  }
}

// 销毁富文本
const handleClose = () => {
  visible.value = false
  model.value = { ...FORM_DATA }
  emits('close')
}

const fetchSave = async () => {
  loading.value = true
  const api = isEdit.value ? edit : add
  try {
    const data = await api(model.value)
    ElMessage.success('提交成功')
    emits('update')
    handleClose()
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    fetchSave()
  } finally {
  }
}
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>
<style lang="scss" scoped>
.form-box {
  :deep(.el-input) {
    width: 450px;
  }
}
</style>
