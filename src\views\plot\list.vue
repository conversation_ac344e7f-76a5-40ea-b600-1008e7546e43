<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-form ref="queryRef" :model="query" :inline="true" label-width="90px">
      <el-form-item label="地块ID：" prop="id">
        <el-input v-model="query.id" placeholder="请输入地块ID" style="width: 200px" />
      </el-form-item>
      <el-form-item label="地块名称：" prop="plot_name">
        <el-input v-model="query.plot_name" placeholder="请输入地块名称" style="width: 200px" />
      </el-form-item>
      <el-form-item label="供地批次：" prop="batch_id">
        <el-select v-model="query.batch_id" placeholder="请选择批次" style="width: 160px">
          <el-option label="全部" value="" />
          <el-option
            v-for="item in batchOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="区县板块：" prop="region_id">
        <el-cascader
          v-model="query.region_id"
          :options="regionData"
          :props="regionProps"
          placeholder="选择区域板块"
          clearable
          style="width: 220px"
        />
      </el-form-item>
      <el-form-item label="交易状况：" prop="status">
        <el-select v-model="query.status" placeholder="请选择" style="width: 160px">
          <el-option label="全部" value="" />
          <el-option
            v-for="item in dictData.plotTradingStatus"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间：" prop="create_time">
        <el-date-picker
          v-model="query.create_time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          style="width: 300px"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
        <el-button type="success" @click="handleAdd">添加</el-button>
        <el-button
          ><a
            href="https://img.betterhousing.cn/develop/simple/other/202504/01/9AB6B06BDBEF835784186DF31D529E70.xlsx"
            >下载导入模板</a
          >
        </el-button>
        <excel-import title="批量导入" @on-import="imports" />
      </el-form-item>
    </el-form>

    <!-- 表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      style="width: 100%; margin-top: 20px"
      row-key="id"
      border
    >
      <el-table-column prop="id" label="地块ID" width="80" />
      <el-table-column prop="batch_id" label="供地批次" width="120">
        <template #default="{ row }">
          {{ getBatchLabel(row.batch_id) }}
        </template>
      </el-table-column>
      <el-table-column prop="plot_name" label="地块名称" min-width="200" show-overflow-tooltip />
      <el-table-column prop="region_name" label="区县" width="100" />
      <el-table-column prop="sector_name" label="板块" width="100" />
      <el-table-column label="交易状况" width="100">
        <template #default="{ row }">
          {{ getDictLabel(dictData.plotTradingStatus, parseInt(row.transaction_status)) }}
        </template>
      </el-table-column>
      <el-table-column prop="house_name" label="所属项目" width="180" show-overflow-tooltip />
      <el-table-column prop="create_time" label="创建时间" width="180" />
      <el-table-column label="发布状态" width="100">
        <template #default="{ row }">
          <el-tag :type="Number(row.status) === 1 ? 'success' : 'info'">
            {{ Number(row.status) === 1 ? '上架' : '下架' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="240" fixed="right">
        <template #default="{ row }">
          <el-button
            link
            :type="Number(row.status) === 1 ? 'danger' : 'success'"
            @click="handlePublishChange(row)"
          >
            {{ Number(row.status) === 1 ? '下架' : '上架' }}
          </el-button>
          <el-link type="primary" class="mr-1" :underline="false" @click="handleEdit(row)">
            编辑
          </el-link>
          <el-link
            v-if="!row.house_id && row.transaction_status === 3"
            type="primary"
            class="mr-1"
            :underline="false"
            @click="handleBindHouse(row)"
          >
            绑定楼盘
          </el-link>
          <el-link type="warning" class="mr-1" :underline="false" @click="handleCopy(row)">
            复制
          </el-link>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="query.page"
        v-model:page-size="query.limit"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 绑定楼盘弹窗 -->
    <el-dialog v-model="bindDialogVisible" title="绑定楼盘" width="600px" destroy-on-close>
      <div class="search-box">
        <el-input
          v-model="searchHouseName"
          placeholder="请输入楼盘名称"
          clearable
          @input="handleHouseSearch"
        >
          <template #append>
            <el-button @click="handleHouseSearch">搜索</el-button>
          </template>
        </el-input>
      </div>

      <div class="house-list" v-loading="houseLoading">
        <el-table
          :data="houseList"
          style="width: 100%; margin-top: 15px"
          height="300"
          border
          highlight-current-row
          @row-click="handleSelectHouse"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="楼盘名称" min-width="180" show-overflow-tooltip />
          <el-table-column prop="develop_brand" label="开发商" width="120" show-overflow-tooltip />
          <el-table-column prop="region_name" label="区域" width="100" />
        </el-table>
      </div>

      <div class="selected-house" v-if="selectedHouse.id">
        <div class="title">已选择楼盘</div>
        <div class="content">
          <span class="name">{{ selectedHouse.name }}</span>
          <span class="id">(ID: {{ selectedHouse.id }})</span>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="bindDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleCreateHouse">新建楼盘</el-button>
          <el-button type="success" @click="confirmBindHouse" :disabled="!selectedHouse.id"
            >确认绑定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request.js'
import { useRouter } from 'vue-router'

export default {
  name: 'Plot',
  setup() {
    const router = useRouter()
    // 响应式变量
    const loading = ref(false)
    const tableData = ref([])
    const total = ref(0)
    const queryRef = ref(null)

    // 字典数据
    const dictData = ref({
      status: [], // 交易状况
      land_use: [] // 土地用途
    })

    // 查询参数
    const query = reactive({
      page: 1,
      limit: 10,
      id: '',
      plot_name: '',
      batch_id: '',
      region_id: '',
      status: '',
      create_time: []
    })
    // 批次选项
    const batchOptions = ref([])

    // 区域数据和属性
    const regionData = ref([])
    const regionProps = {
      value: 'region_id',
      label: 'region_name',
      children: 'children',
      checkStrictly: true,
      emitPath: false
    }

    // 绑定楼盘相关变量
    const bindDialogVisible = ref(false)
    const currentPlotId = ref(null)
    const searchHouseName = ref('')
    const houseList = ref([])
    const houseLoading = ref(false)
    const selectedHouse = ref({})

    // 获取批次标签
    const getBatchLabel = (batchId) => {
      const batch = batchOptions.value.find((item) => item.value === batchId)
      return batch ? batch.label : '--'
    }

    // 获取字典标签
    const getDictLabel = (dict, value) => {
      if (!Array.isArray(dict)) return '--'
      const item = dict.find((item) => item.value === value)
      return item ? item.label : '--'
    }

    // 获取地块列表
    const getList = async () => {
      loading.value = true
      try {
        // 处理查询参数
        const params = { ...query }
        if (params.create_time?.length === 2) {
          params.start_time = params.create_time[0]
          params.end_time = params.create_time[1]
        }
        delete params.create_time

        const res = await request({
          url: '/admin/house.Plot/list',
          method: 'get',
          params
        })

        if (res.code === 200) {
          tableData.value = res.data.list || []
          total.value = res.data.count || 0
        } else {
          ElMessage.error(res.msg || '获取数据失败')
          tableData.value = []
          total.value = 0
        }
      } catch (error) {
        console.error('获取列表失败:', error)
        ElMessage.error('获取列表失败')
        tableData.value = []
        total.value = 0
      } finally {
        loading.value = false
      }
    }

    // 获取批次列表
    const getBatchList = async () => {
      try {
        const res = await request({
          url: '/admin/house.PlotBatch/list',
          method: 'get',
          params: {
            page: 1,
            limit: 100
          }
        })

        if (res.code === 200 && res.data.list) {
          batchOptions.value = res.data.list.map((item) => ({
            value: item.id,
            label: `${item.year}${item.batch}`
          }))
        } else {
          ElMessage.error(res.msg || '获取批次列表失败')
        }
      } catch (error) {
        console.error('获取批次列表失败:', error)
        ElMessage.error('获取批次列表失败')
      }
    }

    // 获取区域数据
    const getRegionData = async () => {
      try {
        const res = await request({
          url: '/admin/house.Plot/dict',
          method: 'get'
        })

        if (res.code === 200) {
          regionData.value = res.data.region || []
        } else {
          ElMessage.error(res.msg || '获取区域数据失败')
        }
      } catch (error) {
        console.error('获取区域数据失败:', error)
        ElMessage.error('获取区域数据失败')
      }
    }

    // 获取字典数据
    const getDictData = async () => {
      try {
        const res = await request({
          url: '/admin/house.Plot/dict',
          method: 'get'
        })

        if (res.code === 200) {
          // 转换交易状况数据结构，使用 val 作为 value
          const plotTradingStatus = (res.data.plotTradingStatus || []).map((item) => ({
            value: parseInt(item.val), // 确保转换为数字
            label: item.name
          }))
          dictData.value = {
            ...res.data,
            plotTradingStatus
          }
        } else {
          ElMessage.error(res.msg || '获取字典数据失败')
        }
      } catch (error) {
        console.error('获取字典数据失败:', error)
        ElMessage.error('获取字典数据失败')
      }
    }

    // 添加地块
    const handleAdd = () => {
      router.push({
        path: '/plot/edit',
        query: { id: '0' }
      })
    }

    // 编辑地块
    const handleEdit = (row) => {
      router.push({
        path: '/plot/edit',
        query: { id: row.id }
      })
    }

    // 查看详情
    const handleDetail = (row) => {
      window.open(`#/plot/detail?id=${row.id}`, '_blank')
    }

    // 搜索处理
    const handleQuery = () => {
      query.page = 1
      getList()
    }

    // 重置查询
    const resetQuery = () => {
      queryRef.value?.resetFields()
      query.page = 1
      query.limit = 10
      query.id = ''
      query.plot_name = ''
      query.batch_id = ''
      query.region_id = ''
      query.status = ''
      query.create_time = []
      getList()
    }

    // 删除地块
    const handleDelete = (row) => {
      ElMessageBox.confirm(`确定要删除地块"${row.plot_name}"吗？此操作不可逆！`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          try {
            const res = await request({
              url: '/admin/house.Plot/del',
              method: 'get',
              params: { id: row.id }
            })

            if (res.code === 200) {
              ElMessage.success('删除成功')
              getList()
            } else {
              ElMessage.error(res.msg || '删除失败')
            }
          } catch (error) {
            console.error('删除失败:', error)
            ElMessage.error('删除失败')
          }
        })
        .catch(() => {})
    }

    // 发布状态变更
    const handlePublishChange = (row) => {
      const currentStatus = Number(row.status)
      const newStatus = currentStatus === 1 ? 2 : 1
      const actionText = newStatus === 1 ? '上架' : '下架'

      ElMessageBox.confirm(`确定要${actionText}地块"${row.plot_name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          try {
            const res = await request({
              url: '/admin/house.Plot/changeStatus',
              method: 'get',
              params: { id: row.id, status: newStatus }
            })

            if (res.code === 200) {
              ElMessage.success(`${actionText}成功`)
              getList()
            } else {
              ElMessage.error(res.msg || `${actionText}失败`)
            }
          } catch (error) {
            console.error('状态更改失败:', error)
            ElMessage.error('操作失败')
          }
        })
        .catch(() => {})
    }

    // 绑定楼盘处理
    const handleBindHouse = (row) => {
      currentPlotId.value = row.id
      bindDialogVisible.value = true
      searchHouseName.value = ''
      houseList.value = []
      selectedHouse.value = {}
    }

    // 楼盘搜索
    const handleHouseSearch = async () => {
      if (!searchHouseName.value.trim() && searchHouseName.value !== '') {
        return
      }

      houseLoading.value = true
      try {
        const res = await request({
          url: '/admin/house.Index/index',
          method: 'get',
          params: {
            name: searchHouseName.value,
            not_bind_plot: 1,
            is_land: [0]
          }
        })

        if (res.code === 200) {
          houseList.value = res.data.list || []
        } else {
          ElMessage.error(res.msg || '获取楼盘列表失败')
          houseList.value = []
        }
      } catch (error) {
        console.error('获取楼盘列表失败:', error)
        ElMessage.error('获取楼盘列表失败')
        houseList.value = []
      } finally {
        houseLoading.value = false
      }
    }

    // 选择楼盘
    const handleSelectHouse = (row) => {
      selectedHouse.value = row
    }

    // 确认绑定楼盘
    const confirmBindHouse = async () => {
      if (!selectedHouse.value.id) {
        ElMessage.warning('请先选择楼盘')
        return
      }

      try {
        const res = await request({
          url: '/admin/house.Plot/bindHouse',
          method: 'post',
          data: {
            id: currentPlotId.value,
            house_id: selectedHouse.value.id
          }
        })

        if (res.code === 200) {
          ElMessage.success('绑定楼盘成功')
          bindDialogVisible.value = false
          getList() // 刷新列表
        } else {
          ElMessage.error(res.msg || '绑定楼盘失败')
        }
      } catch (error) {
        console.error('绑定楼盘失败:', error)
        ElMessage.error('绑定楼盘失败')
      }
    }

    const handleCopy = (row) => {
      ElMessageBox.confirm(`确定要复制地块"${row.plot_name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      })
        .then(async () => {
          try {
            const res = await request({
              url: '/admin/house.Plot/copy',
              method: 'get',
              params: { id: row.id }
            })

            if (res.code === 200) {
              ElMessage.success('复制成功，3秒后跳转到编辑页面...')
              // 等待3秒后跳转到编辑页面
              setTimeout(() => {
                router.push('/plot/edit?id=' + res.data)
              }, 3000)
            } else {
              ElMessage.error(res.msg || '复制失败')
            }
          } catch (error) {
            console.error('复制失败:', error)
            ElMessage.error('复制失败')
          }
        })
        .catch(() => {})
    }

    // 新建楼盘
    const handleCreateHouse = async () => {
      try {
        const res = await request({
          url: '/admin/house.Plot/createHouse',
          method: 'post',
          data: {
            id: currentPlotId.value
          }
        })

        if (res.code === 200) {
          ElMessage.success('新建楼盘成功，3秒后跳转...')
          bindDialogVisible.value = false
          // 等待3秒后跳转到楼盘编辑页面
          setTimeout(() => {
            router.push('/house/info?id=' + res.data)
          }, 3000)
        } else {
          ElMessage.error(res.msg || '新建楼盘失败')
        }
      } catch (error) {
        console.error('新建楼盘失败:', error)
        ElMessage.error('新建楼盘失败')
      }
    }

    // 分页大小变化
    const handleSizeChange = (val) => {
      query.limit = val
      getList()
    }

    // 当前页变化
    const handleCurrentChange = (val) => {
      query.page = val
      getList()
    }

    // 刷新列表
    const refreshList = () => {
      getList()
    }

    // 生命周期钩子
    onMounted(() => {
      getBatchList()
      getRegionData()
      getDictData()
      getList()
    })

    return {
      loading,
      tableData,
      total,
      query,
      queryRef,
      batchOptions,
      regionData,
      regionProps,
      dictData,
      getBatchLabel,
      getDictLabel,
      getList,
      handleQuery,
      resetQuery,
      handleAdd,
      handleEdit,
      handleDetail,
      handleDelete,
      handlePublishChange,
      handleSizeChange,
      handleCurrentChange,
      refreshList,
      // 绑定楼盘相关
      bindDialogVisible,
      currentPlotId,
      searchHouseName,
      houseList,
      houseLoading,
      selectedHouse,
      handleBindHouse,
      handleHouseSearch,
      handleSelectHouse,
      confirmBindHouse,
      handleCreateHouse,
      handleCopy
    }
  },
  methods: {
    // 导入，results数据，header表头
    imports({ results, header }) {
      this.loading = true
      request({
        url: '/admin/house.Plot/import',
        method: 'post',
        data: {
          import: results
        }
      })
        .then((res) => {
          if (res.code === 200) {
            ElMessage.success(res.msg || '导入成功')
            this.getList() // 刷新列表数据
          } else {
            // 服务器可能返回code不为200的情况
            ElMessage.error(res.msg || '导入失败')
          }
        })
        .catch((error) => {
          console.error('导入失败:', error)
          ElMessage.error('导入失败')
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.operation-btns {
  margin-top: 10px;
  margin-bottom: 10px;
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

// 绑定楼盘弹窗样式
.search-box {
  margin-bottom: 15px;
}

.selected-house {
  margin-top: 20px;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f5f7fa;

  .title {
    font-size: 14px;
    color: #606266;
    margin-bottom: 8px;
  }

  .content {
    font-size: 14px;

    .name {
      font-weight: bold;
      color: #303133;
    }

    .id {
      margin-left: 8px;
      color: #909399;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
