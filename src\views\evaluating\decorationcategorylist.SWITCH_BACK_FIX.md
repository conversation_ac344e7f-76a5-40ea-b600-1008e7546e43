# 位置切回原位置时类型回显修复说明

## 问题描述

当位置切回之前的位置时，类型未能正确回显。

**测试场景：**
1. 编辑客厅-吊顶 ✅
2. 切换到厨房（吊顶不存在，被清空）✅
3. 切回客厅（吊顶存在，但没有恢复选择）❌

## 问题原因

原来的逻辑只检查当前 `form.category_id` 的值，但在步骤2中这个值已经被清空了。当切回原位置时，由于 `currentCategoryId` 为空，所以没有进行任何处理。

```javascript
// 问题代码
const currentCategoryId = form.value.category_id; // 已经被清空，为 ''

if (currentCategoryId) { // 条件为 false，不执行
  // 检查和设置逻辑
}
```

## 修复方案

### 1. 增强目标类型ID的获取逻辑

```javascript
// 修复后的逻辑
let targetCategoryId = currentCategoryId;
if (!targetCategoryId && editingRowData.value && !isCreateMode.value) {
  targetCategoryId = editingRowData.value.category_id;
  console.log('当前类型为空，使用原始编辑数据的类型ID:', targetCategoryId);
}
```

**逻辑说明：**
- 优先使用当前选中的类型ID
- 如果当前类型ID为空，且在编辑模式下，则使用原始编辑数据的类型ID
- 这样就能在切回原位置时恢复原来的类型选择

### 2. 修复 handleEdit 中的错误

发现并修复了一个bug：
```javascript
// 错误代码
form.value.category_id = row.id; // 应该是 row.category_id

// 修复后
form.value.category_id = row.category_id;
```

### 3. 统一 watch 监听器的逻辑

让 `watch` 监听器使用与 `handlePositionChange` 相同的逻辑：

```javascript
watch(categories, (newCategories) => {
  // 使用与 handlePositionChange 相同的逻辑
  const currentCategoryId = form.value.category_id;
  let targetCategoryId = currentCategoryId;
  if (!targetCategoryId && editingRowData.value) {
    targetCategoryId = editingRowData.value.category_id;
  }
  
  // 检查和设置逻辑...
})
```

## 执行流程

### 修复后的完整流程

**步骤1：编辑客厅-吊顶**
```
1. handleEdit 执行
2. editingRowData.value = {position_id: 1, category_id: 3, ...}
3. 加载客厅的类型数据 [地板, 墙面, 吊顶]
4. form.category_id = 3 (吊顶)
✅ 显示：客厅-吊顶
```

**步骤2：切换到厨房**
```
1. handlePositionChange(厨房ID) 执行
2. currentCategoryId = 3 (吊顶)
3. 加载厨房的类型数据 [墙面, 橱柜, 台面]
4. 检查吊顶是否存在：不存在
5. form.category_id = '' (清空)
✅ 显示：厨房-（空）
```

**步骤3：切回客厅**
```
1. handlePositionChange(客厅ID) 执行
2. currentCategoryId = '' (已被清空)
3. targetCategoryId = editingRowData.value.category_id = 3 (使用原始数据)
4. 加载客厅的类型数据 [地板, 墙面, 吊顶]
5. 检查吊顶是否存在：存在
6. form.category_id = 3 (恢复吊顶)
✅ 显示：客厅-吊顶
```

## 调试信息

修复后的调试输出：

```
=== 位置改变开始 ===
新位置ID: 1 当前模式: 编辑
改变前的类型ID: 
当前类型为空，使用原始编辑数据的类型ID: 3
目标类型ID: 3 (当前: , 原始: 3)
开始加载类型数据，position_id: 1
类型数据加载完成: [{id: 1, name: "地板"}, {id: 2, name: "墙面"}, {id: 3, name: "吊顶"}]
目标类型是否存在于新列表中: {id: 3, name: "吊顶"}
✅ 设置类型选择: 3
=== 位置改变结束 ===
最终类型ID: 3
```

## 测试用例

### 测试1：切回原位置（类型存在）
1. 编辑客厅-吊顶
2. 切换到厨房（清空）
3. 切回客厅
**预期结果：** 恢复显示吊顶

### 测试2：切回原位置（类型不存在）
1. 编辑客厅-特殊类型（假设只在客厅存在）
2. 切换到卧室（清空）
3. 删除客厅的特殊类型
4. 切回客厅
**预期结果：** 保持清空状态

### 测试3：多次切换
1. 编辑客厅-地板
2. 切换到卧室（保持地板）
3. 切换到厨房（清空）
4. 切回卧室（恢复地板）
5. 切回客厅（恢复地板）
**预期结果：** 每次切换都正确处理

## 关键改进点

1. **智能目标类型获取**：优先使用当前值，回退到原始值
2. **修复数据错误**：`row.id` → `row.category_id`
3. **逻辑统一**：`handlePositionChange` 和 `watch` 使用相同逻辑
4. **冲突避免**：使用标志防止处理冲突
5. **调试友好**：详细的日志输出

## 验收标准

✅ 首次编辑回显正常
✅ 切换到不包含类型的位置时清空
✅ 切回原位置时正确恢复类型选择
✅ 多次切换都能正确处理
✅ 新建模式不受影响
✅ 调试信息清晰完整
