<template>
  <div class="filter-box">
    <el-form :model="form" ref="formRef" label-width="70" class="search-form" inline>
      <el-form-item label="用户昵称" prop="nickname">
        <el-input v-model="form.nickname" placeholder="请输入用户昵称"></el-input>
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="form.phone" placeholder="请输入手机号"></el-input>
      </el-form-item>

      <!-- 商品价格 -->
      <el-form-item label="创建时间" prop="date_value">
        <el-date-picker
            v-model="form.date_value"
            type="datetimerange"
            class="ya-date-value"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD HH:mm:ss"
            :default-time="[new Date(2024, 1, 1, 0, 0, 0), new Date(2024, 1, 1, 23, 59, 59)]"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleChangeFilter">查询</el-button>
      </el-form-item>

      <el-form-item>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {defineComponent, reactive, ref} from 'vue'

export default defineComponent({
  emits: ['onSearch'],
  setup(props, {emit}) {
    const formRef = ref(null)
    const form = reactive({
      nickname: '',
      id: '',
      role: 5,
      type: '',
      date_value: ['', '']
    })
    const handleReset = () => {
      form.type = '';
      formRef.value.resetFields()
    }
    const handleChangeFilter = () => {
      emit('onSearch', {...form})
    }

    return {
      form,
      formRef,
      handleReset,
      handleChangeFilter
    }
  }
})
</script>

<style lang="scss" scoped>
.filter-box {
  padding: 20px 20px 10px 20px;
  border-bottom: 1px solid #eee;

  .search-form {
    :deep(.el-input) {
      width: 216px;
    }

    :deep(.el-select) {
      width: 216px;
    }
  }
}
</style>
