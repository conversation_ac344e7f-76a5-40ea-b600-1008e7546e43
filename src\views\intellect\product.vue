<template>
      <div class="app-container">

        <el-form  ref="queryRef" :model="query" :inline="true" label-width="90px">
        <el-form-item label="产品名称：" prop="name">
          <el-input v-model="query.name" maxlength="20" placeholder="请输入产品名称" style="width: 140px" />

        </el-form-item>
        <el-form-item label="智能品类：">
        <el-select
          v-model="query.class_id"
          class="w-full ya-search-value"
          clearable
          multiple 
          filterable
          remote
          :remote-method="getClasslist"
          placeholder="请选择品类"
          :loading="loading"
        >
          <el-option
            v-for="item in classListData"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
        <el-form-item label="发布状态：" prop="release_status">
          <el-select v-model="query.release_status" style="width: 120px">
            <el-option value="0" label="全部" />
            <el-option value="2" label="已发布" />
            <el-option value="1" label="已下架" />
          </el-select>
        </el-form-item>
        
        <!-- 时间 -->
        <el-form-item>
        <el-select v-model="query.date_field" class="ya-date-field" placeholder="时间类型">
          <el-option value="" label="请选择" />
          <el-option value="create_time" label="创建时间" />
          <el-option value="update_time" label="更新时间" />
        </el-select>
        <el-date-picker
          v-model="query.date_value"
          type="datetimerange"
          class="ya-date-value"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="[new Date(2024, 1, 1, 0, 0, 0), new Date(2024, 1, 1, 23, 59, 59)]"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" title="搜索" @click="handleQuery">搜索</el-button>
        <el-button  @click="resetQuery">重置</el-button>
		<el-button type="primary" @click="add()">添加</el-button>
      </el-form-item>
       
        
      </el-form>
      <!-- 搜索end -->
    <el-table
    ref="table"
    v-loading="loading"
    :data="data"
    :height="height"
    @sort-change="sort"
    @selection-change="select"
  >
    <el-table-column type="selection" width="42" title="全选/反选" />
    <el-table-column :prop="idkey" label="ID" width="80" sortable="custom" />
    <el-table-column prop="name" label="产品名称" min-width="150" />
    <el-table-column prop="calc_type" label="产品品类" min-width="100" show-overflow-tooltip >
		<template #default="scope">
          <span v-text="scope.row.classInfo.name"></span>
        </template>
      </el-table-column>
    <el-table-column prop="sort" label="排序" min-width="100" show-overflow-tooltip  sortable="custom" />
   
    <el-table-column prop="release_status" label="发布状态" min-width="110" show-overflow-tooltip  sortable="custom" >
      <template #default="scope">
        <el-switch
                v-model="scope.row.release_status"
                :active-value="1"
                :inactive-value="0"
                @change="release(scope.row)"

              />
      </template>
    </el-table-column>
    <el-table-column prop="update_time" label="更新时间" min-width="165" sortable="custom" />
    <el-table-column label="操作" width="95">
        <template #default="scope">
        
		<el-link type="primary" class="mr-1" :underline="false" @click="edit(scope.row)">
            编辑
          </el-link>

		<el-link v-if="scope.row.release_status == 0" type="primary" class="mr-1" :underline="false" @click="deleteInfo(scope.row)">
            删除
          </el-link>
        
      </template>
    </el-table-column>
  </el-table>
  <!-- 分页 -->
  <pagination
    v-show="count > 0"
    v-model:total="count"
    v-model:page="query.page"
    v-model:limit="query.limit"
    @pagination="infoList"
  />

<!-- 添加修改 -->
<el-dialog
      v-model="dialog"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="cancel"
      top="5vh"
    >
      <el-form ref="ref" :rules="rules" :model="model" label-width="100px">
        <el-tabs>
          <el-tab-pane label="信息">
            <el-scrollbar native :height="height - 80">
              
              <el-form-item label="产品名称" prop="name">
                <el-input v-model="model.name" placeholder="请输入产品名称"  />
              </el-form-item>
             
              <el-form-item label="所属品类" prop="class_id" placeholder="请选择品类">
                    <el-select
                      v-model="model.class_id"
                      class="w-full"
                      clearable
                      filterable
                      remote
                      placeholder="请选择品类"
                      :remote-method="getClasslist"
                      :loading="loading"
                      @change="classChange"
                      :disabled="model.id"
                    >
                      <el-option
                        v-for="item in classListData"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
              <el-form-item label="排序" prop="sort">
                <el-input v-model="model.sort" type="number" placeholder="sort" clearable />
              </el-form-item>
              
            </el-scrollbar>
          </el-tab-pane>
         
        </el-tabs>
      </el-form>
      <template #footer>
        <el-button :loading="loading" @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="editInfos">提交</el-button>
      </template>
    </el-dialog>
</div>
</template>
<script setup>
import {ref, onMounted} from "vue"
import checkPermission from '@/utils/permission';
import screenHeight from '@/utils/screen-height'
import Pagination from '@/components/Pagination/index.vue'
import { getPageLimit } from '@/utils/settings'
import { queryList,info as infoDetail,editSubmit,releaseStatusChange,deleteInfo as deleInfo } from '@/api/intellect/product'
import { queryList as classList } from '@/api/intellect/productclass'

const classListData = ref([])


const model = ref({
	'id': 0,
	'name': '',
	'class_id': 1,
	'sort': 0

});

const originModel = {
	'id': 0,
	'name': '',
	'class_id': 0,
	'sort': 0

};

const selectTitle = ref('操作')
const selectDialog = ref(false)
const selectType = ref('')

const rowInfo = ref({});

const loading = ref(true);
const data = ref([])
const height = ref(320)
const query = ref({
        page: 1,
        limit: getPageLimit(),
		// sort_field: 'update_time',
		// sort_value: 'desc',

      });
const count = ref(0);
const idkey = ref('id');

const dialog = ref(false);
const dialogTitle = ref('');
const selectNames = ref('')
const selectIds = ref('')
// 根据楼盘名称获取楼盘列表
const getClasslist = (keywords) => {
  classList({ name: keywords })
    .then((res) => {
      classListData.value = res.data.list
    })
    .catch((error) => {})
}
const selectAlert = () => {
  ElMessageBox.alert('请选择需要操作的' + name.value, '提示', {
    type: 'warning',
    callback: () => {}
  })
}

const deleteInfo = ( row ) => {
	ElMessageBox.confirm('确定要删除数据吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(() => {
		// 确认操作逻辑
		deleInfo({id: row.id}).then((res) => {
			infoList()
		})

	}).catch(() => { 
		/* 取消操作 */ 
	})

}

function release(row) {
	
		let dataEdit = {}
		dataEdit['id'] = row.id
		dataEdit['release_status'] = row.release_status == 0 ? 1 : 0
		releaseStatusChange(dataEdit).then((res) => {
			 ElMessage.success(res.msg)
			infoList()
		}).catch(() => {
			ElMessage.error(res.msg)
		})

}
function infoList(){
    loading.value = true;
    queryList(query.value).then((res) => {
          data.value = res.data.list
          count.value = res.data.count
          loading.value = false
        })
        .catch(error => {
          console.log(error);
          loading.value = false
        })

}
function add() {
  loading.value = true;
  dialog.value = true
  dialogTitle.value =  '新增品类' 
  loading.value = false

}

function edit(row) {

  rowInfo.value = row
  loading.value = true;
  dialog.value = true
  dialogTitle.value = row.name + '意见详情：' + row[idkey.value]
  var id = {}
  id[idkey.value] = row[idkey.value]
  infoDetail(id)
    .then((res) => {
      reset(res.data)
    })
    .catch(() => {})
  loading.value = false

}
function editInfos() {
  loading.value = true;
  // 验证
  let dataEdit = {}
  
  dataEdit['id'] = model.value.id
  dataEdit['name'] = model.value.name
  dataEdit['class_id'] = model.value.class_id
  dataEdit['sort'] = model.value.sort

	editSubmit(dataEdit).then((res) => {
		infoList()
		dialog.value = false
	})
	.catch(() => {
		loading.value = false
	})	
  reset()

  loading.value = false
}

// 排序
function sort(sort) {
      query.value.sort_field = sort.prop
      query.value.sort_value = ''
      if (sort.order === 'ascending') {
        query.value.sort_value = 'asc'
        infoList()
      }
      if (sort.order === 'descending') {
        query.value.sort_value = 'desc'
        infoList()
      }
}
// 操作
function select(selection) {
      this.selection = selection
      this.selectIds = this.selectGetIds(selection).toString()
    }

function cancel() {
  dialog.value = false
  reset()    
}
//搜索
const queryRef = ref(null);
function resetQuery(){
  query.value.release_status = '0';
  query.value.date_field = '';
  query.value.date_value[0] = '';
  query.value.date_value[1] = '';
  queryRef.value.resetFields();
  handleQuery();

}
function handleQuery() {
  query.value.pageNum = 1
  infoList()
}
//搜索end
// 重置
function reset(row) {
  if (row) {
    model.value = row
  } else {
    model.value = originModel
  }
}
onMounted(()=>{
    height.value = screenHeight(310)
    infoList()
    classList({'page':1,'limit':10}).then((res) => {

      classListData.value = res.data.list
    })

})

</script>