import request from '@/utils/request'
// 项目列表
const url = '/admin/house.Index/'

/**
 * 项目列表
 * @param {array} params 请求参数
 */
export function houseList(params) {
  return request({
    url: url + 'index',
    method: 'get',
    params: params
  })
}

/**
 * 修改楼盘信息
 * @param {array} data 请求数据
 */
export function editHouse(data) {
  return request({
    url: url + 'edit',
    method: 'post',
    data
  })
}
// /**
//  *
//  *获取楼盘下拉数据
//  * @param {array} data 请求数据
//  */
// export function getHouseSelectAllList(data) {
//   return request({
//     url: url + 'getHouseSelectAllList',
//     method: 'post',
//     data
//   })
// }
/**
 * 楼盘发布
 * @param {array} data 请求数据
 */
export function releaseHouse(data) {
  return request({
    url: url + 'release',
    method: 'post',
    data
  })
}
/**
 * 获取楼盘信息
 * @param {array} params 请求数据
 */
export function info(params) {
  return request({
    url: url + 'info',
    method: 'get',
    params: params
  })
}

/**
 * 获取搜索参数
 * @param {array} params 请求数据
 */
export function searchparams(params) {
  return request({
    url: url + 'searchparams',
    method: 'get',
    params: params
  })
}

/**
 * 楼盘数据导入
 * @param {array} data 请求数据
 */
export function houseexport(data) {
  return request({
    url: url + 'houseexport',
    method: 'post',
    data
  })
}

/**
 * 完整项目列表
 * @param {array} params 请求参数
 */
export function getHouseSelectAllList(params) {
  return request({
    url: url + 'getHouseSelectAllList',
    method: 'get',
    params: params
  })
}
