<template>
    <div class="app-container">
      <el-form ref="queryRef" :model="query" :inline="true" label-width="90px">
        <el-form-item label="推广位名称：" prop="name">
          <el-input
            v-model="query.name"
            maxlength="20"
            placeholder="请输入名称"
            style="width: 140px"
          />
        </el-form-item>
  
        <el-form-item>
          <el-button type="primary" title="搜索" @click="handleQuery">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
  
          <el-button @click="handleAdd">添加广告位</el-button>
        </el-form-item>
      </el-form>
      <!-- 搜索end -->
  
      <el-table ref="table" v-loading="loading" :data="data" :height="600">
        <el-table-column fixed prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="广告位名称" min-width="150" show-overflow-tooltip />
        <el-table-column  label="广告位状态" min-width="150"  >
            <template #default="{ row }">
                <el-text v-if="row.state == 1">有效</el-text>
                <el-text v-else-if="row.state == 2">无效</el-text>
                <el-text v-else>--</el-text>
            </template>
        </el-table-column>
        <el-table-column
          prop="create_time"
          label="创建时间"
          min-width="150"
        />
        <el-table-column
          prop="update_time"
          label="更新时间"
          min-width="150"
        />
       
        <el-table-column fixed="right" label="操作" width="125">
          <template #default="scope">
            <el-link type="primary" class="mr-1" :underline="false" @click="handleEdit(scope.row)">
              编辑
            </el-link>
            <el-link type="primary" class="mr-1" :underline="false" @click="handleViewSup(scope.row)">
              添加广告
            </el-link>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <pagination
        v-show="count > 0"
        v-model:total="count"
        v-model:page="query.page"
        v-model:limit="query.limit"
        @pagination="getList"
      />
      <el-dialog
      v-model="dialogshow"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="dialogcancel"
      width="30%"
      top="5vh"
    >
     
      
        <el-form ref="ref" :model="model"  label-width="100px">

          <el-form-item label="广告位名称" prop="name">
                <el-input
                  key="name"
                  v-model="model.name"
                  placeholder="请输入名称"
                  clearable
                />
          </el-form-item>
          <el-form-item label="广告位标识" prop="siteflag">
                <el-input
                  key="siteflag"
                  v-model="model.siteflag"
                  placeholder="请输入标识名称"
                  clearable
                />
          </el-form-item>

          <el-form-item label="是否有效" prop="state">
            <el-radio-group v-model="model.state">
            <el-radio :value=1>有效</el-radio>
            <el-radio :value=2>无效</el-radio>
          </el-radio-group>
          </el-form-item>

        </el-form>
        <template #footer>
        <el-button :loading="loading" @click="dialogcancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submit">提交</el-button>
      </template>
    </el-dialog>
      
    </div>
  </template>
  <script setup>
  import { getPageLimit } from '@/utils/settings'
  import {list,add,show,edit}  from '@/api/advertising/advertisingsite'
import { useRouter } from 'vue-router'
  import Pagination from '@/components/Pagination/index.vue'

  import { ref } from 'vue'
  const router = useRouter()
  const loading = ref(true)
  const dialogshow = ref(false)
  const dialogTitle = ref("添加广告")
  const data = ref([])
  const resetmodel = ref({name:'',siteflag:'',state:1});
  const model = ref({name:'',siteflag:'',state:1});
  const query = ref({
    page: 1,
    limit: getPageLimit()
    
  })
  
  const count = ref(0)
  
  const queryRef = ref(null)
  
  
  const getList = async () => {
    loading.value = true
    try {
      const res = await list(query.value)
      data.value = res.data.list
      count.value = res.data.count
    } finally {
      loading.value = false
    }
  }
  const dialogcancel = ()=>{
    dialogshow.value = false;
  }
  const resetQuery = () => {
    
    queryRef.value.resetFields()
    handleQuery()
  }
  
  const handleQuery = () => {
    query.value.pageNum = 1
    getList()
  }
  
  const handleAdd = () => {
    model.value = resetmodel.value
    dialogshow.value = true
  }
  
  const handleEdit = async (row) => {
    
    const res = await show({id:row.id})
    model.value = res.data;
    dialogshow.value = true

  }
  
const submit = async ()=>{
  loading.value = true;
  try{
    if(model.value.id){
    const res = await edit(model.value)
    ElMessage.success(res.msg)
    loading.value = false;
  }else{
    const res = await add(model.value)
    ElMessage.success(res.msg)
    loading.value = false;
  }
  dialogshow.value = false
  } finally {
    loading.value = true;
  }
  
  
  getList();

}


  

  const handleViewSup = (row) => {
  router.push({
    path: '/advertising/advertisinglist',
    query: { site_id:row.id }
  })
}
  onMounted(getList)
  </script>
  
  <style lang="scss" scoped></style>
  