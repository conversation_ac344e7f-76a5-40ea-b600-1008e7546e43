<template>
  <el-scrollbar native class="wrapper">
    <el-form :model="model" ref="houseBase" :rules="houseBaseRules" label-width="150px">
      <el-row>
        <el-col :span="11">
          <el-card style="height: 100%" header="基础信息" shadow="hover">
            <el-form-item label="类型" v-if="is_show_new == 1" prop="is_land">
              <el-radio-group
                v-model="model.is_land"
                style="margin-left: 10px"
                @change="houseTypeChange"
              >
                <el-radio label="楼盘" :value="0"></el-radio>
                <el-radio label="地块" :value="1"></el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="宗地编号" prop="name">
              <el-input v-model="model.name" placeholder="输入输入宗地编号" />
            </el-form-item>
            <el-form-item label="销售状态" prop="project_status">
              <el-radio-group v-model="model.project_status" style="margin-left: 10px">
                <el-radio
                  v-for="(value, key) in params_data['houseSalestate']"
                  :key="key"
                  :label="value"
                  :value="key"
                ></el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="项目公司" prop="company">
              <el-select v-model="model.company" class="w-full" multiple clearable filterable>
                <el-option
                  v-for="item in params_data['companys']"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="品牌名称" prop="brand">
              <el-select v-model="model.brand" class="w-full" multiple clearable filterable>
                <el-option
                  v-for="item in params_data['brands']"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="特色标签" prop="feature">
              <el-select v-model="model.feature" class="w-full" multiple clearable filterable>
                <el-option
                  v-for="item in params_data['peitaoFeature']"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-divider>
              <h3 class="bold">价格信息</h3>
            </el-divider>
            <el-form-item label="均价" prop="unit_price">
              <el-col :span="landSpanLenth">
                <el-input v-model="model.unit_price" type="number" placeholder="输入均价">
                  <template #append>元/㎡</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-divider>
              <h3 class="bold">项目建筑信息</h3>
            </el-divider>

            <el-form-item label="物业类型" prop="property_type">
              <el-checkbox-group v-model="model.property_type">
                <el-checkbox
                  v-for="(value, key) in params_data['propertyType']"
                  :label="value"
                  :value="key"
                  :key="key"
                />
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="装修情况" prop="decoration_situation">
              <el-input v-model="model.decoration_situation" placeholder="输入装修情况" />
            </el-form-item>

            <el-divider>
              <h3 class="bold">项目体量信息</h3>
            </el-divider>
            <el-form-item label="占地面积" prop="occupation_area">
              <el-col :span="landSpanLenth">
                <el-input v-model="model.occupation_area" type="number" placeholder="输入占地面积">
                  <template #append>㎡</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="建筑面积" prop="building_area">
              <el-col :span="landSpanLenth">
                <el-input v-model="model.building_area" type="number" placeholder="输入建筑面积">
                  <template #append>㎡</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="容积率" prop="area_ratio">
              <el-col :span="landSpanLenth">
                <el-input v-model="model.area_ratio" type="number" placeholder="输入容积率">
                  <template #append>%</template>
                </el-input>
              </el-col>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :offset="1" :span="12">
          <el-card header="项目位置信息" shadow="hover" style="height: 100%">
            <el-form-item label="楼盘地址" prop="build_address">
              <el-input v-model="model.build_address" />

              <el-col :span="3" :offset="1">
                <el-button :loading="searchLoading" @click="handleGetLng">获取坐标</el-button>
              </el-col>
            </el-form-item>
            <el-form-item label="楼盘坐标" prop="login_bg_id">
              <div id="mapContaniner"></div>
            </el-form-item>
            <el-form-item label="经度" prop="longitude">
              <el-col :span="8">
                <el-input readonly v-model="model.longitude" type="number" placeholder="输入经度">
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="纬度" prop="latitude">
              <el-col :span="8">
                <el-input readonly v-model="model.latitude" type="number" placeholder="输入纬度">
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="区域板块" prop="region_id" class="region_plate">
              <el-col :span="4">
                <el-cascader
                  v-model="model.region_id"
                  :options="regionData"
                  :props="regionProps"
                  @change="changeRegion"
                  class="w-full"
                  clearable
                  filterable
                  placeholder="选择区域"
                />
              </el-col>
              <el-col :span="4" :offset="1">
                <el-cascader
                  v-model="model.plate_id"
                  :options="plateData"
                  :props="plateProps"
                  @change="changePlate"
                  clearable
                  filterable
                  placeholder="请选择板块"
                />
              </el-col>
            </el-form-item>

            <!-- <el-form-item label="板块" prop="sector_name">
              <el-cascader
                v-model="model.sector_name"
                :options="sectorData"
                :props="sectorProps"
                ref="mycascader"
                @change="changeCasc"
                class="w-full"
                placeholder="请选择板块"
                clearable
                filterable
              />
            </el-form-item> -->
            <el-form-item label="售楼处地址" prop="sales_address">
              <el-col :span="12">
                <el-input v-model="model.sales_address" placeholder="输入售楼处地址" />
              </el-col>
              <el-col :span="3" :offset="1">
                <el-button :loading="salesSearchLoading" @click="handleGetSalesLng">获取坐标</el-button>
              </el-col>
            </el-form-item>
            <el-form-item label="售楼处坐标">
              <div id="salesMapContainer"></div>
            </el-form-item>

            <el-form-item label="售楼处经度" prop="sales_longitude">
              <el-input readonly v-model="model.sales_longitude" type="number" placeholder="输入经度">
              </el-input>
            </el-form-item>

            <el-form-item label="售楼处纬度" prop="sales_latitude">
              <el-input readonly v-model="model.sales_latitude" type="number" placeholder="输入纬度">
              </el-input>
            </el-form-item>
            <el-form-item label="所属环线" prop="loop_line">
              <el-radio-group v-model="model.loop_line" style="margin-left: 10px">
                <el-radio
                  v-for="(value, key) in params_data['houseLoopLine']"
                  :label="value"
                  :value="key"
                  :key="key"
                ></el-radio>
              </el-radio-group>
            </el-form-item>
            <el-divider>
              <h3 class="bold">周边配套</h3>
            </el-divider>
            <el-form-item label="学校" prop="school">
              <el-select v-model="model.school" class="w-full" multiple clearable filterable disabled>
                <el-option
                  v-for="item in params_data['peitaoSchool']"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="交通" prop="traffic">
              <el-select v-model="model.traffic" class="w-full" multiple clearable filterable disabled>
                <el-option
                  v-for="item in params_data['peitaoTraffic']"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="商场" prop="shop">
              <el-select v-model="model.shop" class="w-full" multiple clearable filterable disabled>
                <el-option
                  v-for="item in params_data['peitaoShop']"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <!-- <el-divider>
			  <h3 class="bold">地块图片</h3>
			</el-divider>
			
			<el-form-item label="地块图" prop="build_photo">
			  <ImgUploads
			    v-model="model.build_photo"
			    upload-btn="上传图片"
			    file-type="image"
			    file-tip=""
			  />
			</el-form-item> -->
            <!-- <el-form-item label="户型图" prop="unit_photo">
			  <ImgUploads
			    v-model="model.unit_photo"
			    upload-btn="上传图片"
			    file-type="image"
			    file-tip=""
			  />
			</el-form-item>
			
			<el-form-item label="图片" prop="house_photo">
			  <ImgUploads
			    v-model="model.house_photo"
			    upload-btn="上传图片"
			    file-type="image"
			    file-tip=""
			  />
			</el-form-item> -->

            <!-- <el-form-item label="视频" prop="image_url">
			  <ImgUpload
			    v-model="model.image_url"
			    :file-url="model.image_url"
			    file-type="video"
			    :height="100"
			    upload
			  />
			</el-form-item> -->
            <el-form-item label="" prop=""> </el-form-item>
          </el-card>
        </el-col>
      </el-row>

      <div class="sub-btn">
        <el-button :loading="loading" title="返回" @click="refresh()" style="margin-right: 10px"
          >返回</el-button
        >
        <el-button :loading="loading" type="primary" @click="submit()" style="margin-right: 10px"
          >提交</el-button
        >
        <el-button
          :loading="loading"
          type="primary"
          @click="submit(true)"
          style="margin-right: 10px"
          >提交并推送</el-button
        >
      </div>
    </el-form>
  </el-scrollbar>
</template>

<script>
import screenHeight from '@/utils/screen-height'
import * as Project from '@/api/project/house'
import { NULL } from 'sass'
import axios from 'axios'
import { jsonp } from '@/utils/jsonp'
import { ElMessage } from 'element-plus'
import TMap from 'TMap'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { list } from '@/api/project/plate'
const tagsViewStore = useTagsViewStore()
let mapContaniner = null
let multiMarker = null
let markerGeo = null

let salesMapContainer = null
let salesMultiMarker = null
let salesMarkerGeo = null
export default {
  name: 'HouseInfoDetail',
  props: {
    houseId: {
      type: String,
      default: '0'
    }
  },
  data() {
    return {
      name: '楼盘基础信息',
      is_show_new: 1, //
      houseType: 0, // 楼盘类型：0 楼盘 1 地块
      height: 680,
      loading: false,
      landSpanLenth: 8,
      searchLoading: false,
      model: {
        id: 0,
        is_land: 1
      },
      params_data: {},
      regionData: [],
      regionProps: {
        expandTrigger: 'hover',
        // checkStrictly: true,
        value: 'region_id',
        label: 'region_name',
        emitPath: false
      },
      plateData: [],
      plateProps: {
        expandTrigger: 'hover',
        // checkStrictly: true,
        value: 'id',
        label: 'name',
        emitPath: false
      },
      sectorData: [],
      sectorProps: {
        expandTrigger: 'hover',
        // checkStrictly: true,
        value: 'name',
        label: 'name',
        emitPath: false
      },
      houseBaseRules: {
        name: [{ required: true, message: '请填写楼盘名称', trigger: 'blur' }]
        // project_status: [{ required: true, message: '请选择销售状态', trigger: 'blur' }],
        // sales_address_status: [{ required: true, message: '请选择售楼处状态', trigger: 'blur' }],
        // property_type: [{ required: true, message: '请选择售楼处状态', trigger: 'blur' }],
        // build_type: [{ required: true, message: '请选择建筑类型', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.height = screenHeight(210)
  },
  async mounted() {
    await this.params()
    await this.info()
    this.initMap()
    this.initSalesMap()
  },
  beforeUnmount() {
    mapContaniner?.destroy()
    mapContaniner = null
    multiMarker?.remove(['center'])
    multiMarker = null
    markerGeo = null
    salesMapContainer?.destroy()
    salesMapContainer = null
    salesMultiMarker?.remove(['sales_center'])
    salesMultiMarker = null
    salesMarkerGeo = null
  },
  methods: {
    handleGetSalesLng() {
      this.salesSearchLoading = true
      const baseUrl = 'https://apis.map.qq.com'
      const url = `${baseUrl}/ws/geocoder/v1`

      jsonp(url, {
        key: 'POYBZ-JXV6Q-3KD5A-44ZNG-SGTIO-J6FDN',
        output: 'jsonp',
        address: this.model.sales_address
      })
          .then((res) => {
            if (res.status === 0) {
              const { lng, lat } = res.result.location
              this.model.sales_latitude = lat
              this.model.sales_longitude = lng
              this.setSalesCenter(lat, lng)
              this.setSalesMarker()
              if (!this.isSalesMapEvent) {
                this.updateSalesCenter()
              }
            } else {
              ElMessage({
                message: '请填写详细售楼处地址',
                type: 'info'
              })
            }
          })
          .finally(() => {
            this.salesSearchLoading = false
          })
    },
    initSalesMap() {
      salesMapContainer = new TMap.Map(document.getElementById('salesMapContainer'), {
        zoom: 13
      })
    },

    updateSalesCenter() {
      if (!this.isSalesMapEvent) {
        this.isSalesMapEvent = true
      }
      salesMapContainer.on(
          'center_changed',
          (e) => {
            this.setSalesMarker()
            this.model.sales_latitude = e.center.lat
            this.model.sales_longitude = e.center.lng
          },
          false
      )
    },

    setSalesCenter(lat, lng) {
      const center = new TMap.LatLng(lat, lng)
      salesMapContainer.setCenter(center)
    },

    setSalesMarker() {
      if (!salesMarkerGeo) {
        salesMarkerGeo = {
          id: 'sales_center',
          position: salesMapContainer.getCenter(),
          styleId: 'sales_address',
          content: this.model.sales_address
        }
      } else {
        salesMarkerGeo.position = salesMapContainer.getCenter()
        salesMarkerGeo.content = this.model.sales_address
      }

      if (!salesMultiMarker) {
        salesMultiMarker = new TMap.MultiMarker({
          map: salesMapContainer,
          geometries: [salesMarkerGeo],
          styles: {
            sales_address: new TMap.MarkerStyle({
              direction: 'bottom'
            })
          }
        })
      } else {
        salesMultiMarker.updateGeometries([salesMarkerGeo])
      }
    },
    // 信息
    info() {
      if (this.houseId > 0) {
        Project.info({
          id: this.houseId
        }).then((res) => {
          this.model = res.data
          this.model.project_status = parseInt(this.model.project_status)
          this.model.release_status = parseInt(this.model.release_status)
          this.setCenter(this.model.latitude, this.model.longitude)
          this.setMarker(this.model.latitude, this.model.longitude)
          this.getPlateByRegion(res.data.region_id)
        })
      } else {
        this.model.id = 0
      }
    },
    // 字典
    params() {
      Project.searchparams().then((res) => {
        this.params_data = res.data
        this.regionData = res.data.region
        this.sectorData = res.data.sectors
      })
    },

    // 提交
    submit(off = false) {
      this.loading = true
      const params = this.model
      if (off) {
        params.is_send = 1
      }

      Project.editHouse(params)
        .then((res) => {
          if (this.model.id == 0) {
            ElMessage.success('新建楼盘成功，请在楼盘相册管理图片')
            // this.closeSelectedTag(this.$router.currentRoute.value, true)
            this.$router.replace('/house/land?id=' + res.data)
            return
          } else {
            ElMessage.success(res.msg)
            // 关闭当前页面，并返回上一页
            this.closeSelectedTag(this.$router.currentRoute.value)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 刷新
    refresh() {
      this.closeSelectedTag(this.$router.currentRoute.value, '/house/house')
    },

    // 选择 城区板块
    // changeCasc(selectValuesArr) {
    //   console.log('选中的值:', selectValuesArr)
    //   if (selectValuesArr == undefined) {
    //     return
    //   } else {
    //     this.model.region_name = selectValuesArr[0]
    //     this.model.sector_name = selectValuesArr[1]
    //   }
    // },
    changePlate(val) {
      if (val != undefined) {
        var index = this.plateData.findIndex((i) => i.id === val)
        this.model.sector_name = this.plateData[index].name
      }
    },
    changeRegion(selectValuesArr) {
      if (selectValuesArr != undefined) {
        var index = this.regionData.findIndex((i) => i.region_id === selectValuesArr)
        this.model.region_name = this.regionData[index].region_name
      }
      this.getPlateByRegion(selectValuesArr)
    },
    getPlateByRegion(region_id) {
      this.loading = true
      if (region_id == undefined) {
        this.plateData = []
        this.model.plate_id = 0
        this.loading = false
        return
      }
      list({ countyid: region_id, page: 1, limit: 100 })
        .then((res) => {
          this.plateData = res.data.list
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    closeSelectedTag(view, path) {
      var that = this
      tagsViewStore.delView(view).then((res) => {
        this.$router.push(path)
      })
    },
    // 选择 城区板块
    houseTypeChange(selectValuesArr) {
      var path = ''
      if (this.houseId == 0) {
        path = '/house/info'
      } else {
        path = '/house/info?id=' + this.houseId
      }

      this.closeSelectedTag(this.$router.currentRoute.value, path)
    },
    handleGetLng() {
      this.searchLoading = true
      const baseUrl = 'https://apis.map.qq.com'
      const url = `${baseUrl}/ws/geocoder/v1`
      jsonp(url, {
        key: 'POYBZ-JXV6Q-3KD5A-44ZNG-SGTIO-J6FDN',
        output: 'jsonp',
        address: this.model.build_address
      })
        .then((res) => {
          console.log('res:', res)
          if (res.status === 0) {
            const { lng, lat } = res.result.location
            this.model.latitude = lat
            this.model.longitude = lng
            if (res.result?.ad_info?.adcode) {
              this.setRegionId(Number(res.result.ad_info.adcode))
            }
            this.setCenter(lat, lng)
            this.setMarker()
            if (!this.isMapEvent) {
              this.updateCenter()
            }
          } else {
            ElMessage({
              message: '请填写详细地块地址',
              type: 'info'
            })
          }
        })
        .finally(() => {
          this.searchLoading = false
        })
    },
    setRegionId(id) {
      if (this.regionData.findIndex((i) => i.region_id === id) !== -1) {
        this.model.region_id = id
        this.changeRegion(this.model.region_id)
      }
    },
    // 初始化地图
    initMap() {
      mapContaniner = new TMap.Map(document.getElementById('mapContaniner'), {
        zoom: 13
      })
    },
    updateCenter() {
      if (!this.isMapEvent) {
        this.isMapEvent = true
      }
      mapContaniner.on(
        'center_changed',
        (e) => {
          this.setMarker()
          this.model.latitude = e.center.lat
          this.model.longitude = e.center.lng
        },
        false
      )
    },
    setCenter(lat, lng) {
      const center = new TMap.LatLng(lat, lng)
      mapContaniner.setCenter(center)
    },
    setMarker(lat, lng) {
      if (!markerGeo) {
        markerGeo = {
          id: 'center',
          position: mapContaniner.getCenter(),
          styleId: 'build_address',
          content: this.model.build_address
        }
      } else {
        markerGeo.position = mapContaniner.getCenter()
        markerGeo.content = this.model.build_address
      }
      if (!multiMarker) {
        multiMarker = new TMap.MultiMarker({
          map: mapContaniner,
          geometries: [markerGeo],
          styles: {
            build_address: new TMap.MarkerStyle({
              direction: 'bottom'
            })
          }
        })
      } else {
        multiMarker.updateGeometries([markerGeo])
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.wrapper {
  min-width: 1300px;
  :deep(.el-input) {
    width: 350px;
  }
  :deep(.el-select) {
    width: 350px;
  }
  :deep(.el-textarea) {
    width: 350px;
  }
  :deep(.el-divider) {
    margin: 30px 0;
  }
}
.region_plate {
  :deep(.el-input) {
    width: 120%;
  }

  .el-input {
    width: 120%;
  }
}

.sub-btn {
  padding: 20px 0;
}
.bold {
  font-weight: bold;
}
#mapContaniner {
  width: 500px;
  height: 250px;
  /* background: red; */
  /* border: 1px solid red; */
}
#salesMapContainer {
  width: 500px;
  height: 250px;
  /* background: red; */
  /* border: 1px solid red; */
}
</style>
