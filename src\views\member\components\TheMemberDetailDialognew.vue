<template>

    <div>
      <el-descriptions title="基础信息" direction="horizontal" :column="1">
        <el-descriptions-item label="用户ID：">{{ userInfo.member_id }}</el-descriptions-item>
        <el-descriptions-item label="openid：">{{
          userInfo.wxopenid || '暂无'
        }}</el-descriptions-item>
        <el-descriptions-item label="unionid：">{{
          userInfo.wxunionid || '暂无'
        }}</el-descriptions-item>
        <el-descriptions-item label="用户昵称：">{{
          userInfo.nickname || '暂无'
        }}</el-descriptions-item>
        <el-descriptions-item label="手机号：">
          {{ currentPhone || '暂无' }}
          <template v-if="userInfo.phone">
            <span class="ml-[10px] cursor-[pointer]">
              <svg-icon icon-class="view" v-if="!isHidePhone" @click="handViewPhone" />
            </span>
          </template>
        </el-descriptions-item>
        <el-descriptions-item label="关注楼盘： ">{{
          userInfo.followHouse || '暂无'
        }}</el-descriptions-item>
      </el-descriptions>
      <!-- 置业顾问/选房师表单 -->
      <div v-if="userInfo.identity_type == 1">
        <el-form :model="form" label-width="auto" style="max-width: 600px">
          <el-form-item label="选房师身份">
            <el-radio-group v-model="isHouseSelector">
              <el-radio :value=1>是</el-radio>
              <el-radio :value=2>否</el-radio>
            </el-radio-group>
         </el-form-item>
          <el-form-item label="姓名">
            <el-input v-model="userIdentityInfo.service_name" />
          </el-form-item>
          <el-form-item label="手机号">
            <el-input v-model="userIdentityInfo.service_phone" />
          </el-form-item>
          <el-form-item label="微信号">
            <el-input v-model="userIdentityInfo.wechat" />
          </el-form-item>
          
          
          <el-form-item label="个人介绍">
            <el-input v-model="userIdentityInfo.person_mark" type="textarea" />
          </el-form-item>
          <el-form-item label="服务客户数">
            <el-input v-model="userIdentityInfo.service_user_num" />
          </el-form-item>
          <el-form-item label="服务范围">
            <el-radio-group v-model="userIdentityInfo.relation_house_type">
              <el-radio :value=1>全北京</el-radio>
              <el-radio :value=2>区域</el-radio>
              <el-radio :value=3>楼盘</el-radio>
            </el-radio-group>
         </el-form-item>
         <el-form-item label="关联区域">
            <el-select
              v-model="userIdentityInfo.saleregionlist"
              clearable
              placeholder="选择关联区域"
              style="width: 240px"
              multiple
            >
              <el-option
                v-for="item in regionListArr"
                :key="item.region_id"
                :label="item.region_name"
                :value="item.region_id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="关联楼盘">
            <el-select
              v-model="userIdentityInfo.salehouselist"
              clearable
              placeholder="选择关联楼盘"
              style="width: 240px"
              multiple
            >
              <el-option
                v-for="userIdentityInfo in houseListArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="头像">
            <el-input v-model="userIdentityInfo.service_avatar" />
          </el-form-item>
          <el-form-item>
            <el-button :loading="loading" type="primary" @click="submit">提交</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div v-if="userInfo.identity_type == 2">
        置业顾问
      </div>
      <div v-if="userInfo.identity_type == 3">
        选房师
      </div>
      <div class="mt-[20px] font-bold font-size-[16px] color-[#303133]">浏览记录</div>
      <el-table
        ref="browsehistoryList"
        v-loading="historyTableData.loading"
        :data="historyTableData.list"
        height="350"
      >
        <el-table-column
          prop="createtime"
          label="浏览时间"
          width="160"
          :formatter="customformat('YYYY-MM-DD HH:mm')"
        />
        <el-table-column prop="id" label="ID" width="80" />

        <el-table-column prop="viewtype" label="类型" min-width="60" show-overflow-tooltip />
        <el-table-column label="手机设备及系统" min-width="160" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.devicemodel">{{ row.devicemodel }}</span>
            <br v-if="row.devicemodel" />
            <span v-if="row.devicesystem">{{ row.devicesystem }}</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="housename"
          label="楼盘"
          min-width="170"
          show-overflow-tooltip
          :formatter="defaultFormat()"
        />
        <el-table-column
          prop="relate_content"
          label="关联内容"
          min-width="112"
          :formatter="defaultFormat()"
          show-overflow-tooltip
        />
        <el-table-column
          prop="more_content"
          label="更多内容"
          :formatter="defaultFormat()"
          min-width="112"
          show-overflow-tooltip
        />
      </el-table>
      <pagination
        v-model:total="historyTableData.total"
        v-model:page="historyTableData.page"
        v-model:limit="historyTableData.limit"
        @pagination="getUserHistoryList"
      />
    </div>
  
</template>

<script setup>
import { memberInfo, getMemberBrowseHistry, getMemberPhoneDtail } from '@/api/member/member'
import { computed, ref, watch } from 'vue'
import { getPageLimit } from '@/utils/settings'
import { defaultFormat } from '@/utils'
import { customformat } from '@/utils/dateUtil'
import { regionList } from '@/api/setting/region'
import { houseList } from '@/api/project/house'
import { getIsExistIdentity } from '@/api/member/memberidentity'
const STATIC_COFIG = {
  page: 1,
  limit: getPageLimit(),
  loading: false,
  total: 0
}

const loading = ref(true)

const emits = defineEmits(['close'])

const visible = defineModel('visible', {
  type: Boolean,
  default: false
})

const props = defineProps({
  id: [String, Number]
})
watch(()=>props.id,(newVal,oldVal)=>{
  if(newVal){
    getUserInfo()
    getUserHistoryList()
    getIsExistIdentityInfo();
  }
  
})
const userInfo = ref({})
const userIdentityInfo = ref({})
const form = ref({})
const allPhone = ref('')
const isHouseSelector = ref(2);
const regionListArr = ref([]);
const houseListArr = ref([]);
const isHidePhone = ref(false)

const historyTableData = ref({ ...STATIC_COFIG, list: [] })

const currentPhone = computed(() => {
  return isHidePhone.value ? allPhone.value : userInfo.value?.phone
})
function currentIsHouseSelector(identity_type){
  if(identity_type == 3){
    isHouseSelector.value = 1;
  }else{
    isHouseSelector.value = 2;
  }
 
}
const getRegionList = async ()=>{
  const {data} = await regionList({"region_pid":1101});

  
  regionListArr.value = data.list
}
const getHouseList = async () =>{
  const {data} = await houseList({});
  houseListArr.value = data.list;
}
const getIsExistIdentityInfo = async()=>{
  const {data} = await getIsExistIdentity({"member_id":props.id,"identity_type":3})
  userIdentityInfo.value = data;
}
const getUserInfo = async () => {
  try {
    
    const { data } = await memberInfo({ member_id: props.id })
    console.log(data)
    userInfo.value = data
    currentIsHouseSelector(userInfo.value.identity_type)
  } finally {
  }
}

const getUserHistoryList = async () => {
  historyTableData.loading = true
  try {
    const params = {
      member_id: props.id,
      page: historyTableData.value.page,
      limit: historyTableData.value.limit
    }
    const { data } = await getMemberBrowseHistry(params)
   
    historyTableData.value.list = data.list
    historyTableData.value.total = data.count
  } finally {
    historyTableData.loading = false
  }
}


const handleClose = () => {
  emits('close')
  userInfo.value = {}
  allPhone.value = ''
  isHidePhone.value = false
  historyTableData.value = { ...STATIC_COFIG, list: [] }
}

const handViewPhone = async () => {
  if (allPhone.value) return
  const { data } = await getMemberPhoneDtail({ member_id: props.id })
  allPhone.value = data.phone
  isHidePhone.value = true
}
function submit() {
  proxy.$refs['ref'].validate((valid) => {
    if (!valid) {
      return
    }
    loading.value = true
    if (model.value[idkey.value]) {
      
    } else {
      
    }
  })
}
const handleViewHidePhone = () => (isHidePhone.value = false)
onMounted(() => {
  if(props.id){
    getUserInfo()

    getUserHistoryList()
    // getIsExistIdentityInfo();
  }
  getRegionList();
  getHouseList();
})
</script>

<style lang="scss" scoped></style>
