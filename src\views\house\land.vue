<template>
  <div class="app-container">
    <el-tabs>
      <el-tab-pane
          label="地块信息"
      >
        <HouseLand :houseId="house_id"/>
      </el-tab-pane>
      <el-tab-pane v-if="house_id > 0"
                   label="楼盘相册"
                   lazy
                   name="houseimage"
      >
        <HouseImageManage :houseId="house_id"/>
      </el-tab-pane>
      <el-tab-pane v-if="house_id > 0" label="楼盘动态" lazy name="housenews">
        <HouseNews :houseId="house_id"/>
      </el-tab-pane>

    </el-tabs>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import HouseLand from './components/HouseLand.vue'
import HouseImageManage from './components/HouseImage.vue'
import HouseNews from "@/views/house/components/HouseNews.vue";

export default {
  name: 'HouseLandIndex',

  components: {
    HouseNews,
    HouseLand,
    HouseImageManage
  },
  data() {
    return {
      name: '地块信息',
      house_id: '',
    }
  },
  methods: {
    checkPermission
  },
  mounted() {
    this.house_id = this.$route.query.id; // John
  }
}
</script>
