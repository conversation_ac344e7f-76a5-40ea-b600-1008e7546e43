<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    custom-class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
    width="700px"
    :z-index="1000"
    top="2vh"
  >
    <el-form class="form-box" ref="formRef" :rules="rules" :model="siteInfo" label-width="100px">
      <el-divider>点位信息</el-divider>
      <el-form-item label="点位类型"> {{ currentTypeTitle }} </el-form-item>

      <el-form-item label="地铁选择" prop="subwaySelect" v-if="type == 1 && isCreate && siteInfo.feature.type==2">
          <el-radio-group v-model="subwaySelect" @change="selectChange">
            <el-radio :value="1">地铁选择</el-radio>
            <el-radio :value="2">点位选择</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="学校选择" prop="schoolSelect" v-if="type == 2 && isCreate">
          <el-radio-group v-model="schoolSelect" @change="selectChange">
            <el-radio :value="1">学校选择</el-radio>
            <el-radio :value="2">点位选择</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="商场选择" prop="shopSelect" v-if="type == 3 && isCreate">
          <el-radio-group v-model="shopSelect" @change="selectChange">
            <el-radio :value="1">商场选择</el-radio>
            <el-radio :value="2">点位选择</el-radio>
          </el-radio-group>
        </el-form-item>
      
      <el-form-item label="点位名称" prop="facility_name" required v-if="!facility_name_disable">
        <el-input
          v-model="siteInfo.facility_name"
          placeholder="请输入点位名称"
          :disabled="isMark"
          clearable
        >
        </el-input>
      </el-form-item>
      <div v-if="type == 1 && isCreate && siteInfo.feature.type==2 && subwaySelect == 1">
        <el-form-item
              label="地铁线"
              prop="line_id"
              placeholder="选择地铁线"
            >
            <el-select v-model="subwayData.subways" @change="lineChange" class="w-full" clearable filterable>
                <el-option
                  v-for="item in params_data['subways']"
                  :key="item.id"
                  :label="item.subway_name"
                  :value="item.id"
                />
            </el-select>
                 
      </el-form-item>
      <el-form-item
              label="地铁站"
              prop="site_id"
              placeholder="选择地铁站"
            >
            <el-select v-model="subwayData.site" class="w-full" clearable filterable  @change="siteChange">
                <el-option
                  v-for="item in siteData"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
            </el-select>
       
      </el-form-item>

    </div>
    <div v-else-if="type == 2 && isCreate && schoolSelect == 1">
      <el-form-item
              label="学校"
              prop="schoo_id"
              placeholder="请选择学校"
            >
            <el-select v-model="schoolData.school" @change="schoolChange" class="w-full" clearable filterable>
                <el-option
                  v-for="item in schoolRes"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
            </el-select>
                 
      </el-form-item>
    </div>
    <div v-else-if="type == 3 && isCreate && shopSelect == 1">
      <el-form-item
              label="商场"
              prop="shop_id"
              placeholder="请选择商场"
            >
            <el-select v-model="shopData.shop" @change="shopChange" class="w-full" clearable filterable>
                <el-option
                  v-for="item in schoolRes"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
            </el-select>
                 
      </el-form-item>
    </div>
      <div v-else>
        <el-form-item label="经度" prop="longitude.longitude" required>
        <el-input
          v-model="siteInfo.longitude.longitude"
          :disabled="!isCreate"
          placeholder="输入经度信息"
          clearable
        >
        </el-input>
      </el-form-item>
      <el-form-item label="纬度" prop="longitude.latitude" required>
        <el-input
          v-model="siteInfo.longitude.latitude"
          :disabled="!isCreate"
          placeholder="输入纬度信息"
          clearable
        >
        </el-input>
      </el-form-item>
      </div>
      
      <el-form-item label="关联楼盘" prop="house_ids" required>
        <el-select v-model="houseIds" multiple clearable filterable :disabled="!isCreate">
          <el-option
            :disabled="item.id == projectId"
            v-for="(item, index) in projectData"
            :key="index"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <!-- <h3>特征信息</h3> -->
      <el-divider>特征信息</el-divider>
      <el-form-item label="交通" prop="feature.type" v-if="type == 1 && !featureisEmpty">
        <el-radio-group v-model="siteInfo.feature.type" @change="jiaotongChange">
          <el-radio value=" ">无</el-radio>
          <el-radio v-for="(key, value) in TRAFFIC_MAP" :key="value" :value="Number(value)">{{
            key
          }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="type == 2 && !featureisEmpty">
        <el-form-item label="学校" prop="feature.is_famous">
          <el-radio-group v-model="siteInfo.feature.is_famous">
            <el-radio :value="0"> 无</el-radio>
            <el-radio :value="1">知名学校</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="学校品质" prop="feature.quality">
          <el-checkbox-group v-model="siteInfo.feature.quality">
            <el-checkbox label="优质公立小学" value="0" key="0" />
            <el-checkbox label="优质公立中学" value="1" key="1" />
            <el-checkbox label="国际学校" value="2" key="2" />
            <el-checkbox label="优质民办学校" value="3" key="3" />
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="学校类型" prop="feature.type">
          <el-radio-group v-model="siteInfo.feature.type">
            <el-radio :value="3">小学</el-radio>
            <el-radio :value="4">初中</el-radio>
            <el-radio :value="11">幼儿园</el-radio>
          </el-radio-group>
        </el-form-item>
      </template>

      <el-form-item
        label="商场、文化"
        v-if="(type == 3 || type == 6) && !featureisEmpty"
        prop="feature.star"
      >
        <el-input v-model="siteInfo.feature.star" placeholder="输入星级信息，一位小数" clearable>
        </el-input>
      </el-form-item>

      <el-form-item label="医院" v-if="type == 4 && !featureisEmpty" prop="feature.level">
        <el-select v-model="siteInfo.feature.level" placeholder="级别" clearable>
          <el-option label="级别" value="" />
          <el-option
            v-for="(key, value) in HOSPITAL_LAVELS"
            :key="value"
            :label="key"
            :value="Number(value)"
          />
        </el-select>
      </el-form-item>
      <template v-if="type == 5 && !featureisEmpty">
        <el-form-item label="公园类型" prop="feature.type" :rules="customRules.type">
          <el-select v-model="siteInfo.feature.type" clearable placeholder="公园类型">
            <el-option label="类型" value="" />
            <el-option
              v-for="(key, value) in PARK_TYPES"
              :key="value"
              :label="key"
              :value="Number(value)"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="公园等级" prop="feature.level" :rules="customRules.level">
          <el-select v-model="siteInfo.feature.level" clearable placeholder="公园等级">
            <el-option label="级别" value="" />
            <el-option
              v-for="(key, value) in PARK_LAVELS"
              :key="value"
              :label="key"
              :value="Number(value)"
            />
          </el-select>
        </el-form-item>
      </template>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading"> 提交 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { isEmpty } from '@/utils/validate'
import { computed, reactive, ref } from 'vue'
import {
  FACILTY_TYPES,
  FEATURE_MAP,
  HOSPITAL_LAVELS,
  PARK_TYPES,
  PARK_LAVELS,
  TRAFFIC_MAP
} from '../config'
import { projectList } from '@/api/project/project'
import {
  supportEvaluationAddPoint,
  getSupporDetails,
  editSupporDetails,
  markSupporDetails,
  getSchoolList,
} from '@/api/project/evaluating'

import * as Project from '@/api/project/house'
import { getlinesite }  from '@/api/project/subway'
const getDefaultFormData = () => {
  return {
    facility_name: '',
    facility_type: '',
    longitude: {
      longitude: '',
      latitude: ''
    },
    house_ids: '',
    feature: {}
  }
}

const route = useRoute()
const facility_name_disable = ref(false)
const projectData = ref([])
const params_data = ref([])
const subwayData = ref({})
const subwaySelect = ref(1)
const schoolSelect = ref(1)
const shopSelect = ref(1)
const shopData = ref([])
const schoolData = ref([])
const schoolRes = ref([])
const siteData = ref([])

const emits = defineEmits(['close', 'update'])

const visible = defineModel('visible', {
  type: Boolean,
  default: false
})

const props = defineProps({
  id: {
    type: [String, Number],
    default: ''
  },
  model: {
    type: String,
    default: '',
    validator(value) {
      if (!value) return true
      return value && ['create', 'edit', 'mark'].includes(value)
    }
  },
  type: {
    type: [String, Number],
    default: ''
  }
})

const formRef = ref(null)

const projectId = computed(() => route.query.id)

const siteInfo = ref({ ...getDefaultFormData() })

const isEdit = computed(() => props.model === 'edit')

const isMark = computed(() => props.model === 'mark')

const isCreate = computed(() => props.model === 'create')

const currentTypeTitle = computed(() => FACILTY_TYPES[props.type])

const featureisEmpty = computed(() => isEmpty(siteInfo.value.feature))

const submitLoading = ref(false)

const houseIds = computed({
  get() {
    if (isEmpty(siteInfo.value.house_ids)) return []
    return siteInfo.value.house_ids.split(',').map((i) => Number(i))
  },
  set(val) {
    siteInfo.value.house_ids = val.join(',')
  }
})

const editID = ref('')

const validateDecimal = (rule, value, callback) => {
  const regex = /^\d+(\.\d+)?$/
  if (!value || !regex.test(value)) {
    return callback(new Error('请输入有效的小数'))
  }
  callback()
}

const validateStar = (rule, value, callback) => {
  if (!value) {
    return callback()
  }
  if (value > 5) {
    return callback(new Error('最大评分5'))
  }
  const regex = /^\d+(\.\d)?$/
  if (!regex.test(value)) {
    return callback(new Error('请输入整数或一位小数'))
  }
  callback()
}
const rules = reactive({
  facility_name: [
    {
      required: true,
      message: '请输入点位名称',
      trigger: ['blur', 'change']
    }
  ],
  'longitude.longitude': [
    { required: true, message: '经度不能为空', trigger: ['blur', 'change'] },
    { validator: validateDecimal, trigger: ['blur', 'change'] }
  ],
  'longitude.latitude': [
    { required: true, message: '纬度不能为空', trigger: ['blur', 'change'] },
    { validator: validateDecimal, trigger: ['blur', 'change'] }
  ],
  house_ids: [{ required: true, message: '关联楼盘不能为空', trigger: ['blur', 'change'] }],
  'feature.facility_subtype': [
    { required: true, message: '关联楼盘不能为空', trigger: ['blur', 'change'] }
  ],
  'feature.star': [{ validator: validateStar, trigger: ['blur', 'change'] }]
})

const customRules = reactive({
  type: [
    {
      validator: (rule, value, callback) => {
        if (!value && siteInfo.value.feature.level) {
          return callback(new Error('选择公园等级之后须选择公园类型'))
        }
        callback()
      },
      trigger: ['blur', 'change']
    }
  ],
  level: [
    {
      validator: (rule, value, callback) => {
        if (!value && siteInfo.value.feature.type) {
          return callback(new Error('选择公园类型之后须选择公等级'))
        }
        callback()
      },
      trigger: ['blur', 'change']
    }
  ]
})

const dialogTitle = computed(() => {
  if (isCreate.value) return '配套点位增加'
  if (isEdit.value) return '配套点位编辑'
  if (isMark.value) return '配套点位标注'
})

const getProjectList = async () => {
  try {
    const { data } = await projectList({ page: 1, limit: 1000 })
    console.log(data)
    projectData.value = data.list
  } finally {
  }
}

const getDetails = async () => {
  try {
    const { data } = await getSupporDetails(props.id)
    const { id, latitude, longitude, house_id } = data
    for (let key in getDefaultFormData()) {
      if (key === 'feature') {
        siteInfo.value[key] = !isEmpty(data[key]) ? data[key] : FEATURE_MAP[props.type]
      } else {
        siteInfo.value[key] = data[key]
      }
    }
    editID.value = id
    siteInfo.value.longitude = { latitude, longitude }
    siteInfo.value.house_ids = house_id + ''
  } finally {
  }
}
const handleOpen = () => {
  resetForm()
  if (!projectData.value.length) {
    getProjectList()
  }
  if (isCreate.value) {
    siteInfo.value.facility_type = props.type
    siteInfo.value.house_ids = projectId.value
    siteInfo.value.feature = { ...FEATURE_MAP[props.type] }
    
    
    
    // 学校 或者商店
    if (siteInfo.value.facility_type == 2 || siteInfo.value.facility_type == 3) {
      selectChange(1)
      if (siteInfo.value.facility_type == 2) {
        schoolSelect.value = 1
      }
      if (siteInfo.value.facility_type == 3) {
        shopSelect.value = 1
      } 
      getSchoolOrShop()
    }else if (siteInfo.value.facility_type == 1 && siteInfo.value.feature.type == 2 ) {
      selectChange(1)
      subwaySelect.value = 1
      getLine()
    }else{
      selectChange(2)
    }

   
    return
  }else{
    facility_name_disable.value = false
  }
  
  getDetails()
}

const fetchUpdate = async () => {
  submitLoading.value = true
  const feature = {}
  for (let key in siteInfo.value.feature) {
    const val = siteInfo.value.feature[key]
    if (!isEmpty(val)) {
      feature[key] = val
    }
  }

  try {
    if (isCreate.value) {
      await supportEvaluationAddPoint({ ...siteInfo.value, feature })
    }

    if (isEdit.value) {
      await editSupporDetails(editID.value, { ...siteInfo.value, feature })
    }

    if (isMark.value) {
      await markSupporDetails(editID.value, { ...siteInfo.value, feature })
    }

    ElMessage.success('提交成功')
    handleClose()
    emits('update')
  } finally {
    submitLoading.value = false
  }
}

const handleClose = () => {
  formRef.value.resetFields()
  siteInfo.value = { ...getDefaultFormData() }
  emits('close')
  visible.value = false
}

const handleSubmit = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      fetchUpdate()
    }
  })
}
const getLine = async () =>{
        Project.searchparams().then((res) => {
        params_data.value = res.data
      })
    }

const lineChange = async () =>{
   
    
    siteInfo.value.longitude.longitude = ''
    siteInfo.value.longitude.latitude = ''
    siteInfo.value.facility_name = ''
    if (delete siteInfo.value.txpoi_id) {
      delete siteInfo.value.txpoi_id
    }
    if (siteInfo.value.point_id) {
      delete siteInfo.value.point_id
    }
    
  getlinesite({lineid:subwayData.value.subways}).then((res) => {
      siteData.value = res.data
  })
  siteChange(0)
  
}
const siteChange = async (val) =>{
  if (val == undefined) {
      resetForm()
    }
    for (let index = 0; index < siteData.value.length; index++) {
      if (siteData.value[index].id == val) {
        siteInfo.value.longitude.longitude = siteData.value[index].lng
        siteInfo.value.longitude.latitude = siteData.value[index].lat
        siteInfo.value.facility_name = siteData.value[index].name
        siteInfo.value.poi_id = siteData.value[index].txpoi_id
        siteInfo.value.point_id = val
        siteInfo.value.source_id = val
        return 
      }
    }
    subwayData.value.site = null
    
}
const schoolChange = async (val) =>{
  if (val == undefined) {
    resetForm()
  }
  for (let index = 0; index < schoolRes.value.length; index++) {
      if (schoolRes.value[index].id == val) {
        siteInfo.value.longitude.longitude = schoolRes.value[index].longitude
        siteInfo.value.longitude.latitude = schoolRes.value[index].latitude
        siteInfo.value.facility_name = schoolRes.value[index].name
        siteInfo.value.poi_id = schoolRes.value[index].poi_id
        siteInfo.value.feature.is_famous = schoolRes.value[index].is_famous
        if (schoolRes.value[index].quality != null) {
          siteInfo.value.feature.quality = schoolRes.value[index].quality
        }
        siteInfo.value.point_id = val
        return 
      }
    }
  }
  const shopChange = async (val) =>{
    if (val == undefined) {
      resetForm()
    }
  for (let index = 0; index < schoolRes.value.length; index++) {
      if (schoolRes.value[index].id == val) {
        siteInfo.value.longitude.longitude = schoolRes.value[index].longitude
        siteInfo.value.longitude.latitude = schoolRes.value[index].latitude
        siteInfo.value.facility_name = schoolRes.value[index].name
        siteInfo.value.feature.star = schoolRes.value[index].star
        siteInfo.value.poi_id = schoolRes.value[index].poi_id
        siteInfo.value.point_id = val
        return 
      }
    }
  }  
const getSchoolOrShop = async () =>{
    
    getSchoolList({"type":siteInfo.value.facility_type}).then((res) => {
      schoolRes.value = res.data
      })
    }
const selectChange = async (val) =>{
  
  if (isCreate.value == 1) {
    if (val== 1) {
      facility_name_disable.value = true    
    }else{
      facility_name_disable.value = false
    }
  }
}
const jiaotongChange = async (val) =>{
  if(isCreate.value){
    if (val == 2) {
      selectChange(1)
    }else{
      selectChange(0)
    }
    resetForm()
  }
  
}
const resetForm = async () =>{
  let house_ids =  siteInfo.value.house_ids
  let features =  siteInfo.value.feature
  let facType =  siteInfo.value.facility_type
  let facSubType =  siteInfo.value.feature.type
  siteInfo.value = getDefaultFormData() 
  siteInfo.value.house_ids = house_ids
  siteInfo.value.feature = features
  siteInfo.value.facility_type = facType
  siteInfo.value.type = facSubType
  if (siteInfo.value.facility_type == 1 && siteInfo.value.feature.type == 2 && subwaySelect.value == 2) {
    facility_name_disable.value = false
  }
  subwayData.value = {}
  schoolData.value = {}
  shopData.value = {}
}
</script>
<style lang="scss" scoped>
.form-box {
  :deep(.el-input) {
    width: 300px;
  }
  :deep(.el-select) {
    width: 300px;
  }
}
</style>
