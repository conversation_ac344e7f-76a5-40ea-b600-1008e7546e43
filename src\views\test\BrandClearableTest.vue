<template>
  <div class="test-container">
    <h2>品牌下拉列表清除功能测试</h2>
    
    <el-card style="margin-bottom: 20px;">
      <template #header>
        <span>测试控制面板</span>
      </template>
      
      <div style="margin-bottom: 20px;">
        <el-button type="primary" @click="addTestBrands">添加测试品牌</el-button>
        <el-button type="warning" @click="clearAllBrands">清空所有品牌</el-button>
        <el-button @click="logCurrentBrands">查看当前品牌</el-button>
      </div>
      
      <div style="background: #f0f0f0; padding: 10px; border-radius: 4px;">
        <div><strong>当前选中的品牌数量：</strong> {{ selectedBrands.length }}</div>
        <div><strong>品牌ID列表：</strong> {{ selectedBrands.map(b => b.id).join(', ') }}</div>
        <div><strong>品牌名称列表：</strong> {{ selectedBrands.map(b => getBrandName(b.id)).join(', ') }}</div>
      </div>
    </el-card>

    <el-card>
      <template #header>
        <span>品牌选择器测试</span>
      </template>
      
      <el-form label-width="120px">
        <el-form-item label="选择品牌">
          <el-select
            v-model="selectedBrandIds"
            multiple
            placeholder="请选择品牌"
            style="width: 100%;"
            @change="handleBrandChange"
          >
            <template #tag="{ option }">
              <el-tag 
                :key="option.value" 
                type="primary" 
                closable
                @close="handleRemoveBrand(option.value)"
                class="brand-tag"
              >
                {{ getBrandName(option.value) }}
              </el-tag>
            </template>
            
            <el-option
              v-for="brand in brandOptions"
              :key="brand.id"
              :label="brand.union_brand"
              :value="brand.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <div style="margin-top: 20px;">
        <h4>实时监控：</h4>
        <div style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace;">
          <div>selectedBrandIds: {{ selectedBrandIds }}</div>
          <div>selectedBrands: {{ JSON.stringify(selectedBrands) }}</div>
          <div>操作历史: {{ operationHistory.slice(-5).join(' → ') }}</div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'BrandClearableTest'
})

// 模拟品牌数据
const brandOptions = ref([
  { id: 1, union_brand: '苹果 Apple' },
  { id: 2, union_brand: '三星 Samsung' },
  { id: 3, union_brand: '华为 Huawei' },
  { id: 4, union_brand: '小米 Xiaomi' },
  { id: 5, union_brand: '索尼 Sony' },
  { id: 6, union_brand: '戴尔 Dell' },
  { id: 7, union_brand: '联想 Lenovo' },
  { id: 8, union_brand: '惠普 HP' }
])

// 选中的品牌ID列表
const selectedBrandIds = ref([])

// 操作历史
const operationHistory = ref(['初始化'])

// 计算选中的品牌对象列表
const selectedBrands = computed(() => {
  return selectedBrandIds.value.map(id => ({ id }))
})

// 监听品牌选择变化
watch(selectedBrandIds, (newIds, oldIds) => {
  console.log('品牌选择变化:', oldIds, '->', newIds)
  
  if (newIds.length > oldIds.length) {
    const addedIds = newIds.filter(id => !oldIds.includes(id))
    addedIds.forEach(id => {
      const brandName = getBrandName(id)
      operationHistory.value.push(`添加: ${brandName}`)
    })
  } else if (newIds.length < oldIds.length) {
    const removedIds = oldIds.filter(id => !newIds.includes(id))
    removedIds.forEach(id => {
      const brandName = getBrandName(id)
      operationHistory.value.push(`移除: ${brandName}`)
    })
  }
}, { deep: true })

// 获取品牌名称
const getBrandName = (id) => {
  const brand = brandOptions.value.find(item => item.id === id)
  return brand ? brand.union_brand : `未知品牌(${id})`
}

// 处理品牌选择变化
const handleBrandChange = (value) => {
  console.log('品牌选择变化事件:', value)
}

// 处理移除品牌
const handleRemoveBrand = (brandId) => {
  console.log('移除品牌ID:', brandId)
  
  // 从选中列表中移除
  selectedBrandIds.value = selectedBrandIds.value.filter(id => id !== brandId)
  
  const brandName = getBrandName(brandId)
  ElMessage.success(`已移除品牌: ${brandName}`)
  
  console.log('移除后的品牌列表:', selectedBrandIds.value)
}

// 测试函数
const addTestBrands = () => {
  selectedBrandIds.value = [1, 3, 5, 7] // 苹果、华为、索尼、联想
  operationHistory.value.push('批量添加测试品牌')
  ElMessage.info('已添加测试品牌')
}

const clearAllBrands = () => {
  selectedBrandIds.value = []
  operationHistory.value.push('清空所有品牌')
  ElMessage.info('已清空所有品牌')
}

const logCurrentBrands = () => {
  console.log('当前选中的品牌:', selectedBrands.value)
  console.log('品牌ID列表:', selectedBrandIds.value)
  console.log('品牌名称:', selectedBrandIds.value.map(id => getBrandName(id)))
  
  ElMessage.info(`当前选中 ${selectedBrands.value.length} 个品牌，详情请查看控制台`)
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

h2 {
  color: #303133;
  margin-bottom: 20px;
}

h4 {
  margin: 10px 0;
  color: #606266;
}

.brand-tag {
  margin: 2px;
  cursor: pointer;
}

.brand-tag:hover {
  opacity: 0.8;
}

.brand-tag .el-tag__close {
  color: #fff;
}

.brand-tag .el-tag__close:hover {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
}
</style>
