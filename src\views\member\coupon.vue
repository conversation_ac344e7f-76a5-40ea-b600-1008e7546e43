<template>
  <div class="page-contaniner">
    

    <div class="table-box">
    
      
      <el-table :data="tableData.list" v-loading="tableData.loading">
        <el-table-column prop="id" label="ID" />
        <el-table-column prop="member_id" label="用户ID" />
        
        <el-table-column prop="face_value" label="券金额" :formatter="defaultFormat()" />
       
        <el-table-column prop="receive_time" label="领取时间" :formatter="defaultFormat()" />
        <el-table-column prop="able_start_time" label="可用开始时间" :formatter="defaultFormat()" />
        <el-table-column prop="able_end_time" label="可用结束时间" :formatter="defaultFormat()" />
       
        <el-table-column prop="state" label="状态"  >
          <template #default="scope">
          <el-text v-if="scope.row.state == 1 " >待使用</el-text>
          <el-text v-else-if="scope.row.state == 2" >已核销</el-text>
          <el-text v-else-if="scope.row.state == 3" >已失效</el-text>
          <el-text v-else>-</el-text>
        </template>
        </el-table-column>
        <el-table-column prop="use_user_name" label="核销人" :formatter="defaultFormat()" />
        <el-table-column prop="use_time" label="核销时间" :formatter="defaultFormat()" />
        <el-table-column
          prop="create_time"
          label="创建时间"
         
        />
        
        <el-table-column label="操作" width="140">
          <template #default="{ row }">
            <div class="flex">
              <div class="ml-[10px]">
                <el-link type="primary" v-if="row.state == 1" @click="editdialog(row,1)">核销</el-link>
                <el-link type="primary" v-if="row.state == 2" @click="editdialog(row,2)">查看</el-link>
                
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="tableData.total > 0"
        v-model:total="tableData.total"
        v-model:page="query.page"
        v-model:limit="query.limit"
        @pagination="featchList"
      />
    </div>
    <el-dialog
      v-model="dialog"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="cancel"
      top="5vh"
    >
      <el-form ref="editform"  :model="model" label-width="100px">
        <el-tabs>
          <el-tab-pane label="信息">
            <el-scrollbar native >
              
              <el-form-item label="会员：" prop="nickname">
                <el-text>{{ model.nickname }}</el-text>
              </el-form-item>
              <el-form-item label="券金额" prop="face_value">
                <el-text>{{ model.face_value }}</el-text>
              </el-form-item>
             
              <el-form-item v-if="model.state == 2" label="核销时间" prop="use_time">
                <el-text :formatter="defaultFormat()">{{ model.use_time }}</el-text>
              </el-form-item>
              <el-form-item v-if="model.state == 2" label="核销人" prop="use_user_name">
                <el-text :formatter="defaultFormat()">{{ model.use_user_name }}</el-text>
              </el-form-item>
              <el-form-item label="核销凭证" prop="use_img_url">
                <ImgUpload
                  v-model="model.use_img_id"
                  v-model:file-url="model.use_img_url"
                  file-type="image"
                  :disabled="viewMode"
                  :height="100"
                />
                </el-form-item>
              
              <el-form-item label="备注" prop="remarks">
                <el-input v-model="model.remarks" placeholder="备注" :disabled="viewMode" clearable type="textarea" />
              </el-form-item>
            </el-scrollbar>
          </el-tab-pane>
         
          
        </el-tabs>
      </el-form>
      <template #footer>
        <el-button :loading="loading" @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" :disabled="viewMode" @click="eidtsubmit">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>


import { reactive, ref, onMounted,defineOptions } from 'vue'
import { getPageLimit } from '@/utils/settings'
import { list, info, edit } from '@/api/member/membercoupon'
import { customformat } from '@/utils/dateUtil'
import { defaultFormat } from '@/utils'


const dialog = ref(false);
const model = ref({});
const dialogTitle = ref('');
const rowInfo = ref({});
const loading = ref(true);
const viewMode = ref(false)
defineOptions({
name: 'MemberCoupon'
})

const query = ref({
  page: 1,
 
  limit: getPageLimit()
})

function editdialog(row,isview) {
  
  
  viewMode.value = isview == 1 ? false : true;
  rowInfo.value = row
  loading.value = true;
  dialog.value = true
  dialogTitle.value = "核销优惠券";

  
  let id = row.id;
  info({id:id})
    .then((res) => {
      reset(res.data)
    })
    .catch((e) => {
        console.log(e);
        
    })
  loading.value = false

}

function eidtsubmit(){
  if(model.value.use_img_url == ''){

  }
  loading.value = true;
  let dataform = {
    "use_img_url":model.value.use_img_url,
    'remarks':model.value.remarks,
    'id':model.value.id
  }

  edit(dataform).then((res)=>{
 
    featchList()
    ElMessage.success("核销成功");
    dialog.value = false
    
  }).catch((e)=>{
    console.log(e);
    
  }).finally(()=>{
    loading.value = false
  });

}

function cancel() {
  dialog.value = false
  reset()    
}
// 重置
function reset(row) {
  if (row) {
    model.value = row
  } else {
    model.value = {}
  }
}
const visible = ref(false)

const tableData = reactive({
  total: 0,
  auditnum: 0,
  list: [],
  loading: false
})

const currentId = ref(null)

const featchList = async () => {
  tableData.loading = true
  const { data } = await list(query.value)
  tableData.total = data.count
  tableData.list = data.list
  tableData.auditnum = data.auditnum
  try {
  } finally {
    tableData.loading = false
  }
}

const handleSearch = (params) => {
  query.value = { ...query.value, ...params }
  featchList()
}


//onMounted()
onMounted(() => {
  
  featchList()
})
</script>
<style lang="scss" scoped>
.page-contaniner {
  min-width: 1200px;
  .table-box {
    padding: 10px;
    margin: 0 20px;
  }
}
</style>
