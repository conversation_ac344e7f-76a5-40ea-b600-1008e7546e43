<template>
  <div class="app-container">
    <el-tabs>
      <el-tab-pane
        :label="name"
      >
      </el-tab-pane>
    </el-tabs>
    <!-- 用户名称，上次登录时间 -->
    <el-row>
      <el-col class="mb-2">
        <el-form-item :label="memberScore.zygw.service_name">
          <span>{{ timestampToDate(memberScore.update_time * 1000) }}</span>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row v-loading="loading" :gutter="10">
      <el-col :xs="24" :span="6">
        <el-card :body-style="{ padding: '10px 0px' }">
          <template #header>
            <div class="text-center">
              <span>用户可用分</span>
            </div>
          </template>
          <div class="text-center" title="用户可用分">
            <el-statistic :value="memberScore.available_score" />
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :span="6">
        <el-card :body-style="{ padding: '10px 0px' }">
          <template #header>
            <div class="text-center">
              <span>用户冻结积分</span>
            </div>
          </template>
          <div class="text-center" title="用户冻结积分">
            <el-statistic :value="memberScore.freeze_score" />
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :span="6">
        <el-card :body-style="{ padding: '10px 0px' }">
          <template #header>
            <div class="text-center">
              <span>累计发放积分</span>
            </div>
          </template>
          <div class="text-center" title="累计发放积分">
            <el-statistic :value="memberScore.total_get_score" />
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :span="6">
        <el-card :body-style="{ padding: '10px 0px' }">
          <template #header>
            <div class="text-center">
              <span>累计消耗积分</span>
            </div>
          </template>
          <div class="text-center" title="累计消耗积分">
            <el-statistic :value="memberScore.total_consume_score" />
          </div>
        </el-card>
      </el-col>
    </el-row>
    <br />
    <!-- 查询 -->
    <el-row>
      <el-col class="mb-2"> </el-col>
    </el-row>
    <!-- 操作 -->
    <el-row>
      <el-row>
        <el-col class="mb-2">
          <el-form-item label="积分管理">
            <el-col>
              <el-button title="积分下发" @click="selectOpen('give')">积分下发</el-button>
              <el-button title="积分消耗" @click="selectOpen('consume')">积分消耗</el-button>
              <el-button title="积分冻结" @click="selectOpen('frozen')">积分冻结</el-button>
            </el-col>
          </el-form-item>
        </el-col>
      </el-row>
    </el-row>

    <!-- 列表 -->
    <el-table
      ref="table"
      v-loading="loading"
      :data="data"
      :height="height"
      @sort-change="sort"
      @selection-change="select"
    >
      <el-table-column type="selection" width="42" title="全选/反选" />
      <el-table-column :prop="idkey" label="ID" width="80" />
      <el-table-column prop="create_time" label="操作时间" width="165" />
      <el-table-column prop="create_time" label="分值添加时间" width="165" />
      <el-table-column prop="op_type" label="积分类型" min-width="40" show-overflow-tooltip>
        <template #default="scope">
          <span v-text="publicparams['idetifyScoreType'][scope.row.op_type.toString()]"></span>
        </template>
      </el-table-column>
      <el-table-column prop="number" label="分数" min-width="40" show-overflow-tooltip />
      <el-table-column prop="detail_name" label="类型明细" min-width="105" show-overflow-tooltip />
      <el-table-column prop="prove_material" label="证明图片" min-width="62" show-overflow-tooltip>
        <template #default="scope">
          <FileImage :file-url="scope.row.prove_material" lazy />
        </template>
      </el-table-column>
      <el-table-column prop="action_type" label="操作类型" min-width="85">
        <template #default="scope">
          <span
            v-text="scope.row.action_type == 1 || scope.row.action_type == '1' ? '手动' : '自动'"
          ></span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="count > 0"
      v-model:total="count"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="list"
    />
    <!-- 添加修改 -->
    <el-dialog
      v-model="dialog"
      v-if="dialog"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="cancel"
      top="5vh"
    >
      <el-form ref="ref" :rules="rules" :model="model" label-width="100px">
        <el-tabs>
          <el-tab-pane :label="selectTitle">
            <el-scrollbar native :height="height - 80">
              <!-- <el-form-item label="选择时间" prop="create_time">
			  <el-date-picker
				v-model="model.create_time"
				type="datetime"
				value-format="YYYY-MM-DD HH:mm:ss"
				:default-time="new Date(2024, 1, 1, 0, 0, 0)"
				placeholder="选择时间"
			  />
			</el-form-item> -->

              <div v-if="selectType == 'give'">
                <el-form-item
                  label="任务归属"
                  prop="getscore_reason"
                  v-for="(val, key) in model.taskList"
                >
                  <el-col :span="5">
                    <el-select v-model="val.getscore_reason" class="w-full" filterable>
                      <el-option
                        v-for="(item, index) in publicparams['idetifyScoreGiveReason']"
                        :key="index"
                        :label="item"
                        :value="index"
                      />
                    </el-select>
                  </el-col>
                  <el-col offset="2" :span="3">
                    <el-input v-model="val.number" type="number" placeholder="输入积分" />
                  </el-col>
                  <el-col :span="5" :offset="1">
                    <el-button @click="addPoints" type="primary">+</el-button>
                    <el-button @click="cancelPoints(key)" v-if="key > 0">-</el-button>
                  </el-col>
                </el-form-item>
              </div>
              <div v-else-if="selectType == 'consume'">
                <el-form-item
                  label="兑换商品"
                  prop="getscore_reason"
                  v-for="(val, key) in model.taskList"
                >
                  <el-col :span="6">
                    <el-input v-model="val.goods_name" placeholder="请输入商品名称" clearable />
                  </el-col>
                  <el-col offset="2" :span="4">
                    <el-input v-model="val.number" type="number" placeholder="输入积分" />
                  </el-col>
                  <el-col :span="5" :offset="1">
                    <el-button @click="addPoints" type="primary">+</el-button>
                    <el-button @click="cancelPoints(key)" v-if="key > 0">-</el-button>
                  </el-col>
                </el-form-item>
              </div>
              <div v-else>
                <el-form-item
                  label="冻结理由"
                  prop="freeze_reason"
                  v-for="(val, key) in model.taskList"
                >
                  <el-col :span="6">
                    <el-select v-model="val.freeze_reason" class="w-full" filterable>
                      <el-option
                        v-for="(item, index) in publicparams['idetifyScoreFrozenReason']"
                        :key="index"
                        :label="item"
                        :value="index"
                      />
                    </el-select>
                  </el-col>
                  <el-col offset="2" :span="4">
                    <el-input v-model="val.number" type="number" placeholder="输入积分" />
                  </el-col>
                </el-form-item>
              </div>
              <el-form-item label="材料证明" prop="prove_material">
                <ImgUploadPicUrl
                  source="0"
                  :isWatermark="model.is_watermark"
                  v-model="model.prove_material"
                  upload-btn="上传图片"
                  file-type="image"
                  file-tip=""
                />
              </el-form-item>
            </el-scrollbar>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <el-button :loading="loading" @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submit">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import screenHeight from '@/utils/screen-height'
import Pagination from '@/components/Pagination/index.vue'
import { arrayColumn } from '@/utils/index'
import { getPageLimit } from '@/utils/settings'
import { getScore, getScoreDetailList, addScoreDetail } from '@/api/member/memberscore'
export default {
  name: 'ZygwjfDetal',
  components: { Pagination },

  data() {
    return {
      name: '置业顾问积分',
      height: 680,
      loading: false,
      idkey: 'id',
      member_id: 0,
      identify_id: 0,
      identity_type: 2,
      exps: [{ exp: 'like', name: '包含' }],
      query: {
        page: 1,
        limit: getPageLimit(),
        member_id: 0
      },
      // 用户积分详情
      memberScore: {
        available_score: 0,
        freeze_score: 0,
        total_consume_score: 0,
        total_get_score: 0,
        update_time: 0,
        zygw: {
          service_name: ''
        }
      },

      // 公共参数
      publicparams: {},
      data: [],
      count: 0,
      dialog: false,
      dialogTitle: '',
      model: {
        member_id: '',
        identify_id: '',
        identify_type: '',
        taskList: [],
        prove_material: '',
        task_id: 0,
        task_sub_id: 0,
        action_type: 1,
        op_type: 0,
        create_time: 0,
        is_watermark: 0
      },
      rules: {
        // name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        // release_time: [{ required: true, message: '请选择发布时间', trigger: 'blur' }]
      },

      selection: [],
      selectIds: '',
      selectTitle: '操作',
      selectDialog: false,
      selectType: ''
    }
  },
  created() {
    this.height = screenHeight()
  },
  mounted() {
    this.member_id = this.$route.query.id // John
    this.query.member_id = this.member_id
    this.model.member_id = this.member_id

    this.identity_type = this.$route.query.identity_type // John
    this.query.identity_type = this.identity_type
    this.model.identity_type = this.identity_type

    this.getScore()
    this.list()

    if (this.identity_type == 3) {
      this.name = '选房师积分'
    }else if (this.identity_type == 4) {
      this.name = '主播积分'
    }
  },
  methods: {
    getScore() {
      var queryData = { member_id: this.member_id,identity_type: this.identity_type }
      getScore(queryData).then((res) => {
        this.memberScore = res.data
      })
    },
    // 列表
    list() {
      this.loading = true
      this.query.member_id = this.member_id
      this.query.identity_type = this.identity_type
      getScoreDetailList(this.query)
        .then((res) => {
          this.data = res.data.list
          this.count = res.data.count
          this.publicparams = res.data.publicparams
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 添加修改
    add() {
      this.dialog = true
      this.dialogTitle = this.name + '添加'
      this.reset()
      this.model.prove_material = ''
      console.log(this.model)
      this.model.taskList = []
      this.addPoints()
    },
    // 添加修改
    addPoints() {
      if (this.selectType == 'give') {
        var jsonArr = {
          getscore_reason: '1',
          number: 0
        }
        this.model.taskList.push(jsonArr)
      }
      if (this.selectType == 'consume') {
        var jsonArr = {
          goods_name: '',
          number: 0
        }
        this.model.taskList.push(jsonArr)
      }
      if (this.selectType == 'frozen') {
        var jsonArr = {
          freeze_reason: null,
          number: 0
        }
        this.model.taskList.push(jsonArr)
      }
    },
    cancelPoints(index) {
      this.model.taskList.splice(index, 1)
    },
    cancel() {
      this.dialog = false
      this.reset()
    },
    submit() {
      this.model.member_id = this.member_id
      this.model.identify_id = this.memberScore.zygw.id
      this.model.identity_type = this.identity_type
      if (this.selectType == 'frozen') {
        this.model.op_type = 3
      } else if (this.selectType == 'give') {
        this.model.op_type = 1
      } else {
        this.model.op_type = 2
      }
      console.log(this.model)
      this.$refs['ref'].validate((valid) => {
        if (valid) {
          addScoreDetail(this.model)
            .then((res) => {
              this.list()
              this.getScore()
              this.dialog = false
              ElMessage.success(res.msg)
            })
            .catch(() => {})
        } else {
          ElMessage.error('请完善必填项（带红色星号*）')
        }
      })
    },
    // 重置
    reset(row) {
      if (row) {
        this.model = row
      } else {
        this.model = this.$options.data().model
      }
    },

    // 刷新
    refresh() {
      const limit = this.query.limit
      this.query = this.$options.data().query
      this.$refs['table'].clearSort()
      this.query.limit = limit
      this.list()
    },
    // 排序
    sort(sort) {
      this.query.sort_field = sort.prop
      this.query.sort_value = ''
      if (sort.order === 'ascending') {
        this.query.sort_value = 'asc'
        this.list()
      }
      if (sort.order === 'descending') {
        this.query.sort_value = 'desc'
        this.list()
      }
    },
    // 操作
    select(selection) {
      this.selection = selection
      this.selectIds = this.selectGetIds(selection).toString()
    },
    selectGetIds(selection) {
      return arrayColumn(selection, this.idkey)
    },
    selectAlert() {
      ElMessageBox.alert('请选择需要操作的' + this.name, '提示', {
        type: 'warning',
        callback: () => {}
      })
    },
    selectOpen(selectType) {
      this.selectTitle = '操作'
      if (selectType === 'give') {
        this.selectTitle = '积分下发'
      } else if (selectType === 'consume') {
        this.selectTitle = '积分消耗'
      } else if (selectType === 'frozen') {
        this.selectTitle = '积分冻结'
      }
      this.selectType = selectType
      this.add()
    },
    timestampToDate(timestamp) {
      if (!timestamp) {
        return ''
      }
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      const seconds = date.getSeconds().toString().padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }
  }
}
</script>
