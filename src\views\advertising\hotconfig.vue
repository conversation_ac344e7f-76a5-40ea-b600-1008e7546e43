<template>
  <div class="app-container">
    <el-form ref="queryRef" :model="query" :inline="true" label-width="90px">
      <el-form-item label="标题：" prop="title">
        <el-input
            v-model="query.title"
            maxlength="20"
            placeholder="请输入标题"
            style="width: 140px"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" title="搜索" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
        <el-button @click="handleAdd">添加</el-button>
      </el-form-item>
    </el-form>

    <el-table ref="table" v-loading="loading" :data="data" :height="600">
      <el-table-column fixed prop="id" label="ID" width="80"/>
      <el-table-column prop="title" label="标题" min-width="150" placeholder="请输入标题，四个汉字"
                       show-overflow-tooltip/>
      <el-table-column prop="subtitle" label="副标题" min-width="150" placeholder="请输入标题，六个汉字"
                       show-overflow-tooltip/>
      <el-table-column label="跳转类型" min-width="100">
        <template #default="{ row }">
          <el-text>{{ row.jump_type === 1 ? '小程序' : 'H5' }}</el-text>
        </template>
      </el-table-column>
      <el-table-column prop="jump_url" label="跳转链接" min-width="150" show-overflow-tooltip/>
      <el-table-column prop="sort" label="顺序" width="80"/>
      <el-table-column label="状态" width="80">
        <template #default="{ row }">
          <el-tag :type="row.status ? 'success' : 'info'">
            {{ row.status ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="create_at" label="创建时间" min-width="150"/>
      <el-table-column prop="update_at" label="更新时间" min-width="150"/>

      <el-table-column fixed="right" label="操作" width="125">
        <template #default="scope">
          <el-link type="primary" class="mr-1" :underline="false" @click="handleEdit(scope.row)">
            编辑
          </el-link>
          <el-popconfirm title="确认删除该条数据吗？" @confirm="handleDelete(scope.row)">
            <template #reference>
              <el-link class="mr-1" type="danger" :underline="false">删除</el-link>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="count > 0"
        v-model:total="count"
        v-model:page="query.page"
        v-model:limit="query.limit"
        @pagination="getList"
    />

    <el-dialog
        v-model="dialogshow"
        :title="dialogTitle"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="dialogcancel"
        width="500px"
        top="5vh"
    >
      <el-form ref="formRef" :model="model" :rules="rules" label-width="120px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="model.title" placeholder="请输入标题" clearable/>
        </el-form-item>

        <el-form-item label="副标题" prop="subtitle">
          <el-input v-model="model.subtitle" placeholder="请输入副标题" clearable/>
        </el-form-item>

        <el-form-item label="跳转类型" prop="jump_type">
          <el-radio-group v-model="model.jump_type">
            <el-radio :label="1">小程序</el-radio>
            <el-radio :label="2">H5</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="跳转链接" prop="jump_url">
          <el-input v-model="model.jump_url" placeholder="请输入跳转链接" clearable/>
        </el-form-item>

        <el-form-item label="顺序" prop="sort">
          <el-input-number v-model="model.sort" :min="0" :max="999"/>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-switch
              v-model="model.status"
              :active-value="1"
              :inactive-value="0"
              active-text="启用"
              inactive-text="禁用"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button :loading="loading" @click="dialogcancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submit">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import {getPageLimit} from '@/utils/settings'
import {useRouter} from 'vue-router'
import Pagination from '@/components/Pagination/index.vue'
import {ElMessage} from 'element-plus'
import request from '@/utils/request'

const router = useRouter()
const loading = ref(false)
const dialogshow = ref(false)
const dialogTitle = ref("添加")
const data = ref([])
const formRef = ref(null)

// 表单校验规则
const rules = {
  title: [
    {required: true, message: '请输入标题', trigger: 'blur'},
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入标题'))
        } else {
          const chinese = /^[\u4e00-\u9fff\w]{1,4}$/u
          if (!chinese.test(value)) {
            callback(new Error('请输入1-4个汉字'))
          } else {
            callback()
          }
        }
      },
      trigger: 'blur'
    }
  ],
  subtitle: [
    {required: true, message: '请输入副标题', trigger: 'blur'},
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入副标题'))
        } else {
          const chinese = /^[\u4e00-\u9fff\w]{1,6}$/u
          if (!chinese.test(value)) {
            callback(new Error('请输入1-6个汉字'))
          } else {
            callback()
          }
        }
      },
      trigger: 'blur'
    }
  ],
  jump_type: [{required: true, message: '请选择跳转类型', trigger: 'change'}],
  jump_url: [{required: true, message: '请输入跳转链接', trigger: 'blur'}],
  sort: [{required: true, message: '请输入顺序', trigger: 'blur'}],
  status: [{required: true, message: '请选择状态', trigger: 'change'}]
}

// 重置的默认值
const resetmodel = ref({
  title: '',
  subtitle: '',
  jump_type: 1,
  jump_url: '',
  sort: 0,
  status: 1
})

// 表单数据
const model = ref({
  title: '',
  subtitle: '',
  jump_type: 1,
  jump_url: '',
  sort: 0,
  status: 1
})

const query = ref({
  page: 1,
  limit: getPageLimit(),
  title: ''
})

const count = ref(0)
const queryRef = ref(null)

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const res = await request({
      url: '/admin/advertise.HotConfig/list',
      method: 'get',
      params: query.value
    })
    if (res.code === 200) {
      data.value = res.data.list
      count.value = res.data.count
    } else {
      ElMessage.error(res.message || '获取列表失败')
    }
  } catch (error) {
    console.error('获取列表失败:', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}


// 删除
const handleDelete = async (row) => {
  try {
    const res = await request({
      url: '/admin/advertise.HotConfig/del',
      method: 'post',
      data: {id: row.id}
    })
    if (res.code === 200) {
      ElMessage.success('删除成功')
      getList()
    } else {
      ElMessage.error(res.message || '删除失败')
    }
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  }
}

// 关闭弹窗
const dialogcancel = () => {
  dialogshow.value = false
  formRef.value?.resetFields()
}

// 重置查询
const resetQuery = () => {
  queryRef.value.resetFields()
  handleQuery()
}

// 查询
const handleQuery = () => {
  query.value.page = 1
  getList()
}

// 添加
const handleAdd = () => {
  dialogTitle.value = "添加"
  model.value = JSON.parse(JSON.stringify(resetmodel.value))
  dialogshow.value = true
}

// 编辑
const handleEdit = async (row) => {
  dialogTitle.value = "编辑"
  try {
    const res = await request({
      url: `/admin/advertise.HotConfig/info?id=${row.id}`,
      method: 'get'
    })
    if (res.code === 200) {
      model.value = res.data
      dialogshow.value = true
    } else {
      ElMessage.error(res.msg || '获取详情失败')
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  }
}

// 提交
const submit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const res = await request({
          url: '/admin/advertise.HotConfig/save',
          method: 'post',
          data: model.value
        })
        if (res.code === 200) {
          ElMessage.success('保存成功')
          dialogshow.value = false
          getList() // 刷新列表
        } else {
          ElMessage.error(res.message || '保存失败')
        }
      } catch (error) {
        console.error('保存失败:', error)
        ElMessage.error('保存失败')
      } finally {
        loading.value = false
      }
    }
  })
}

onMounted(getList)
</script>

<style lang="scss" scoped>
.el-dialog {
  .el-form {
    padding: 0 20px;
  }
}
</style>