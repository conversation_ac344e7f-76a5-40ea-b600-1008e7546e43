<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-form ref="queryRef" :model="query" :inline="true" label-width="90px">
      <el-form-item label="更新时间：" prop="update_time">
        <el-date-picker
          v-model="query.update_time"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择时间"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="上线时间：" prop="online_time">
        <el-date-picker
          v-model="query.online_time"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择时间"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
        <el-button type="success" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格部分 -->
    <el-table
      v-if="flatData.length > 0"
      ref="tableRef"
      border
      v-loading="loading"
      :data="flatData"
      style="width: 100%; margin-top: 20px"
      row-key="id"
      :span-method="spanMethod"
    >
      <!-- 编号 (ID) column -->
      <el-table-column prop="id" label="编号" width="80" />

      <!-- 购房性质 (Purchase Nature) column -->
      <el-table-column prop="purchase_nature" label="购房性质" width="150">
        <template #default="{ row }">
          <span v-if="row.purchase_nature === 1">首套房</span>
          <span v-if="row.purchase_nature === 2">二套房</span>
          <span v-if="row.purchase_nature === 3">第三套及以上</span>
        </template>
      </el-table-column>

      <!-- 建筑面积 (Building Area) column -->
      <el-table-column prop="area" label="建筑面积" width="150" />

      <!-- 契税率 (Tax Rate) column -->
      <el-table-column prop="tax_rate" label="契税率" width="120">
        <template #default="{ row }">{{ row.tax_rate }} %</template>
      </el-table-column>

      <!-- 操作 (Operations) column -->
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleEdit(row)">编辑</el-button>
          <el-button
            link
            :type="row.status ? 'success' : 'primary'"
            @click="handleStatusChange(row)"
          >
            {{ row.status ? '下线' : '上线' }}
          </el-button>
          <el-button link type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      v-model:total="total"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="getList"
    />

    <!-- 编辑弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="150px">
        <!-- 更新时间 -->
        <el-form-item label="更新时间" prop="update_time">
          <el-date-picker
            v-model="form.update_time"
            type="date"
            placeholder="选择时间"
            value-format="YYYY-MM-DD"
            :disabled-date="() => false"
            style="width: 50%"
          />
        </el-form-item>

        <!-- 动态渲染房屋类型表单 -->
        <template v-for="(item, index) in houseTypes" :key="index">
          <el-form-item v-if="item.show" :label="item.label + '建筑面积'" :prop="item.areaProp">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-select
                  v-model="form.house_area_operator"
                  placeholder="请选择"
                  style="width: 100%; min-width: 120px"
                >
                  <el-option
                    v-for="opt in operatorOptions"
                    :key="opt.value"
                    :label="opt.label"
                    :value="opt.value"
                  />
                </el-select>
              </el-col>
              <el-col :span="16" v-if="index !== 2">
                <el-input-number
                  v-if="index !== 2"
                  v-model="form.area"
                  :precision="2"
                  :step="0.01"
                  :min="0"
                  placeholder="请输入面积"
                  style="width: 100%"
                />
              </el-col>
            </el-row>
          </el-form-item>

          <el-form-item v-if="item.show" :label="item.label + '契税率'" :prop="item.taxProp">
            <el-input-number
              v-model="form[item.taxKey]"
              :precision="2"
              :step="0.01"
              :min="0"
              style="width: 100%"
            >
              <template #append>%</template>
            </el-input-number>
          </el-form-item>

          <div v-if="!isEditMode" class="toggle-button-container">
            <el-button type="primary" @click="toggleHouseTax(item.toggleKey, item.type)">
              新增{{ item.label }}契税
            </el-button>
          </div>
        </template>

        <!-- 确定和取消按钮 -->
        <el-form-item class="submit-buttons">
          <el-button type="primary" @click="handleSubmit">确定</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Pagination from '@/components/Pagination/index.vue'
import request from '@/utils/request'

// 状态变量
const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('新增')
const formRef = ref(null)
const queryRef = ref(null)
const tableData = ref([])
const total = ref(0)

// 查询参数
const query = ref({
  page: 1,
  limit: 10,
  update_time: '',
  online_time: ''
})

// API调用
const api = {
  info: (params) => request.get('/admin/tools.DeedTaxCalculation/info', { params }),
  list: (params) => request.get('/admin/tools.DeedTaxCalculation/list', { params }),
  save: (data) => request.post('/admin/tools.DeedTaxCalculation/save', data),
  delete: (id) => request.get(`/admin/tools.DeedTaxCalculation/delete?id=${id}`),
  changeStatus: (id, status) =>
    request.get(`/admin/tools.DeedTaxCalculation/changeStatus`, { params: { id, status } })
}

// 当前房屋类型标识
const currentHouseType = ref('')

// 表单初始值
const initForm = reactive({
  update_time: '',
  house_area_operator: '',
  area: undefined,
  tax_rate: undefined,
  second_house_area_operator: '',
  second_house_area_value: undefined,
  second_house_tax_rate: undefined,
  third_house_area_operator: '',
  third_house_area_value: undefined,
  third_house_tax_rate: undefined
})
const form = ref({ ...initForm })
const isEditMode = ref(false)
// 房屋类型配置
const houseTypes = ref([
  {
    label: '首套房',
    show: false,
    areaProp: 'first_house_area',
    operatorKey: 'house_area_operator',
    valueKey: 'area',
    taxProp: 'tax_rate',
    taxKey: 'tax_rate',
    toggleKey: 'FirstHouseTax',
    type: 1
  },
  {
    label: '二套房',
    show: false,
    areaProp: 'second_house_area',
    operatorKey: 'house_area_operator',
    valueKey: 'area',
    taxProp: 'tax_rate',
    taxKey: 'tax_rate',
    toggleKey: 'SecondHouseTax',
    type: 2
  },
  {
    label: '第三套及以上',
    show: false,
    areaProp: 'third_house_area',
    operatorKey: 'house_area_operator',
    valueKey: 'area',
    taxProp: 'tax_rate',
    taxKey: 'tax_rate',
    toggleKey: 'ThirdHouseTax',
    type: 3
  }
])

// 操作符选项
const operatorOptions = [
  { label: '>', value: '>' },
  { label: '<', value: '<' },
  { label: '=', value: '=' },
  { label: '≥', value: '≥' },
  { label: '≤', value: '≤' },
  { label: '不限', value: '不限' }
]

// 表单验证规则
const rules = reactive({
  update_time: [{ required: true, message: '请选择更新时间', trigger: 'change' }],
  first_house_area: [
    { validator: (rule, value, callback) => validateArea(callback), trigger: 'blur' }
  ],
  second_house_area: [
    { validator: (rule, value, callback) => validateArea(callback), trigger: 'blur' }
  ],
  third_house_area: [
    { validator: (rule, value, callback) => validateArea(callback), trigger: 'blur' }
  ],
  tax_rate: [{ required: true, message: '请输入契税率', trigger: 'blur' }]
})

// 验证建筑面积
const validateArea = (callback) => {
  const operator = form.value.house_area_operator
  console.log(operator)
  const value = form.value.area
  // 当 operator 为 '不限' 时直接通过校验
  if (!operator || operator === '不限') {
    callback()
    return
  }

  // 其他情况继续原有校验逻辑
  if (value === undefined || value === null) {
    callback(new Error('请输入建筑面积'))
  } else {
    callback()
  }
}

// 解析后端返回的 area 数据
const parseAreaData = (areaStr) => {
  if (!areaStr || typeof areaStr !== 'string') return
  const operators = ['≥', '≤', '>', '<', '=', '不限'] // 支持的操作符
  for (const op of operators) {
    if (areaStr.startsWith(op)) {
      console.log('匹配的操作符:', op)
      form.value.house_area_operator = op
      form.value.area = parseFloat(areaStr.slice(op.length)) // 提取数值部分
      console.log('解析后的 form:', JSON.stringify(form.value)) // 调试赋值结果
      return
    }
  }
  if (areaStr === 'infinite') {
    form.value.house_area_operator = 'infinite'
    form.value.area = undefined
    console.log('解析后的 form:', JSON.stringify(form.value))
  }
}

// 加载编辑数据
const loadEditData = (data) => {
  if (!data) {
    console.error('后端数据为空')
    return
  }

  console.log('后端返回数据:', data) // 调试后端数据
  console.log('houseTypes:', houseTypes.value) // 调试房屋类型配置

  const { purchase_nature, area, tax_rate, update_time } = data
  // 转换为整数，确保类型一致
  const natureValue = parseInt(purchase_nature, 10)
  console.log('purchase_nature:', natureValue) // 调试 purchase_nature

  const houseItem = houseTypes.value.find((item) => item.type === natureValue)
  console.log('找到的 houseItem:', houseItem) // 调试匹配结果

  if (houseItem) {
    // 设置当前房屋类型并显示对应表单
    currentHouseType.value = houseItem.type
    houseItem.show = true
    console.log('-----', houseItem)

    // 解析 area 数据
    parseAreaData(area)

    // 设置其他字段
    form.value.tax_rate = parseFloat(tax_rate) // 转换为浮点数
    form.value.update_time = update_time.split(' ')[0] // 只取日期部分

    console.log('加载完成后的 form:', JSON.stringify(form.value)) // 调试最终 form
  } else {
    console.error(`未找到匹配的房屋类型，purchase_nature: ${natureValue}`)
  }
}

// 切换显示状态（限制只能选择一个，并设置当前房屋类型）
const toggleHouseTax = (toggleKey, houseType) => {
  const currentItem = houseTypes.value.find((h) => h.toggleKey === toggleKey)
  if (currentItem) {
    if (currentItem.show) {
      currentItem.show = false
      currentHouseType.value = ''
    } else {
      houseTypes.value.forEach((item) => {
        item.show = item.toggleKey === toggleKey
      })
      currentHouseType.value = houseType
    }
    console.log('当前房屋类型:', currentHouseType.value)
  }
}

// 取消操作
const handleCancel = () => {
  // form.value = { ...initForm }; // 重置为初始值
  form.value = { ...initForm } // 重置为初始值
  formRef.value.resetFields()
  houseTypes.value.forEach((item) => (item.show = false))
  currentHouseType.value = ''
  dialogVisible.value = false
}
// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const res = await api.list(query.value)
    if (res.code === 200) {
      tableData.value = res.data.list
      total.value = res.data.total
    }
  } catch (error) {
    console.error('获取列表失败:', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

// 查询处理
const handleQuery = () => {
  query.value.page = 1
  getList()
}

// 重置查询
const resetQuery = () => {
  queryRef.value?.resetFields()
  query.value = {
    page: 1,
    limit: 10,
    update_time: '',
    online_time: ''
  }
  getList()
}
// 平铺数据，按 purchase_nature 排序
const flatData = computed(() => {
  return [...tableData.value].sort((a, b) => a.purchase_nature - b.purchase_nature)
})

// 分组信息，用于合并列
const groupInfo = computed(() => {
  const info = {}
  flatData.value.forEach((item, index) => {
    const key = item.purchase_nature
    if (!info[key]) {
      info[key] = { count: 0, startIndex: index }
    }
    info[key].count++
  })
  return info
})

// 合并列逻辑
const spanMethod = ({ row, rowIndex, columnIndex }) => {
  if (columnIndex === 1) {
    // 购房性质列
    const key = row.purchase_nature
    const group = groupInfo.value[key]
    if (group && rowIndex === group.startIndex) {
      return { rowspan: group.count, colspan: 1 } // 分组第一行合并
    } else {
      return { rowspan: 0, colspan: 0 } // 后续行隐藏
    }
  }
  return { rowspan: 1, colspan: 1 } // 其他列保持默认
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增'
  form.value = { ...initForm }
  dialogVisible.value = true
  houseTypes.value.forEach((item) => (item.show = false))
  currentHouseType.value = ''
  isEditMode.value = false // 设置为新增模式
}

// 编辑
const handleEdit = (row) => {
  isEditMode.value = true // 设置为编辑模式
  houseTypes.value.forEach((item) => (item.show = false))
  currentHouseType.value = ''
  form.value = { ...initForm } // 重置为初始值
  dialogTitle.value = '编辑'
  dialogVisible.value = true
  api.info({ id: row.id }).then((res) => {
    if (res.code === 200) {
      form.value = res.msg
      console.log('------*', res)
      loadEditData(form.value)
    } else {
      console.error('获取列表失败:', error)
      ElMessage.error('获取列表失败')
    }
  })
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除该记录吗？`, '提示', {
      type: 'warning'
    })
    const res = await api.delete(row.id)
    if (res.code === 200) {
      ElMessage.success('删除成功')
      getList()
    }
  } catch (error) {
    console.error('删除失败:', error)
  }
}

// 状态变更
const handleStatusChange = async (row) => {
  try {
    const newStatus = row.status ? 0 : 1
    const res = await api.changeStatus(row.id, newStatus)
    if (res.code === 200) {
      ElMessage.success(`${newStatus ? '上线' : '下线'}成功`)
      getList()
    }
  } catch (error) {
    console.error('状态更改失败:', error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  console.log(currentHouseType.value)
  try {
    submitLoading.value = true

    await formRef.value.validate()
    form.value.house_type = currentHouseType.value
    const res = await api.save(form.value)
    if (res.code === 200) {
      ElMessage.success(form.value.id ? '编辑成功' : '新增成功')
      dialogVisible.value = false
      getList()
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitLoading.value = false
  }
}

// 初始化
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.dialog-footer {
  padding: 20px 0 0;
  text-align: right;
}

.toggle-button-container {
  margin-bottom: 20px;
}

.toggle-button-container .el-button {
  width: 150px;
}
</style>
