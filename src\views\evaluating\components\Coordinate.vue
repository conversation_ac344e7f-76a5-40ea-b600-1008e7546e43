<template>
  <el-text class="flex flex-items-center">
    芝麻选房小程序前台使用腾讯地图进行展示，请在
    <el-link class="custom-link" href="https://lbs.qq.com/getPoint/" target="_blank">
      腾讯地图
    </el-link>
    进行经纬度数据的获取，以便获得最精确的位置信息
  </el-text>
  <div class="mt-[20px]">
    <el-form
      ref="formref"
      :model="coordinate"
      :rules="rules"
      label-width="120px"
      label-position="left"
    >
      <el-form-item label="纬度" prop="lat">
        <el-col :span="6">
          <el-input v-model="coordinate.lat" type="text" />
        </el-col>
      </el-form-item>
      <el-form-item label="经度" prop="lont">
        <el-col :span="6">
          <el-input v-model="coordinate.lont" type="text" />
        </el-col>
      </el-form-item>
      <el-form-item>
        <el-button :loading="loading" type="primary" @click="submit">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import * as Evaluating from '@/api/project/evaluating'
const houseInfo = defineProps({ house: { type: Object } })

const coordinate = ref({ lont: '', lat: '', projectID: houseInfo.house.id })

const loading = ref(false)
const formref = ref(null)

const query = ref({
  projectID: houseInfo.house.id
})
const validateDecimal = (rule, value, callback) => {
  const regex = /^\d+(\.\d+)?$/
  if (!value || !regex.test(value)) {
    return callback(new Error('请输入有效的小数'))
  }
  callback()
}
const rules = {
  lat: [
    { required: true, message: '纬度不能为空', trigger: ['blur', 'change'] },
    { validator: validateDecimal, trigger: ['blur', 'change'] }
  ],
  lont: [
    { required: true, message: '经度不能为空', trigger: ['blur', 'change'] },
    { validator: validateDecimal, trigger: ['blur', 'change'] }
  ]
}

function info() {
  Evaluating.coordinateInfo(query.value).then((res) => {
    let arr = Object.keys(res.data)

    if (arr.length !== 0) {
      coordinate.value.lat = res.data.lat
      coordinate.value.lont = res.data.lont
    }
  })
}
// 提交
function submit() {
  formref.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      Evaluating.coordinateEdit(coordinate.value)
        .then((res) => {
          loading.value = false
          ElMessage.success(res.msg)
        })
        .catch(() => {
          loading.value = false
        })
    }
  })
}

onMounted(() => {
  info()
})
</script>
<style lang="scss" scoped>
.custom-link {
  color: var(--el-color-primary);
}
.custom-link.el-link--primary:focus,
.custom-link.el-link--primary:hover,
.custom-link.el-link--primary:active {
  color: inherit;
  text-decoration: none;
}
</style>
