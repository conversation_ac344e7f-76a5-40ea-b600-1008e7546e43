<template>
  <div class="app-container">
    <!-- 查询 -->
	<el-form ref="queryRef" :model="query" :inline="true" label-width="90px">
		<el-form-item label="主播：">
		  <el-input
		    v-model="query.service_name"
		    class="ya-search-value"
		    placeholder="请输入主播名称"
		    clearable
		  />
		</el-form-item>
		
		<el-form-item label="任务批次：">
		  <el-input
		    v-model="query.batch_no"
		    class="ya-search-value"
		    placeholder="任务批次"
		    clearable
		  />
		</el-form-item>
		
		<el-form-item label="审核状态：">
			<!-- 1未处理 2已审核 4无效 -->
		  <el-select v-model="query.state" style="width: 120px" clearable>
		    <el-option key="1" label="未处理" value="1" />
			<el-option key="2" label="已审核" value="2" />
			<el-option key="4" label="无效" value="4" />
		  </el-select>
		</el-form-item>
		<el-form-item label="任务类型：">
		  <el-select v-model="query.batch_task_type" style="width: 120px" clearable placeholder="全部">
		    <el-option :key="Number(key)" :label="item" :value="Number(key)" v-for="(item,key) in publicparams['idetifyScoreTaskType']" />
		    
		  </el-select>
		</el-form-item>
	</el-form>
    <el-row>
      <el-col class="mb-2">
		
        <el-select v-model="query.date_field" class="ya-date-field" placeholder="时间类型">
          <el-option value="create_time" label="提交时间" />
          <el-option value="review_time" label="审核时间" />
        </el-select>
        <el-date-picker
          v-model="query.date_value"
          type="datetimerange"
          class="ya-date-value"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="[new Date(2024, 1, 1, 0, 0, 0), new Date(2024, 1, 1, 23, 59, 59)]"
        />
		&nbsp;&nbsp;
        <el-button type="primary" @click="search()">查询</el-button>
        <el-button title="重置" @click="refresh()">
          <svg-icon icon-class="refresh" />
        </el-button>
      </el-col>
    </el-row>
    
    
    <!-- 列表 -->
    <el-table
      ref="table"
      v-loading="loading"
      :data="data"
      :height="height"
      @sort-change="sort"
      @selection-change="select"
    >
      <el-table-column type="selection" width="42" title="全选/反选" />
      <el-table-column :prop="idkey" label="ID" width="80" sortable="custom" />
      <el-table-column prop="batch_task_type" label="任务类型" min-width="105" show-overflow-tooltip >
	  
	  <template #default="scope">
	      <span v-text="publicparams['idetifyScoreTaskType'][scope.row.batch_task_type.toString()]"></span>
	    </template>
	  </el-table-column>
	  
      <el-table-column prop="batch_no" label="批次" min-width="85" show-overflow-tooltip />
      <el-table-column prop="create_time" label="提交时间" min-width="85" show-overflow-tooltip >
	  <template #default="scope">
	      <span v-text="scope.row.create_time"></span>
	    </template>
	  </el-table-column>
      <el-table-column prop="service_name" label="主播" min-width="105" show-overflow-tooltip />
	  <el-table-column label="审核状态" min-width="85" show-overflow-tooltip >
	    <template #default="scope">
	      <span v-text="publicparams['idetifyTaskReviewState'][scope.row.state.toString()]"></span>
	    </template>
	  </el-table-column>
      <el-table-column label="操作" width="135">
        <template #default="scope">
          <el-link v-if="scope.row.state==1" type="primary" class="mr-1" :underline="false" @click="editCocreate(scope.row,true)">
            审核
          </el-link>
		
		  <!-- <el-link v-if="scope.row.state==2" type="primary" class="mr-1" :underline="false" @click="reviewLog(scope.row)">
		    审核记录
		  </el-link> -->
		  <el-link v-if="scope.row.state==2" type="primary" class="mr-1" :underline="false" @click="editCocreate(scope.row,false)">
		    审核记录
		  </el-link>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="count > 0"
      v-model:total="count"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="list"
    />
    <!-- 添加修改 -->
    <!-- 添加修改 -->
    <el-dialog
          v-model="dialog"
          :title="dialogTitle"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          :before-close="cancel"
          top="5vh"
        >
          <el-form ref="ref" :rules="rules" :model="model" label-width="100px">
            <el-tabs>
              <el-tab-pane label="信息">
				  <el-divider>
				    <h3 class="bold">任务信息</h3>
				  </el-divider>
                <el-scrollbar native :height="height - 80">
                  
                  <el-form-item label="主播">
                    <el-input v-model="rowInfo.service_name" disabled />
                  </el-form-item>
                  <el-form-item label="任务类型">
                    <el-input v-model="publicparams['idetifyScoreTaskType'][rowInfo.batch_task_type.toString()]"  disabled/>
                  </el-form-item>
                  <!-- <el-form-item label="任务名称">
                    <el-input v-model="publicparams['basicTaskFlects'][rowInfo.task_id]" disabled />
                  </el-form-item> -->
                  <el-form-item  label="提交时间">
					  <template #default="scope">
					      <span v-text="rowInfo.create_time"></span>
					    </template>
                  </el-form-item>
                  <el-divider>
                    <h3 class="bold">附件</h3>
                  </el-divider>
				  <!-- 楼盘信息纠错 -->
				  <div v-for="(value, key) in model">
					  <!-- 提供新盘信息 -->
					  <div v-if="value.task_id==38">
						  <el-form-item label="新楼盘信息">
							<el-col :span="12">
								<el-input v-model="value.house_name" disabled />
							</el-col>
							 <el-col :span="6" :offset="1">
								
							</el-col>
						  </el-form-item>
						  <el-form-item label="是否通过" prop="state">
						    <el-radio-group v-model="value.state" @change="changeReviewState(key)">
						      <el-radio :value="2">通过</el-radio>
						      <el-radio :value="3">不通过</el-radio>
						    </el-radio-group>
						  </el-form-item> 
						  <div v-if="value.state == 2">
							  <el-form-item label="输入积分" prop="score">
							    <el-input v-model="value.score"  type="number" min="1"/>
							  </el-form-item>
							  <el-form-item label="选择加分数量" >
							    <el-input v-model="value.plusCount" type="number" min="1" />
							  </el-form-item>
						  </div>
						
						<el-form-item label="审核理由" v-else>
						  <el-input v-model="value.review_mark"  />
						</el-form-item>   
					  </div>
					  <!-- 提供楼盘图片 -->
					  
					  <div v-else-if="value.task_id==39">
						  <el-form-item label="图片类型">
						    <el-input v-model="housetypes[value.img_type.toString()]" disabled />
						  </el-form-item> 
						 <el-form-item label="楼盘图片">
							 <el-col :span="4">
							 <el-image
							   style="height: 91px"
							   :src="value.img_url"
							   :fit="fit"
							   :lazy="lazy"
							   title="点击上传"
							 >
							   <template #error>
							     <svg-icon icon-class="picture" />
							   </template>
							 </el-image>
							 </el-col>
							 <el-col :span="4" :offset="1">
								<el-radio-group v-model="value.state" @change="changeReviewState(key)" prop="state">
								  <el-radio :value="2">通过</el-radio>
								  <el-radio :value="3">不通过</el-radio>
								</el-radio-group>
						   </el-col>
						 </el-form-item>
						 <el-form-item label="输入积分" prop="score" v-if="value.state == 2">
						   <el-input v-model="value.score"  />
						 </el-form-item>
						 <el-form-item label="审核理由" prop="review_mark" v-else>
						   <el-input v-model="value.review_mark"  />
						 </el-form-item>
					  </div>
					  <!-- 楼盘信息纠错 -->
					  <div v-else-if="value.task_id==40">
						 <el-form-item :label="'楼盘纠错'">
						 <el-col :span="4">
						 <el-image
						   style="height: 91px"
						   :src="value.img_url"
						   :fit="fit"
						   :lazy="lazy"
						   title="点击上传"
						 >
						   <template #error>
							 <svg-icon icon-class="picture" />
						   </template>
						 </el-image>
						 </el-col>
						 <el-col :span="4" :offset="1">
							<el-radio-group v-model="value.state" @change="changeReviewState(key)" prop="state">
							  <el-radio :value="2">通过</el-radio>
							  <el-radio :value="3">不通过</el-radio>
							</el-radio-group>
						   </el-col>
						 </el-form-item>
						 <el-form-item label="纠错理由">
							 
						   <el-input v-model="value.mark" disabled />
						 </el-form-item>
						 <el-form-item label="输入积分" prop="score" v-if="value.state == 2">
							<el-col :span="12">
								<el-input v-model="value.score" type="number" min="1" />
							</el-col>
						 </el-form-item>
						 <el-form-item label="审核理由" prop="review_mark" v-else>
							<el-col :span="12">
								<el-input v-model="value.review_mark"  />
						    </el-col>
						 </el-form-item>			  
					  </div>
					  <!-- 置业顾问清理 -->
					  <div v-else-if="value.task_id==41">
						  <el-form-item label="楼盘名称">
						    <el-input v-model="houseInfo.name"  disabled/>
						  </el-form-item>
						 <el-form-item label="置业顾问清理">
							<el-col :span="7">
								<el-select
								  v-model="value.identity_id"
								  class="w-full"
								  disabled
								>
								  <el-option
								    :key="value.identity_id"
								    :label="identitys[value.identity_id.toString()].service_name"
								    :value="value.identity_id"
								  />
								</el-select>
							</el-col>
							 <el-col :span="8" :offset="1">
								<el-radio-group v-model="value.state" @change="changeReviewState(key)" prop="state">
								  <el-radio :value="2">通过</el-radio>
								  <el-radio :value="3">不通过</el-radio>
								</el-radio-group>
							</el-col>
						</el-form-item>	
						<el-form-item label="清理原因" prop="mark" >
						  <el-input v-model="value.mark" disabled />
						</el-form-item>		
						<el-form-item label="输入分数" prop="score" v-if="value.state == 2">
						  <el-input v-model="value.score" type="number" min="1"  />
						</el-form-item>
						<el-form-item label="审核理由" prop="review_mark" v-else>
						  <el-input v-model="value.review_mark"  />
						</el-form-item>			  
					  </div>
					  
					  <!-- 其他任务 -->
					  <div v-else>
							<el-form-item :label="value.detail_name">
							 <el-col :span="4">
							 <el-image
							   style="height: 91px"
							   :src="value.img_url"
							   :fit="fit"
							   :lazy="lazy"
							   title="点击上传"
							 >
							   <template #error>
								 <svg-icon icon-class="picture" />
							   </template>
							 </el-image>
							 </el-col>
							 <el-col :span="4" :offset="1">
								<el-radio-group v-model="value.state" @change="changeReviewState(key)" prop="state">
								  <el-radio :value="2">通过</el-radio>
								  <el-radio :value="3">不通过</el-radio>
								</el-radio-group>
							  </el-col>
							</el-form-item>
							<el-form-item label="输入积分" prop="score" v-if="value.state == 2">
								<el-col :span="8" :offset="1">
								<el-input v-model="value.score" type="number" min="1"/>
								</el-col>
							</el-form-item>
							<el-form-item label="审核理由" prop="review_mark" v-else>
								<el-col :span="16">
									<el-input v-model="value.review_mark"   />
								</el-col>
							</el-form-item>		  
					  </div>
					   <el-divider></el-divider>
				  </div>
                </el-scrollbar>
              </el-tab-pane>
             
            </el-tabs>
          </el-form>
          <template #footer>
            <el-button :loading="loading" @click="cancel">取消</el-button>
            <el-button :loading="loading" type="primary" @click="submit" v-if="scan==true">提交</el-button>
          </template>
        </el-dialog>
	
  </div>
</template>

<script>
import screenHeight from '@/utils/screen-height'
import Pagination from '@/components/Pagination/index.vue'
import { arrayColumn } from '@/utils/index'
import { getPageLimit } from '@/utils/settings'
import {
  tasklist,
  bachNoInfo,
  bachReviewTask
} from '@/api/member/membertask'

export default {
  name: '主播任务列表',
  components: { Pagination},

  data() {
    return {
      name: '主播任务列表',
      height: 680,
      loading: false,
      idkey: 'id',
	  scan:false, // 是否查看审核记录
      exps: [{ exp: 'like', name: '包含' }],
      query: {
        page: 1,
		identity_type:4,
        service_name:null,
        review_state: 0,
        state: null,
        limit: getPageLimit(),
        date_field: 'create_time'
      },
	  identitys:{}, // 要清理的职业顾问信息
	  houseInfo:{}, // 要清理的职业顾问信息
	  housetypes:{}, // 要清理的职业顾问信息
      data: [],
      count: 0,
      dialog: false,
      dialogTitle: '',
	  rowInfo:{},
      model: {},
      rules: {
		  // state: [{ required: true, message: '请选择审核状态', trigger: 'blur' }],
		  // score: [{ required: true, message: '请输入积分', trigger: 'blur' }],
		  // review_mark: [{ required: true, message: '请输审核理由', trigger: 'blur' }],
		  // tasklist:[]
	  },
      selection: [],
      selectIds: '',
      selectTitle: '操作',
      selectDialog: false,
	  addoptionDialog:false,
	  publicparams:{}
    }
  },
  created() {
    this.height = screenHeight()
    this.list()
  },
  methods: {
	
	// 验证最大总价是否大于最小总价
	validMaxTotalPrice(){
		return this.model.max_total_price >= this.model.min_total_price
	},
    // 列表
    list() {
      this.loading = true
      tasklist(this.query)
        .then((res) => {
          this.data = res.data.list
		  this.count = res.data.count
		  this.publicparams = res.data.publicparams
		  this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },

    editCocreate(row,scan=false) {
		this.scan=scan
	  this.rowInfo = row
      this.dialog = true
      this.dialogTitle = this.name + '审核：' + row.batch_no
      let paramdata = {"batch_no":row.batch_no,"scan":scan}
      bachNoInfo(paramdata)
        .then((res) => {
			this.model = res.data.list
			this.identitys = res.data.identitys
			this.houseInfo = res.data.house_info
			this.housetypes = res.data.housetypes
        })
        .catch(() => {})
		
    },
    cancel() {
      this.dialog = false
      this.reset()
    },
    submit() {
		this.$refs['ref'].validate((valid) => {
		  if (valid) {
				bachReviewTask(this.model)
				  .then((res) => {
					this.list()
					this.reset()
					this.dialog = false
					ElMessage.success(res.msg)
				  })
				  .catch(() => {
					ElMessage.success(res.msg)
				})
			} else {
				ElMessage.error('请完善必填项（带红色星号*）')
			}
		})
    },
	changeReviewState(index){
		
		if(this.model[index].state==2){
			if(this.scan){
				this.model[index].review_mark  = ""
			}
			
		}else{
			if(this.scan){
				this.model[index].score  = 0
			}
		}
	},
	reviewLog(row){
		
		var url = "/member/zygwjf?id="+row.member_id+"&identity_type=4";
		this.$router.push(url)
	},
    // 重置
    reset(row) {
      if (row) {
        this.model = row
      } else {
		console.log(this.$options.data())
        this.model = this.$options.data().model
      }

      if (this.$refs['ref'] !== undefined) {
        try {
          this.$refs['ref'].resetFields()
          this.$refs['ref'].clearValidate()
        } catch (error) {
			console.log(error)
		}
      }
	  
    },
    // 查询
    search() {
      this.query.page = 1
      this.list()
    },
    // 刷新
    refresh() {
      const limit = this.query.limit
      this.query = this.$options.data().query
      this.$refs['table'].clearSort()
      this.query.limit = limit
      this.list()
    },
    // 排序
    sort(sort) {
      this.query.sort_field = sort.prop
      this.query.sort_value = ''
      if (sort.order === 'ascending') {
        this.query.sort_value = 'asc'
        this.list()
      }
      if (sort.order === 'descending') {
        this.query.sort_value = 'desc'
        this.list()
      }
    },
    // 操作
    select(selection) {
      this.selection = selection
      this.selectIds = this.selectGetIds(selection).toString()
    },
    selectGetIds(selection) {
      return arrayColumn(selection, this.idkey)
    },
    selectAlert() {
      ElMessageBox.alert('请选择需要操作的' + this.name, '提示', {
        type: 'warning',
        callback: () => {}
      })
    },
	timestampToDate(timestamp) {
	  const date = new Date(timestamp);
	  const year = date.getFullYear();
	  const month = (date.getMonth() + 1).toString().padStart(2, '0');
	  const day = date.getDate().toString().padStart(2, '0');
	  const hours = date.getHours().toString().padStart(2, '0');
	  const minutes = date.getMinutes().toString().padStart(2, '0');
	  const seconds = date.getSeconds().toString().padStart(2, '0');
	  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
	}
  }
}
</script>
<style>
.bordered-box {
  border: 1px solid #d3dce6; /* 设置边框样式 */
  border-radius: 4px; /* 可选：设置圆角 */
  padding: 10px; /* 内边距 */
  box-sizing: border-box; /* 确保边框不影响内部内容的宽度 */
  margin-top: 10px;
}
</style>
