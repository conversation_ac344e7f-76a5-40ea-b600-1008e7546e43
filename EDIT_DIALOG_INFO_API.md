# 编辑对话框数据回显功能实现说明

## 功能需求

点击编辑按钮时：
1. 通过 `info` 接口获取完整的表单数据
2. 回显表单数据到编辑对话框
3. 获取详细评测列表数据
4. 获取亮点功能列表数据
5. 根据 `info` 接口返回的字段默认勾选亮点功能表格的复选框

## 实现方案

### 1. API接口添加

在 `src/api/intelligent/intelligentevaluation.js` 中添加了 `info` 接口：

```javascript
/**
 * 智能评估详情
 * @param {array} params 请求参数，包含id
 */
export function info(params) {
  return request({
    url: url + 'info',
    method: 'get',
    params: params
  })
}
```

### 2. 编辑对话框流程重构

#### 修改前的流程
```javascript
function openEditDialog(row) {
  // 直接使用列表数据
  Object.assign(formData, { ...row })
  dialogVisible.value = true
  // 加载子数据
  fetchProductList(formData.id)
  getLightList({ intellect_id: row.id })
}
```

#### 修改后的流程
```javascript
function openEditDialog(row) {
  dialogTitle.value = '修改' + name
  isEditMode.value = true
  dialogVisible.value = true
  loading.value = true

  // 1. 通过info接口获取完整数据
  info({ id: row.id })
    .then(res => {
      const data = res.data || {}
      Object.assign(formData, data)
      
      // 2. 数据格式处理
      // 处理图片数据
      // 处理默认值
      
      // 3. 加载子数据
      fetchProductList(formData.id)
      return getLightList({ intellect_id: row.id })
    })
    .then(res => {
      // 4. 处理亮点数据回显
      highlightsData.value = res.data || []
      // 5. 恢复复选框选中状态
      restoreHighlightSelection()
    })
    .catch(err => {
      ElMessage.error('获取数据失败')
    })
    .finally(() => {
      loading.value = false
    })
}
```

### 3. 亮点复选框回显逻辑

#### 数据格式处理
```javascript
// info接口返回的selectedHighlights可能是分组格式
// {scene_type_raw: [id1, id2]} 或者 [id1, id2]

let selectedIds = []

if (Array.isArray(formData.selectedHighlights)) {
  // 如果是数组格式，直接使用
  selectedIds = formData.selectedHighlights
} else if (typeof formData.selectedHighlights === 'object') {
  // 如果是分组格式，提取所有ID
  selectedIds = Object.values(formData.selectedHighlights).flat()
}
```

#### 复选框状态恢复
```javascript
setTimeout(() => {
  if (highlightTableRef.value && selectedIds.length > 0) {
    // 清除所有选中状态
    highlightTableRef.value.clearSelection()
    
    // 根据ID恢复选中状态
    highlightsData.value.forEach(row => {
      if (selectedIds.includes(row.id)) {
        highlightTableRef.value.toggleRowSelection(row, true)
        console.log('恢复选中亮点:', row.name, row.id)
      }
    })
    
    // 更新formData.selectedHighlights为选中的完整行对象
    const selectedRows = highlightsData.value.filter(row => selectedIds.includes(row.id))
    formData.selectedHighlights = selectedRows
  }
}, 100)
```

## 数据流程图

```
点击编辑按钮
    ↓
显示对话框 + 显示loading
    ↓
调用info接口获取详细数据
    ↓
回显基础表单数据
    ↓
并行加载子数据:
├── fetchProductList() - 获取详细评测列表
└── getLightList() - 获取亮点功能列表
    ↓
处理亮点数据回显:
├── 解析selectedHighlights格式
├── 提取需要选中的ID数组
└── 恢复表格复选框选中状态
    ↓
隐藏loading，完成数据加载
```

## 关键技术点

### 1. 异步数据加载顺序
```javascript
info({ id: row.id })
  .then(res => {
    // 先处理基础数据
    Object.assign(formData, res.data)
    
    // 再加载依赖数据
    fetchProductList(formData.id)
    return getLightList({ intellect_id: row.id })
  })
  .then(res => {
    // 最后处理UI状态
    restoreHighlightSelection()
  })
```

### 2. 数据格式兼容性
支持多种 `selectedHighlights` 格式：
- 数组格式：`[1, 2, 3]`
- 分组格式：`{"security": [1], "lighting": [2, 3]}`
- 空值处理：`undefined`、`null`、`[]`

### 3. 表格状态管理
```javascript
// 延迟执行确保表格已渲染
setTimeout(() => {
  if (highlightTableRef.value) {
    // 先清除再设置，避免状态冲突
    highlightTableRef.value.clearSelection()
    // 逐行设置选中状态
    highlightTableRef.value.toggleRowSelection(row, true)
  }
}, 100)
```

### 4. Loading状态管理
```javascript
// 开始时显示loading
loading.value = true

// 所有异步操作完成后隐藏loading
.finally(() => {
  loading.value = false
})
```

## 后端接口要求

### info接口返回数据格式
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "id": 1,
    "house_id": 123,
    "short_comment": "智能化程度很高",
    "count_score": 95,
    "level": "1",
    "cover_score": 90,
    "fun_score": 88,
    "intellect_pics": [
      {
        "file_url": "https://example.com/image1.jpg",
        "file_name": "image1.jpg"
      }
    ],
    "fun_img_url": "https://example.com/fun.jpg",
    "experience": "实拍体验内容",
    "brand_info": "品牌资料内容",
    "selectedHighlights": {
      "security": [1],
      "lighting": [2, 3]
    }
  }
}
```

### 关键字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| selectedHighlights | Object/Array | 已选中的亮点，支持分组格式或ID数组 |
| intellect_pics | Array | 智能图集，包含file_url和file_name |
| fun_img_url | String | 功能展示图片URL |
| experience | String | 实拍体验内容 |
| brand_info | String | 品牌资料内容 |

## 测试验证

### 1. 基础数据回显测试
1. 点击编辑按钮
2. 确认表单字段正确回显
3. 确认图片正确显示
4. 确认各个tab页数据正确

### 2. 亮点复选框回显测试
1. 切换到"功能亮点"tab
2. 确认之前选中的亮点正确显示为选中状态
3. 确认未选中的亮点显示为未选中状态

### 3. 子数据加载测试
1. 切换到"详细评测"tab
2. 确认产品列表正确加载
3. 确认数据与当前记录关联

### 4. 错误处理测试
1. 模拟接口错误
2. 确认错误提示正确显示
3. 确认loading状态正确隐藏

## 调试信息

开发时可以通过控制台查看以下调试信息：
```
info接口返回的数据: {id: 1, selectedHighlights: {...}, ...}
formData.selectedHighlights: {...}
需要回显的亮点IDs: [1, 2, 3]
恢复选中亮点: 智能安防 1
恢复选中亮点: 智能照明 2
回显后的selectedHighlights: [{id: 1, name: '智能安防', ...}]
```

## 注意事项

1. **接口调用顺序**：必须先调用info接口获取基础数据，再加载子数据
2. **异步处理**：使用Promise链式调用确保数据加载顺序
3. **表格渲染时序**：复选框状态恢复需要延迟执行
4. **数据格式兼容**：支持多种selectedHighlights格式
5. **错误处理**：完整的错误处理和用户提示
6. **Loading状态**：合理的loading状态管理提升用户体验

现在编辑对话框可以正确通过info接口获取数据并完成所有回显功能了！
