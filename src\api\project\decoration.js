import request from '@/utils/request'
// 内容管理
const url = '/admin/evaluating.Decoration/'
/**
 * 内容列表
 * @param {array} params 请求参数
 */
export function list(params) {
  return request({
    url: url + 'list',
    method: 'get',
    params: params
  })
}
/**
 * 内容发布时间
 * @param {array} data 请求数据
 */
export function release(data) {
    return request({
      url: url + 'release',
      method: 'post',
      data
    })
  }

  /**
 * 内容信息
 * @param {array} params 请求参数
 */
export function info(params) {
    return request({
      url: url + 'info',
      method: 'get',
      params: params
    })
  }


  /**
 * 内容修改
 * @param {array} data 请求数据
 */
export function edit(data) {
    // 这里需要判断是添加还是修改，所以需要判断id是否存在，存在则为修改，否则为添加
    // 如果id存在，则为修改，否则为添加
    return request({
      url: url + 'edit',
      method: 'post',
      data
    })
  }

