<template>
  <el-scrollbar native class="wrapper">
    <el-form :model="model" ref="houseBase" :rules="houseBaseRules" label-width="120px">
      <el-row>
        <el-col :span="11">
          <el-card style="height: 100%" header="基础信息" shadow="hover">
            <el-form-item label="类型" prop="is_land">
              <el-radio-group
                v-model="model.is_land"
                style="margin-left: 10px"
                @change="houseTypeChange"
              >
                <el-radio label="楼盘" :value="0"></el-radio>
                <el-radio label="地块" :value="1" :disabled="canbeLand"></el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="楼盘名称" prop="name">
              <el-input v-model="model.name" />
            </el-form-item>
            <el-form-item v-if="is_show_ext == 1" label="楼盘拼音">
              <el-input v-model="model.ename" />
            </el-form-item>
            <el-form-item label="楼盘别名">
              <div>
                <el-tag
                  v-for="(tag, index) in model.alias_name"
                  :key="index"
                  closable
                  @close="removeTag(index)"
                >
                  {{ tag }}
                </el-tag>
                <el-input
                  v-model="aliasInput"
                  @keyup.enter="addTag"
                  @blur="addTag"
                  placeholder="输入别名后回车"
                  style="width: 150px; margin-left: 10px"
                />
              </div>
            </el-form-item>
            <el-form-item label="销售状态" prop="project_status">
              <el-radio-group v-model="model.project_status" style="margin-left: 10px">
                <el-radio
                  v-for="(value, key) in params_data['houseSalestate']"
                  :key="key"
                  :label="value"
                  :value="key"
                ></el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="is_show_ext == 1" label="楼盘等级" prop="house_level">
              <el-radio-group v-model="model.house_level" style="margin-left: 10px">
                <el-radio
                  v-for="(value, key) in params_data['houseLevel']"
                  :label="value.name"
                  :key="key"
                  :value="value.value"
                ></el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="项目公司" prop="company">
              <el-select
                v-model="model.company"
                class="w-full"
                @change="changeCompany"
                multiple
                clearable
                filterable
              >
                <el-option
                  v-for="item in params_data['companys']"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="品牌名称" prop="brand">
              <el-select
                v-model="model.brand"
                class="w-full"
                multiple
                clearable
                filterable
                @change="changeBrands"
              >
                <el-option
                  v-for="item in params_data['brands']"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="售楼处状态" v-if="is_show_ext == 1" prop="sales_address_status">
              <el-radio-group v-model="model.sales_address_status" style="margin-left: 10px">
                <el-radio label="未开放" :value="0"></el-radio>
                <el-radio label="已开放" :value="1"></el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item>
              <template #label>
                <div class="bold">售楼处电话</div>
              </template>
            </el-form-item>
            <el-form-item label="座机" prop="sales_phone">
              <el-col :span="6">
                <el-input v-model="model.sales_phone" placeholder="输入座机" />
              </el-col>
            </el-form-item>
            <el-form-item label="400" prop="sales_400">
              <el-col :span="6">
                <el-input v-model="model.sales_400" placeholder="输入400"> </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="交房时间" prop="handover_time">
              <el-date-picker
                v-model="model.handover_time"
                type="date"
                value-format="YYYY-MM-DD"
                :default-time="new Date(2024, 1, 1)"
                placeholder="开始时间"
              />
            </el-form-item>
            <el-form-item label="特色标签" prop="feature">
              <el-select
                v-model="model.feature"
                class="w-full"
                multiple
                clearable
                filterable
                :value-key="'id'"
              >
                <el-option
                  v-for="item in params_data['peitaoFeature']"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
              <el-col :span="3" :offset="1">
                <el-button @click="addOption">添加</el-button>
              </el-col>
            </el-form-item>
            <el-divider>
              <h3 class="bold">价格信息（优先展示单价-均价）</h3>
            </el-divider>
            <el-form-item>
              <template #label>
                <div class="bold">单价</div>
              </template>
            </el-form-item>

            <el-form-item v-if="is_show_ext == 1" label="起价" prop="min_unit_price">
              <el-col :span="6">
                <el-input v-model="model.min_unit_price" type="number" placeholder="输入单价起价">
                  <template #append>元/㎡</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item v-if="is_show_ext == 1" label="最高价" prop="max_unit_price">
              <el-col :span="6">
                <el-input v-model="model.max_unit_price" type="number" placeholder="输入单价最高价">
                  <template #append>元/㎡</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="均价" prop="unit_price">
              <el-col :span="8">
                <el-input v-model="model.unit_price" type="number" placeholder="输入均价">
                  <template #append>元/㎡</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item v-if="is_show_ext == 1" label="销售指导价" prop="guide_unit_price">
              <el-col :span="8">
                <el-input
                  v-model="model.guide_unit_price"
                  type="number"
                  placeholder="输入销售指导价"
                >
                  <template #append>元/㎡</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item>
              <template #label>
                <div class="bold">总价</div>
              </template>
            </el-form-item>

            <el-form-item label="起价" prop="min_total_price">
              <el-col :span="8">
                <el-input v-model="model.min_total_price" type="number" placeholder="输入总价起价">
                  <template #append>万元/套</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="最高价" prop="max_total_price">
              <el-col :span="8">
                <el-input
                  v-model="model.max_total_price"
                  type="number"
                  placeholder="输入总价最高价"
                >
                  <template #append>万元/套</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item v-if="is_show_ext == 1">
              <template #label>
                <div class="bold">更多信息</div>
              </template>
            </el-form-item>

            <el-form-item label="手动价格" v-if="is_show_ext == 1" prop="hander_price">
              <el-input v-model="model.hander_price" placeholder="" />
            </el-form-item>

            <el-form-item label="价格时间" v-if="is_show_ext == 1" prop="hander_price_time">
              <el-date-picker
                v-model="model.hander_price_time"
                type="datetime"
                value-format="YYYY-MM-DD"
                :default-time="new Date(2024, 1, 1)"
                placeholder="价格时间"
              />
            </el-form-item>
            <el-divider>
              <h3 class="bold">项目建筑信息</h3>
            </el-divider>

            <el-form-item label="建筑类型" prop="build_type">
              <el-checkbox-group v-model="model.build_type">
                <el-checkbox
                  v-for="(value, key) in params_data['buildingType']"
                  :label="value"
                  :value="key"
                  :key="key"
                />
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="物业类型" prop="property_type">
              <el-checkbox-group v-model="model.property_type">
                <el-checkbox
                  v-for="(value, key) in params_data['propertyType']"
                  :label="value"
                  :value="key"
                  :key="key"
                />
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="户型" prop="unit_type">
              <el-checkbox-group v-model="model.unit_type">
                <el-checkbox
                  v-for="(value, key) in params_data['houseType']"
                  :label="value"
                  :value="key"
                  :key="key"
                />
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="户型面积" class="region_plate">
              <el-col :span="6">
                <el-input v-model="model.min_unit_area" type="number" placeholder="最小面积">
                  <template #append>㎡</template>
                </el-input>
              </el-col>
              <el-col :span="1" label="户型面积" :offset="2">
                <sapn>-</sapn>
              </el-col>

              <el-col :span="6">
                <el-input v-model="model.max_unit_area" type="number" placeholder="最大面积">
                  <template #append>㎡</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="产权年限" prop="property_period">
              <el-col :span="6">
                <el-input v-model="model.property_period" type="number" placeholder="输入产权年限">
                  <template #append>年</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="楼层情况" prop="floor_info">
              <el-input v-model="model.floor_info" placeholder="输入楼层情况" />
            </el-form-item>
            <el-form-item label="装修情况" prop="decoration_situation">
              <el-input v-model="model.decoration_situation" placeholder="输入装修情况" />
            </el-form-item>
            <el-form-item label="立面材料" prop="facade_material">
              <el-input v-model="model.facade_material" placeholder="输入立面情况" />
            </el-form-item>
            <el-divider>
              <h3 class="bold">项目体量信息</h3>
            </el-divider>

            <el-form-item label="占地面积" prop="occupation_area">
              <el-col :span="6">
                <el-input v-model="model.occupation_area" type="number" placeholder="输入占地面积">
                  <template #append>㎡</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="建筑面积" prop="building_area">
              <el-col :span="6">
                <el-input v-model="model.building_area" type="number" placeholder="输入建筑面积">
                  <template #append>㎡</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="总户数" prop="household_number">
              <el-col :span="6">
                <el-input v-model="model.household_number" type="number" placeholder="输入总户数">
                  <template #append>户</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="容积率" prop="area_ratio">
              <el-col :span="6">
                <el-input v-model="model.area_ratio" placeholder="输入容积率" />
              </el-col>
            </el-form-item>
            <el-form-item label="楼栋总数" prop="build_number">
              <el-col :span="6">
                <el-input v-model="model.build_number" type="number" placeholder="输入楼栋总数">
                  <template #append>栋</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-divider>
              <h3 class="bold">项目车位信息</h3>
            </el-divider>

            <el-form-item label="车位总数" prop="park_space_number">
              <el-col :span="6">
                <el-input
                  v-model="model.park_space_number"
                  type="number"
                  placeholder="输入车位总数"
                >
                  <template #append>个</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="车位占比" prop="park_space_ratio">
              <el-col :span="6">
                <el-input v-model="model.park_space_ratio" placeholder="输入车位占比"> </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="车位价格" v-if="is_show_ext == 1" prop="park_fee">
              <el-col :span="6">
                <el-input v-model="model.park_fee" type="number" placeholder="输入车位价格">
                  <template #append>元/㎡</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="租车位费用" v-if="is_show_ext == 1" prop="rental_park_fee">
              <el-col :span="6">
                <el-input v-model="model.rental_park_fee" type="number" placeholder="输入租车费用">
                  <template #append>元/月</template>
                </el-input>
              </el-col>
            </el-form-item>
          </el-card>
        </el-col>

        <el-col :offset="1" :span="12" v-if="model.is_land == 0">
          <el-card header="项目位置信息" shadow="hover">
            <el-form-item label="楼盘地址" prop="build_address">
              <el-col :span="12">
                <el-input v-model="model.build_address" placeholder="输入楼盘地址" />
              </el-col>
              <el-col :span="3" :offset="1">
                <el-button :loading="searchLoading" @click="handleGetLng">获取坐标</el-button>
              </el-col>
            </el-form-item>
            <el-form-item label="楼盘坐标" prop="login_bg_id">
              <div id="mapContaniner"></div>
            </el-form-item>
            <el-form-item label="经度" prop="longitude">
              <el-input readonly v-model="model.longitude" type="number" placeholder="输入经度">
              </el-input>
            </el-form-item>
            <el-form-item label="纬度" prop="latitude">
              <el-input readonly v-model="model.latitude" type="number" placeholder="输入纬度">
              </el-input>
            </el-form-item>

            <el-form-item
              label="区域板块"
              prop="region_id"
              placeholder="选择区域"
              class="region_plate"
            >
              <el-col :span="4">
                <el-cascader
                  v-model="model.region_id"
                  :options="regionData"
                  :props="regionProps"
                  @change="changeRegion"
                  class="w-full"
                  clearable
                  filterable
                />
              </el-col>
              <el-col :span="4" :offset="1">
                <el-cascader
                  v-model="model.plate_id"
                  :options="plateData"
                  :props="plateProps"
                  @change="changePlate"
                  clearable
                  filterable
                />
              </el-col>
            </el-form-item>
            
            <el-form-item
              label="所属街道"
              prop="street_id"
              placeholder="所属街道"
              class="region_plate"
            >
              <el-col :span="6">
                <el-cascader
                  v-model="model.street_id"
                  :options="streetData"
                  :props="streetProps"
                  
                  clearable
                  filterable
                />
              </el-col>
              <el-col :span="4" :offset="2">
                <el-button style="margin-top: 5px;" type="primary" @click="handleGetNewStreet">刷新街道信息</el-button>
              </el-col>
            </el-form-item>

            <el-form-item label="板块" prop="sector_name" v-if="is_show_old == 1">
              <el-cascader
                v-model="model.sector_name"
                :options="sectorData"
                :props="sectorProps"
                ref="mycascader"
                @change="changeCasc"
                class="w-full"
                placeholder="请选择板块"
                clearable
                filterable
              />
            </el-form-item>
            <el-form-item label="楼盘四至" v-if="is_show_ext == 1" prop="house_four">
              <el-input v-model="model.house_four" placeholder="输入楼盘四至信息" />
            </el-form-item>

            <el-form-item label="楼盘围栏" v-if="is_show_ext == 1" prop="poi_fence">
              <el-input
                type="textarea"
                :rows="4"
                v-model="model.poi_fence"
                placeholder="输入楼盘地理围栏（|符号分隔）"
                clearable
              />
            </el-form-item>
            <el-form-item label="售楼处地址" prop="sales_address">
              <el-col :span="12">
                <el-input v-model="model.sales_address" placeholder="输入售楼处地址" />
              </el-col>
              <el-col :span="3" :offset="1">
                <el-button :loading="salesSearchLoading" @click="handleGetSalesLng"
                  >获取坐标</el-button
                >
              </el-col>
            </el-form-item>
            <el-form-item label="售楼处坐标">
              <div id="salesMapContainer"></div>
            </el-form-item>

            <el-form-item label="售楼处经度" prop="sales_longitude">
              <el-input
                readonly
                v-model="model.sales_longitude"
                type="number"
                placeholder="输入经度"
              >
              </el-input>
            </el-form-item>

            <el-form-item label="售楼处纬度" prop="sales_latitude">
              <el-input
                readonly
                v-model="model.sales_latitude"
                type="number"
                placeholder="输入纬度"
              >
              </el-input>
            </el-form-item>
            <el-form-item label="所属环线" prop="loop_line">
              <el-radio-group v-model="model.loop_line" style="margin-left: 10px">
                <el-radio
                  v-for="(value, key) in params_data['houseLoopLine']"
                  :key="key"
                  :label="value"
                  :value="key"
                ></el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="周边地铁" prop="subway">
              <el-select v-model="model.subway" class="w-full" multiple clearable filterable disabled>
                <el-option
                  v-for="item in params_data['subways']"
                  :key="item.id"
                  :label="item.subway_name"
                  :value="item.id"
                  :disabled="!originSubway.includes(item.id)"
                />
              </el-select>
            </el-form-item>
            <el-divider>
              <h3 class="bold">内部配套</h3>
            </el-divider>

            <el-form-item label="绿化率" prop="green_proportion">
              <el-col :span="6">
                <el-input v-model="model.green_proportion" type="number" placeholder="输入绿化率">
                  <template #append>%</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="养老设施" v-if="is_show_ext == 1" prop="older_facilities">
              <el-input v-model="model.older_facilities" />
            </el-form-item>
            <el-form-item label="儿童设施" v-if="is_show_ext == 1" prop="child_facilities">
              <el-input v-model="model.child_facilities" />
            </el-form-item>
            <el-form-item label="健身设施" v-if="is_show_ext == 1" prop="fit_facilities">
              <el-input v-model="model.fit_facilities" />
            </el-form-item>
            <el-form-item label="会所" v-if="is_show_ext == 1" prop="club_facilities">
              <el-input v-model="model.club_facilities" />
            </el-form-item>
            <el-divider>
              <h3 class="bold">周边配套</h3>
            </el-divider>
            <el-form-item label="学校" prop="school">
              <el-select v-model="model.school" class="w-full" multiple clearable filterable disabled>
                <el-option
                  v-for="item in params_data['peitaoSchool']"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                  :disabled="!originSchool.includes(item.value)"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="交通" prop="traffic">
              <el-select v-model="model.traffic" class="w-full" multiple clearable filterable disabled>
                <el-option
                  v-for="item in params_data['peitaoTraffic']"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                  :disabled="!originTraffic.includes(item.value)"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="商场" prop="shop">
              <el-select v-model="model.shop" class="w-full" multiple clearable filterable disabled>
                <el-option
                  v-for="item in params_data['peitaoShop']"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                  :disabled="!originShop.includes(item.value)"
                />
              </el-select>
            </el-form-item>
            <el-divider v-if="is_show_ext == 1">
              <h3 class="bold">项目费用信息</h3>
            </el-divider>
            <el-form-item label="电费" v-if="is_show_ext == 1" prop="electric_fee">
              <el-col :span="6">
                <el-input v-model="model.electric_fee" type="number" placeholder="输入电费信息">
                  <template #append>元/度</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="水费" v-if="is_show_ext == 1" prop="water_fee">
              <el-col :span="6">
                <el-input v-model="model.water_fee" type="number" placeholder="输入水费信息">
                  <template #append>元/m³</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="燃气费" v-if="is_show_ext == 1" prop="gas_fee">
              <el-col :span="6">
                <el-input v-model="model.gas_fee" type="number" placeholder="输入燃气费信息">
                  <template #append>元/m³</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-divider>
              <h3 class="bold">项目物业信息</h3>
            </el-divider>

            <el-form-item
              label="物业公司"
              prop="property_company_ids"
              @change="changePropertyCompany"
            >
              <el-select
                v-model="model.property_company_ids"
                class="w-full"
                multiple
                clearable
                filterable
              >
                <el-option
                  v-for="item in params_data['housePropertyCompany']"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="物业费" prop="property_expenses">
              <el-col :span="6">
                <el-input v-model="model.property_expenses" type="number" placeholder="输入物业费">
                  <template #append>元/㎡</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-divider>
              <h3 class="bold">更多信息</h3>
            </el-divider>
            <el-form-item label="自持率" v-if="is_show_ext == 1" prop="model.self_hold_rato">
              <el-col :span="6">
                <el-input v-model="model.self_hold_rato" type="number" placeholder="输入自持率">
                  <template #append>%</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="现房销售率" v-if="is_show_ext == 1" prop="model.sale_rato">
              <el-col :span="6">
                <el-input v-model="model.sale_rato" type="number" placeholder="输入现房销售率">
                  <template #append>%</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="责任编辑" v-if="is_show_ext == 1" prop="editor_duty">
              <el-col :span="6">
                <el-input v-model="model.editor_duty" placeholder="输入责任编辑" />
              </el-col>
            </el-form-item>
            <el-form-item label="热门楼盘标识" prop="house_mark">
              <el-checkbox-group v-model="model.house_mark">
                <el-checkbox
                  v-for="(value, key) in params_data['houseMark']"
                  :label="value.name"
                  :value="value.value"
                  :key="key"
                />
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="上新时间" prop="add_time">
              <el-date-picker
                v-model="model.add_time"
                type="date"
                value-format="YYYY-MM-DD"
                :default-time="new Date(2024, 1, 1)"
                placeholder="上新时间"
              />
            </el-form-item>
            <el-form-item label="销售套数" prop="sale_count">
              <el-input v-model="model.sale_count" type="number" placeholder="销售套数">
                <template #append>套</template>
              </el-input>
            </el-form-item>
            <el-form-item label="销售金额" prop="sale_amount">
              <el-input v-model="model.sale_amount" type="number" placeholder="销售金额">
                <template #append>万</template>
              </el-input>
            </el-form-item>
            <el-form-item label="好房子描述" prop="desc">
              <el-input v-model="model.desc" placeholder="20个字符" maxlength="20" show-word-limit>
              </el-input>
            </el-form-item>
            <el-form-item label="合作楼盘" prop="cooperation_house">
              <el-checkbox-group v-model="model.cooperation_house" @change="handleCheckboxChange">
                <el-checkbox :label="1">是</el-checkbox>
                <el-checkbox :label="0">否</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item
              label="仅展示以下置业顾问"
              prop="selectedAdvisors"
              v-if="Array.isArray(model.cooperation_house) && model.cooperation_house.includes(1)"
            >
              <el-select
                v-model="model.selectedAdvisors"
                multiple
                placeholder="请选择置业顾问"
                @change="handleSelectChange"
              >
                <el-option
                  v-for="advisor in paramsData.advisors"
                  :key="advisor.id"
                  :label="advisor.service_name"
                  :value="advisor.id"
                />
              </el-select>
            </el-form-item>
            <el-divider>
              <h3 class="bold">楼盘图片</h3>
            </el-divider>

            <!-- <el-form-item label="楼号图" prop="build_photo">
              <ImgUploads
                v-model="model.build_photo"
                upload-btn="上传图片"
                file-type="image"
                file-tip=""
              />
            </el-form-item> -->

            <!-- <el-form-item label="户型图" prop="unit_photo">
              <ImgUploads
                v-model="model.unit_photo"
                upload-btn="上传图片"
                file-type="image"
                file-tip=""
              />
            </el-form-item> -->

            <!-- <el-form-item label="其他图片" prop="house_photo">
              <ImgUploads
                v-model="model.house_photo"
                upload-btn="上传图片"
                file-type="image"
                file-tip=""
              />
            </el-form-item> -->

            <el-form-item label="视频" prop="house_video">
              <ImgUpload
                v-model:file-url="model.house_video"
                file-type="video"
                :height="100"
                :width="160"
                upload
              />
              <span>只能上传50M以内的视频</span>
            </el-form-item>
            <el-form-item label="" prop=""> </el-form-item>
          </el-card>
        </el-col>
      </el-row>
      <div class="sub-btn">
        <el-button :loading="loading" title="返回" @click="refresh()" style="margin-right: 10px"
          >返回</el-button
        >
        <el-button :loading="loading" type="primary" @click="submit()" style="margin-right: 10px"
          >提交</el-button
        >
        <el-button
          :loading="loading"
          type="primary"
          @click="submit(true)"
          style="margin-right: 10px"
          >提交并推送</el-button
        >
      </div>
      <el-dialog
        v-model="addoptionDialog"
        title="添加特色标签"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        top="20vh"
        width="20%"
      >
        <el-form ref="addoptionRef" label-width="120px">
          <el-form-item label="标签名称">
            <el-input v-model="tagmodel.name" placeholder="输入标签名称" />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button :loading="loading" @click="addoptionCancel">取消</el-button>
          <el-button :loading="loading" type="primary" @click="addoptionSubmit">提交</el-button>
        </template>
      </el-dialog>
    </el-form>
  </el-scrollbar>
</template>

<script>
import * as Project from '@/api/project/house'
import { jsonp } from '@/utils/jsonp'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useTagsViewStore } from '@/store/modules/tagsView'
import TMap from 'TMap'
import { list } from '@/api/project/plate'
import { tagsbygroup, addTag } from '@/api/project/tag'
import { getIdentityList } from '@/api/member/memberidentity'
import { list as listRegionApi,getNewStreetByTxmap } from '@/api/setting/region'
let mapContaniner = null
let multiMarker = null
let markerGeo = null
// const router = useRouter()
const tagsViewStore = useTagsViewStore()

let salesMapContainer = null
let salesMultiMarker = null
let salesMarkerGeo = null

export default {
  name: 'HouseInfoDetail',
  props: {
    houseId: {
      type: String,
      default: '0'
    }
  },
  data() {
    // 自定义校验规则
    const validateHouseDesc = (rule, value, callback) => {
      if (!value) {
        callback()
        return
      }

      // 检查长度
      if (value.length > 20) {
        callback(new Error('描述不能超过20个字符'))
        return
      }

      // 检查字符类型
      const validPattern = /^[\u4e00-\u9fa5a-zA-Z0-9\p{P}]+$/u
      if (!validPattern.test(value)) {
        callback(new Error('只能包含中文、字母、数字和标点符号'))
        return
      }

      callback()
    }

    return {
      salesSearchLoading: false,
      searchLoading: false,
      name: '楼盘基础信息',
      is_show_new: 0, // 是否展示新字段
      is_show_old: 0, //  是否展示老字段
      is_show_ext: 0, //  是否展示以后扩展字段
      houseType: 0, // 楼盘类型：0 楼盘 1 地块
      loading: false,
      landSpanLenth: 8,
      model: {
        id: 0,
        is_land: 0,
        sales_latitude: '',
        sales_longitude: '',
        alias_name: []
      },
      originSubway: [],
      originSchool: [],
      originShop: [],
      originTraffic: [],
      aliasInput: '',
      canbeLand: false,
      params_data: {},
      regionData: [],
      regionProps: {
        expandTrigger: 'hover',
        // checkStrictly: true,
        value: 'region_id',
        label: 'region_name',
        emitPath: false
      },
      plateData: [],
      plateProps: {
        expandTrigger: 'hover',
        // checkStrictly: true,
        value: 'id',
        label: 'name',
        emitPath: false
      },
      streetData: [],
      streetProps: {
        expandTrigger: 'hover',
        // checkStrictly: true,
        value: 'id',
        label: 'name',
        emitPath: false
      },
      isMapEvent: false,
      sectorData: [],
      sectorProps: {
        expandTrigger: 'hover',
        value: 'name',
        label: 'name'
        //   emitPath: false
      },
      addoptionDialog: false,
      tagmodel: {
        group_id: 5,
        name: ''
      },
      houseBaseRules: {
        name: [{ required: true, message: '请填写楼盘名称', trigger: 'blur' }],
        // project_status: [{ required: true, message: '请选择销售状态', trigger: 'blur' }],
        // sales_address_status: [{ required: true, message: '请选择售楼处状态', trigger: 'blur' }],
        // property_type: [{ required: true, message: '请选择售楼处状态', trigger: 'blur' }],
        // build_type: [{ required: true, message: '请选择建筑类型', trigger: 'blur' }]
        desc: [{ validator: validateHouseDesc, trigger: 'blur' }]
      },
      cooperation_house: [],
      selectedAdvisors: [],
      paramsData: { advisors: [] }
    }
  },

  async mounted() {
    await this.params()
    await this.info()
    this.initMap()
    this.initSalesMap()
    this.basicTags()
    if (this.houseId > 0) {
      this.fetchData()
      this.canbeLand = true
    }
  },
  beforeUnmount() {
    mapContaniner?.destroy()
    mapContaniner = null
    multiMarker?.remove(['center'])
    multiMarker = null
    markerGeo = null
    console.log('destroyed')
    // Add to existing beforeUnmount hook
    salesMapContainer?.destroy()
    salesMapContainer = null
    salesMultiMarker?.remove(['sales_center'])
    salesMultiMarker = null
    salesMarkerGeo = null
  },
  methods: {
    handleGetSalesLng() {
      this.salesSearchLoading = true
      const baseUrl = 'https://apis.map.qq.com'
      const url = `${baseUrl}/ws/geocoder/v1`

      jsonp(url, {
        key: 'POYBZ-JXV6Q-3KD5A-44ZNG-SGTIO-J6FDN',
        output: 'jsonp',
        address: this.model.sales_address
      })
        .then((res) => {
          if (res.status === 0) {
            const { lng, lat } = res.result.location
            this.model.sales_latitude = lat
            this.model.sales_longitude = lng
            this.setSalesCenter(lat, lng)
            this.setSalesMarker()
            if (!this.isSalesMapEvent) {
              this.updateSalesCenter()
            }
          } else {
            ElMessage({
              message: '请填写详细售楼处地址',
              type: 'info'
            })
          }
        })
        .finally(() => {
          this.salesSearchLoading = false
        })
    },
    addTag() {
      const value = this.aliasInput.trim()
      if (value && !this.model.alias_name.includes(value)) {
        this.model.alias_name.push(value)
      }
      this.aliasInput = '' // 清空输入框
    },
    removeTag(index) {
      this.model.alias_name.splice(index, 1)
    },
    initSalesMap() {
      salesMapContainer = new TMap.Map(document.getElementById('salesMapContainer'), {
        zoom: 13
      })
    },

    updateSalesCenter() {
      if (!this.isSalesMapEvent) {
        this.isSalesMapEvent = true
      }
      salesMapContainer.on(
        'center_changed',
        (e) => {
          this.setSalesMarker()
          this.model.sales_latitude = e.center.lat
          this.model.sales_longitude = e.center.lng
        },
        false
      )
    },

    setSalesCenter(lat, lng) {
      const center = new TMap.LatLng(lat, lng)
      salesMapContainer.setCenter(center)
    },

    setSalesMarker() {
      if (!salesMarkerGeo) {
        salesMarkerGeo = {
          id: 'sales_center',
          position: salesMapContainer.getCenter(),
          styleId: 'sales_address',
          content: this.model.sales_address
        }
      } else {
        salesMarkerGeo.position = salesMapContainer.getCenter()
        salesMarkerGeo.content = this.model.sales_address
      }

      if (!salesMultiMarker) {
        salesMultiMarker = new TMap.MultiMarker({
          map: salesMapContainer,
          geometries: [salesMarkerGeo],
          styles: {
            sales_address: new TMap.MarkerStyle({
              direction: 'bottom'
            })
          }
        })
      } else {
        salesMultiMarker.updateGeometries([salesMarkerGeo])
      }
    },
    handleCheckboxChange(value) {
      if (value.length > 1) {
        this.model.cooperation_house = [value[value.length - 1]]
      }
      if (value.includes(0)) {
        this.model.selectedAdvisors = []
      }
    },
    // 信息
    info() {
      if (this.houseId > 0) {
        Project.info({
          id: this.houseId
        }).then((res) => {
          this.model = res.data
          this.originSubway = res.data.subway
          this.originSchool = res.data.school
          this.originShop = res.data.shop
          this.originTraffic = res.data.traffic
          
          this.model.is_land = 0
          this.model.project_status = parseInt(this.model.project_status)
          this.model.release_status = parseInt(this.model.release_status)

          this.model.cooperation_house = this.model.cooperation_house === 1 ? [1] : [0]

          this.model.selectedAdvisors = this.model.selectedAdvisors
            ? this.model.selectedAdvisors
                .toString()
                .split(',')
                .map((id) => Number(id))
            : []
          this.model.alias_name = this.model.alias_name ? this.model.alias_name.split(',') : []
          this.setCenter(this.model.latitude, this.model.longitude)
          this.setMarker(this.model.latitude, this.model.longitude)
          this.getPlateByRegion(res.data.region_id)
          this.getStreetByRegion(res.data.region_id)
        })
      } else {
        this.model.id = 0
        this.model.cooperation_house = [0]
      }
    },
    // 字典
    params() {
      Project.searchparams().then((res) => {
        this.params_data = res.data
        this.regionData = res.data.region
        this.sectorData = res.data.sectors
        this.is_show_new = res.data.is_show_new
        this.is_show_old = res.data.is_show_old
        this.is_show_ext = res.data.is_show_ext
      })
    },
    isActive(tag) {
      return tag.fullPath === this.$route.fullPath
    },
    closeSelectedTag(view, is_jump = false) {
      tagsViewStore.delView(view).then((res) => {
        if (is_jump == false) {
          if (this.isActive(view)) {
            this.toLastView(res.visitedViews, view)
          }
        }
      })
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView && latestView.fullPath) {
        // console.log(latestView)
        // return
        this.$router.push('/house/house')
        // this.$router.push(latestView.fullPath)
      } else {
        console.log('view?.name')
        return
        // 现在默认情况是如果没有标签视图则重定向到主页，
        // 你可以根据自己的需要进行调整。
        if (view?.name === 'Dashboard') {
          // 要重新加载主页
          this.$router.replace({ path: '/redirect' + view.fullPath })
        } else {
          this.$router.push('/#/house/house')
        }
      }
    },
    // 提交
    submit(off = false) {
      this.loading = true
      if (this.model.property_company_ids) {
        this.changePropertyCompany(this.model.property_company_ids)
      }
      if (this.model.company) {
        this.changeCompany(this.model.company)
      }
      if (this.model.brand) {
        this.changeBrands(this.model.brand)
      }
      console.log(this.model)
      const params = this.model
      console.log(params)
      if (off) {
        params.is_send = 1
      }

      Project.editHouse(params)
        .then((res) => {
          this.loading = false
          if (this.model.id == 0) {
            ElMessage.success('新建楼盘成功，请在楼盘相册管理图片')
            this.closeSelectedTag(this.$router.currentRoute.value, true)
            this.$router.replace('/house/info?id=' + res.data)
            return
          } else {
            ElMessage.success(res.msg)
            // 关闭当前页面，并返回上一页
            this.closeSelectedTag(this.$router.currentRoute.value)
          }
          this.model.id = res.data
          this.houseId = res.data
          return
        })
        .finally(() => {
          this.loading = false
        })
    },
    basicTags() {
      tagsbygroup({ groupid: 5 })
        .then((res) => {
          this.tagsBasics = res.data
        })
        .catch(() => {})
    },
    // 新增标签
    addOption() {
      this.addoptionDialog = true
    },
    // 取消新增标签
    addoptionCancel() {
      this.addoptionDialog = false
      this.tagmodel.name = ''
    },
    // 提交新增标签
    addoptionSubmit() {
      if (this.tagmodel.name == '') {
        ElMessage.error('请输入标签名称')
        return
      }

      var is_exist = 0
      this.tagsBasics.forEach((item) => {
        if (item.name == this.tagmodel.name) {
          is_exist = 1
          return
        }
      })

      if (is_exist == 1) {
        ElMessage.error('标签已经存在')
        return
      }

      addTag(this.tagmodel)
        .then((res) => {
          console.log('标签添加成功', res) // 打印返回数据
          if (res && res.data) {
            var mm = { id: res.data, name: this.tagmodel.name }
            console.log(mm)
            this.params_data['peitaoFeature'].push(mm)
            this.model.feature.push(res.data)
            ElMessage.success('添加标签成功')
            this.addoptionCancel()
          } else {
            ElMessage.error('标签添加失败：无返回数据')
          }
        })
        .catch((error) => {
          console.error('标签添加失败', error) // 打印错误信息
          ElMessage.error('添加标签失败')
          this.addoptionDialog = false
        })

      this.addoptionDialog = false
    },
    // 刷新
    refresh() {
      this.closeSelectedTag(this.$router.currentRoute.value)
    },

    // 选择 城区板块
    changeCasc(selectValuesArr) {
      if (selectValuesArr == undefined) {
        return
      } else {
        this.model.region_name = selectValuesArr[0]
        this.model.sector_name = selectValuesArr[1]
      }
    },
    changeBrands(val) {
      var m = []
      this.params_data['brands'].find((item) => {
        // 这里的userList就是上面遍历的数据源
        if (val.includes(item.id)) {
          // 筛选出匹配数据
          m.push(item.name)
        }
      })
      this.model.develop_brand = m.join(',')
    },
    changePropertyCompany(val) {
      var m = []
      this.params_data['housePropertyCompany'].find((item) => {
        // 这里的userList就是上面遍历的数据源
        if (val.includes(item.id)) {
          // 筛选出匹配数据
          m.push(item.name)
        }
      })
      this.model.property_company = m.join(',')
    },
    changeCompany(val) {
      var m = []
      this.params_data['companys'].find((item) => {
        // 这里的userList就是上面遍历的数据源
        if (val.includes(item.id)) {
          // 筛选出匹配数据
          m.push(item.name)
        }
      })
      this.model.project_company = m.join(',')
    },
    changePlate(val) {
      if (val != undefined) {
        var index = this.plateData.findIndex((i) => i.id === val)
        this.model.sector_name = this.plateData[index].name
      }
    },
    changeRegion(selectValuesArr) {
      if (selectValuesArr != undefined) {
        var index = this.regionData.findIndex((i) => i.region_id === selectValuesArr)
        this.model.region_name = this.regionData[index].region_name
      }
      this.getPlateByRegion(selectValuesArr)
      this.getStreetByRegion(selectValuesArr)
    },
    getPlateByRegion(region_id) {
      this.loading = true
      if (region_id == undefined) {
        this.plateData = []
        this.model.plate_id = 0
        this.loading = false
        return
      }
      list({ state: 1, countyid: region_id, page: 1, limit: 100 })
        .then((res) => {
          this.plateData = res.data.list
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 刷新街道
    handleGetNewStreet() {
      if (this.model.region_id == 0 || this.model.latitude == '' || this.model.longitude == '') {
          ElMessage({
              message: '请先补充 城区数据 和 经纬度 信息',
              type: 'info'
            })
        return
      }
      getNewStreetByTxmap({ region_id: this.model.region_id,latitude:this.model.latitude,longitude:this.model.longitude })
        .then((res) => {
           if(res.code==200){
            let tmpk = {'id': res.data.region_id, 'name': res.data.region_name}
            this.streetData.push(tmpk)
            ElMessage.success({
              message: '刷新街道信息成功',
              type: 'info'
            })
           }else if(res.code==201){
            ElMessage.success({
              message: '刷新街道信息成功',
              type: 'info'
            })
           }else{
            ElMessage.error({
              message: res.msg,
              type: 'error'
            })
           }
          
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 选择 街道
    getStreetByRegion(region_id) {
      this.loading = true
      if (region_id == undefined) {
        this.streetData = []
        this.model.street_id = 0
        this.loading = false
        return
      }
      listRegionApi({ region_pid: region_id,level:4 })
        .then((res) => {
          for (var i = 0; i < res.data.list.length; i++) {
            let tmpk = {'id': res.data.list[i].region_id, 'name': res.data.list[i].region_name}
            this.streetData.push(tmpk)
          }
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },

    // 选择 城区板块
    houseTypeChange(selectValuesArr) {
      if (this.houseId == 0) {
        this.closeSelectedTag(this.$router.currentRoute.value, true)
        this.$router.replace('/house/land')
        // window.location.href = '/#/house/land'
      }
    },
    handleGetLng() {
      this.searchLoading = true
      const baseUrl = 'https://apis.map.qq.com'
      const url = `${baseUrl}/ws/geocoder/v1`
      jsonp(url, {
        key: 'POYBZ-JXV6Q-3KD5A-44ZNG-SGTIO-J6FDN',
        output: 'jsonp',
        address: this.model.build_address
      })
        .then((res) => {
          if (res.status === 0) {
            const { lng, lat } = res.result.location
            this.model.latitude = lat
            this.model.longitude = lng
            if (res.result?.ad_info?.adcode) {
              this.setRegionId(Number(res.result.ad_info.adcode))
            }
            this.setCenter(lat, lng)
            this.setMarker()
            if (!this.isMapEvent) {
              this.updateCenter()
            }
          } else {
            ElMessage({
              message: '请填写详细楼盘地址',
              type: 'info'
            })
          }

          console.log(res.result)
        })
        .finally(() => {
          this.searchLoading = false
        })
    },
    setRegionId(id) {
      if (this.regionData.findIndex((i) => i.region_id === id) !== -1) {
        this.model.region_id = id
        this.changeRegion(this.model.region_id)
      }
    },
    // 初始化地图
    initMap() {
      mapContaniner = new TMap.Map(document.getElementById('mapContaniner'), {
        zoom: 13
      })
    },
    updateCenter() {
      if (!this.isMapEvent) {
        this.isMapEvent = true
      }
      mapContaniner.on(
        'center_changed',
        (e) => {
          this.setMarker()
          this.model.latitude = e.center.lat
          this.model.longitude = e.center.lng
        },
        false
      )
    },
    setCenter(lat, lng) {
      const center = new TMap.LatLng(lat, lng)
      mapContaniner.setCenter(center)
    },
    setMarker(lat, lng) {
      if (!markerGeo) {
        markerGeo = {
          id: 'center',
          position: mapContaniner.getCenter(),
          styleId: 'build_address',
          content: this.model.build_address
        }
      } else {
        markerGeo.position = mapContaniner.getCenter()
        markerGeo.content = this.model.build_address
      }
      if (!multiMarker) {
        multiMarker = new TMap.MultiMarker({
          map: mapContaniner,
          geometries: [markerGeo],
          styles: {
            build_address: new TMap.MarkerStyle({
              direction: 'bottom'
            })
          }
        })
      } else {
        multiMarker.updateGeometries([markerGeo])
      }
    },
    async fetchData() {
      console.log(this.houseId)
      try {
        const response = await getIdentityList({ house_id: this.houseId })
        this.paramsData.advisors = response?.data || []
        console.log(this.paramsData.advisors)
      } catch (error) {
        console.error('获取数据失败:', error)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
#salesMapContainer {
  width: 500px;
  height: 250px;
}
.wrapper {
  min-width: 1300px;
  :deep(.el-input) {
    width: 350px;
  }
  :deep(.el-select) {
    width: 350px;
  }
  :deep(.el-textarea) {
    width: 350px;
  }
  :deep(.el-divider) {
    margin: 30px 0;
  }
}
.region_plate {
  :deep(.el-input) {
    width: 120%;
  }

  .el-input {
    width: 120%;
  }
}
.sub-btn {
  padding: 20px 0;
}
.bold {
  font-weight: bold;
}
hr {
  line-height: 10px;
}
#mapContaniner {
  width: 500px;
  height: 250px;
}
#mapContaniner {
  width: 500px;
  height: 250px;
}
</style>
