// import { BASE_API } from '@/utils/static';
import tinymce from 'tinymce/tinymce';
import '@/assets/tinymce/zh_CN.js';

import 'tinymce/plugins/image'; // 插入编辑图片
import 'tinymce/plugins/link'; // 插入编辑图片
import 'tinymce/plugins/wordcount'; // 字数统计
import 'tinymce/plugins/paste'; // 字数统计
import 'tinymce/plugins/print'; // 
import 'tinymce/plugins/preview'; // 
import 'tinymce/plugins/code';
import 'tinymce/plugins/table';
import 'tinymce/themes/silver/theme.min.js'; // 主题js
import 'tinymce/skins/ui/oxide/skin.min.css'; // 主题css
import 'tinymce/icons/default'; // 图标css

const toolbar =
  'code formatselect table  bold italic forecolor backcolor  alignleft aligncenter alignright alignjustify  bullist numlist outdent indent  link  ';
// image
const tinymceInit = {
  // language_url: zhCN,
  language: 'zh_CN',
  skin: false,
  content_css: false,
  plugins: 'print preview table image wordcount paste link code', // 图片上传和字数显示插件
  toolbar, // 菜单栏
  branding: false, // 禁止显示tinymce标识
  resize: false, // 禁止resize监听
  height: 800,
  width: 400,
  paste_data_images: true, // 网络上传图片，false变为base64文件
  // images_upload_url,
  link_context_toolbar: true,
  images_upload_handler: (blobInfo, success, failure) => {
    const img = 'data:image/jpeg;base64,' + blobInfo.base64()
    success(img)
  }
  // images_upload_handler: (blobInfo, success, failure) => {
  //   let xhr;
  //   let formData;
  //   xhr = new XMLHttpRequest();
  //   xhr.withCredentials = false;
  //   xhr.open('POST', images_upload_url);
  //   xhr.onload = function () {
  //     let json;
  //     json = JSON.parse(xhr.responseText);
  //     if (json.code !== 200) {
  //       failure(json.msg);
  //     }
  //     if (!json || typeof json.data.url !== 'string') {
  //       failure(json.msg);
  //       return;
  //     }
  //     success(json.data.url);
  //   };
  //   formData = new FormData();
  //   formData.append('img', blobInfo.blob(), blobInfo.filename());
  //   xhr.send(formData);
  // },
};

export { tinymceInit, tinymce };
