# 亮点列表选中问题修复说明

## 问题描述

亮点列表选中后，提交给后台的 `selectedHighlights` 的值是 `[]`，正确应该是 `[选中数据的id]`。

## 问题原因

### 1. 错误的表格选择绑定方式
```vue
<!-- 错误的写法 -->
<el-table v-model:selection="formData.selectedHighlights" row-key="id">
```

Element Plus 表格组件不支持 `v-model:selection` 这种双向绑定语法。

### 2. 缺少选择变化处理函数
没有正确处理表格选择状态变化的事件。

## 修复方案

### 1. 修正表格选择绑定方式

**修复前：**
```vue
<el-table 
  :data="highlightsData" 
  style="width: 100%" 
  border 
  v-model:selection="formData.selectedHighlights" 
  row-key="id"
>
```

**修复后：**
```vue
<el-table 
  ref="highlightTableRef"
  :data="highlightsData" 
  style="width: 100%" 
  border 
  row-key="id"
  @selection-change="handleHighlightSelectionChange"
>
```

### 2. 添加选择变化处理函数

```javascript
// 处理亮点选择变化
function handleHighlightSelectionChange(selection) {
  console.log('亮点选择变化:', selection)
  // 将选中的行数据存储到 formData.selectedHighlights
  formData.selectedHighlights = selection
  console.log('更新后的selectedHighlights:', formData.selectedHighlights)
}
```

### 3. 添加表格引用

```javascript
// 亮点表格引用
const highlightTableRef = ref(null)
```

### 4. 修复编辑时的选中状态恢复

```javascript
// 如果有已选中的亮点，需要在表格中恢复选中状态
if (formData.selectedHighlights && formData.selectedHighlights.length > 0) {
  // 延迟执行，确保表格已渲染
  setTimeout(() => {
    if (highlightTableRef.value) {
      // 清除所有选中状态
      highlightTableRef.value.clearSelection()
      // 根据已选中的ID恢复选中状态
      highlightsData.value.forEach(row => {
        const isSelected = formData.selectedHighlights.some(selected => 
          (typeof selected === 'object' ? selected.id : selected) === row.id
        )
        if (isSelected) {
          highlightTableRef.value.toggleRowSelection(row, true)
        }
      })
    }
  }, 100)
}
```

### 5. 优化提交时的数据处理

**修复前：**
```javascript
// 确保selectedHighlights只包含选中行的id
if (formData.selectedHighlights && Array.isArray(formData.selectedHighlights)) {
  formData.selectedHighlights = formData.selectedHighlights.map(item => item.id);
}
```

**修复后：**
```javascript
// 确保selectedHighlights只包含选中行的id
if (formData.selectedHighlights && Array.isArray(formData.selectedHighlights)) {
  formData.selectedHighlights = formData.selectedHighlights.map(item => {
    // 如果item是对象，提取id；如果已经是id，直接返回
    return typeof item === 'object' ? item.id : item;
  });
} else {
  // 如果不是数组，确保为空数组
  formData.selectedHighlights = [];
}
```

## 数据流程

### 1. 用户选择亮点时
```
用户勾选表格行 → @selection-change 事件触发 → handleHighlightSelectionChange 函数执行 → formData.selectedHighlights 更新为选中的行对象数组
```

### 2. 提交表单时
```
formData.selectedHighlights = [
  {id: 1, name: '智能安防', ...},
  {id: 3, name: '智能照明', ...}
]
↓ 数据转换
formData.selectedHighlights = [1, 3]
↓ 提交给后端
{selectedHighlights: [1, 3]}
```

### 3. 编辑时恢复选中状态
```
后端返回数据 → formData.selectedHighlights = [1, 3] → 表格渲染完成 → 根据ID数组恢复表格选中状态
```

## 关键改进点

1. **正确的事件处理**：使用 `@selection-change` 事件而不是 `v-model:selection`
2. **数据类型兼容**：支持对象数组和ID数组两种格式
3. **状态恢复**：编辑时正确恢复表格的选中状态
4. **错误处理**：确保数据格式正确，避免提交空数组
5. **调试友好**：添加详细的控制台日志

## 测试验证

### 1. 新增时测试
1. 打开添加对话框
2. 切换到"功能亮点"标签页
3. 勾选几个亮点
4. 查看控制台输出：`formData.selectedHighlights` 应该包含选中的行对象
5. 点击保存，查看网络请求：`selectedHighlights` 应该是ID数组

### 2. 编辑时测试
1. 打开编辑对话框
2. 切换到"功能亮点"标签页
3. 确认之前选中的亮点正确显示为选中状态
4. 修改选中状态
5. 点击保存，确认提交的数据正确

### 3. 控制台调试信息
```
亮点选择变化: [{id: 1, name: '智能安防', ...}]
更新后的selectedHighlights: [{id: 1, name: '智能安防', ...}]
原始selectedHighlights: [{id: 1, name: '智能安防', ...}]
转换后selectedHighlights: [1]
```

## 注意事项

1. **表格渲染时序**：恢复选中状态需要等待表格渲染完成，使用 `setTimeout` 延迟执行
2. **数据格式一致性**：确保前后端数据格式一致，后端期望接收ID数组
3. **空值处理**：确保 `selectedHighlights` 始终是数组类型
4. **调试信息**：保留控制台日志便于排查问题

现在亮点列表的选中功能应该正常工作了，提交给后台的 `selectedHighlights` 将是正确的ID数组格式。
