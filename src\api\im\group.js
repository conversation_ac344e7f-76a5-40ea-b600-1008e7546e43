import request from '@/utils/request'
// 群聊
const url = '/admin/im.Group/'
/**
 * 群聊列表
 * @param {array} params 请求参数
 */
export function list(params) {
    return request({
        url: url + 'list',
        method: 'get',
        params: params
    })
}
/**
 * 群聊详情
 * @param {array} params 请求参数
 */
export function info(params) {
    return request({
        url: url + 'info',
        method: 'get',
        params: params
    })
}
/**
 * 群聊添加
 * @param {array} data 请求数据
 */
export function add(data) {
    return request({
        url: url + 'add',
        method: 'post',
        data
    })
}
/**
 * 群聊修改
 * @param {array} data 请求数据
 */
export function edit(data) {
    return request({
        url: url + 'edit',
        method: 'post',
        data
    })
}
/**
 * 群聊删除
 * @param {array} data 请求数据
 */
export function dele(data) {
    return request({
        url: url + 'dele',
        method: 'post',
        data
    })
}
export function groupcate(){
    return request({
        url: url + 'groupcate',
        method: 'get'
    })
}
/**
 * 获取楼盘
 * @param {array} data 请求数据
 */
export function houseData(data){
    return request({
        url: url + 'houseData',
        method: 'post',
        data
    })
}
export function leaderData(){
    return request({
        url: url + 'leaderData',
        method: 'get'
    })
}

