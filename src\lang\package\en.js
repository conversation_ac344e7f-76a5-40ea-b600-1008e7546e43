export default {
  // 会员
  member: {
    'Member statistic': 'Member statistic'
  },
  // 内容
  content: {
    content: 'content',
    category: 'category',
    tag: 'tag',
    'Content statistic': 'Content statistic'
  },
  // 文件
  file: {
    file: 'file',
    group: 'group',
    tag: 'tag',
    'File statistic': 'File statistic',
    'file type': 'file type'
  },
  // 公共
  common: {
    Notice: 'Notice',
    view: 'View',
    close: 'Close',
    tip: 'tip',
    'Don not prompt again': 'Don not prompt again',
    'Please enter': 'Please enter',
    day: 'day',
    month: 'month',
    'Start date': 'Start date',
    'End date': 'End date',
    'Count statistic': 'Count statistic',
    'Are you sure you want to exit the system?': 'Are you sure you want to exit the system?'
  },
  // 路由
  route: {
    dashboard: 'Dashboard',
    'System setting': 'System setting',
    会员管理: 'Member',
    会员标签: 'Member tag',
    会员分组: 'Member group',
    会员接口: 'Member api',
    会员统计: 'Member statistic',
    会员日志: 'Member log',
    会员第三方账号: 'Member third',
    内容管理: 'Content',
    内容分类: 'Content category',
    内容标签: 'Content tag',
    文件管理: 'File',
    文件分组: 'File group',
    文件标签: 'File tag',
    设置管理: 'Setting',
    会员设置: 'Member setting',
    内容设置: 'Content setting',
    文件设置: 'File setting',
    轮播管理: 'Carousel',
    通告管理: 'Notice',
    协议管理: 'Accord',
    反馈管理: 'Feedback',
    友链管理: 'Link',
    地区管理: 'Region',
    系统管理: 'System',
    菜单管理: 'Menu',
    角色管理: 'Role',
    部门管理: 'Dept',
    职位管理: 'Post',
    用户管理: 'User',
    用户日志: 'User log',
    公告管理: 'Notice',
    个人中心: 'User center',
    接口文档: 'apidoc',
    系统设置: 'System setting'
  },
  // 登录页面
  login: {
    username: 'Username',
    password: 'Password',
    captchaCode: 'Captcha Code',
    login: 'Login',
    'Please enter username': 'Please enter username',
    'Please enter password': 'Please enter password',
    'Please enter captcha code': 'Please enter captcha code',
    'Click to refresh the captcha code': 'Click to refresh the captcha code'
  },
  // 导航栏
  navbar: {
    'Clear Cache': 'Clear Cache',
    'full screen': 'full screen',
    'Language switching': 'Language switching',
    'Theme switching': 'Theme switching',
    dashboard: 'Dashboard',
    userCenter: 'User Center',
    logout: 'Logout'
  },
  // 标签栏
  TagsView: {
    Refresh: 'Refresh',
    Close: 'Close',
    'Close Other': 'Close Other',
    'Close left side': 'Close left side',
    'Close right side': 'Close right side',
    'Close all': 'Close all'
  },
  // 行为验证码
  AjCaptch: {
    Refresh: 'Refresh',
    Close: 'Close',
    'Please complete security verification': 'Please complete security verification',
    'Swipe right to complete verification': 'Swipe right to complete verification',
    'Verification successful': 'Verification successful',
    'Validation failed': 'Validation failed',
    'Please click on them one by one': 'Please click on them one by one'
  }
}
