import request from '@/utils/request';
// 价格评测
const url = '/admin/evaluating.Evaluating/';
/**
 * 价格评测列表
 * @param {array} params 请求参数
 */
export function evaluatingPriceList(params) {
  return request({
    url: url + 'priceevaluationList',
    method: 'get',
    params: params
  })
}

/**
 * 一房一价数据导入
 * @param {array} data 请求数据
 */
export function priceevaluatingimports(data) {
  return request({
    url: url + 'priceevaluatingimport',
    method: 'post',
    data
  })
}

export function getDetailEvaluatingPriceByID(data) {
  return request({
    url: url + 'pricedetail',
    method: 'post',
    data
  })
}

/**
 * 周边价格数据导入
 * @param {array} data 请求数据
 */
export function peripheryimports(data) {
  return request({
    url: url + 'peripheryimport',
    method: 'post',
    data
  })
}
//获取本楼盘的 周边楼盘列表
export function getPeripheryByID(data) {
  return request({
    url: url + 'getperpheryList',
    method: 'post',
    data
  })
}

export function priceMarkChange(data) {
  return request({
    url: url + 'priceMarkChange',
    method: 'post',
    data
  })
}


export function coordinateInfo(params) {
  return request({
    url: url + 'coordinateInfo',
    method: 'get',
    params: params
  })
}

export function coordinateEdit(data) {
  return request({
    url: url + 'coordinateEdit',
    method: 'post',
    data
  })
}

export function getSupportEvaluationList(data) {
  return request({
    url: '/admin/evaluating.SupportEvaluation/index',
    method: 'post',
    data
  })
}

// 配套保存
export function supportEvaluationSave(data) {
  return request({
    url: '/admin/evaluating.SupportEvaluation/save',
    method: 'post',
    data
  })
}

// 配套站点删除
export function supportEvaluationDeletePoint(data) {
  return request({
    url: '/admin/evaluating.SupportEvaluation/deletePoint',
    method: 'post',
    data
  })
}
// 新增自定义点位
export function supportEvaluationAddPoint(data) {
  return request({
    url: '/admin/evaluating.SupportEvaluation/addPoint',
    method: 'post',
    data
  })
}

export function supportBasicimports(data) {
  return request({
    url: '/admin/evaluating.SupportEvaluation/importBasicSupportData',
    method: 'post',
    data
  })
}

export function getSupporDetails(id) {
  return request({
    url: '/admin/evaluating.SupportEvaluation/show?id=' + id,
    method: 'get',
  })
}
// 编辑点位
export function editSupporDetails(id, data) {
  return request({
    url: '/admin/evaluating.SupportEvaluation/editPoint?id=' + id,
    method: 'post',
    data
  })
}

export function markSupporDetails(id, data) {
  return request({
    url: '/admin/evaluating.SupportEvaluation/markPoint?id=' + id,
    method: 'post',
    data
  })
}

export function removeNewLabel(data) {
  return request({
    url: '/admin/evaluating.SupportEvaluation/removeNewLabel',
    method: 'post',
    data
  })
}
// 配套推送
export function sendMessage(data) {
  return request({
    url: '/admin/evaluating.SupportEvaluation/sendMessage',
    method: 'post',
    data
  })
}

export function exChangeBuildUnit(data) {
  return request({
    url: url + 'pcinfoExchage',
    method: 'post',
    data
  })
}

export function getSchoolList(data) {
  return request({
    url: '/admin/evaluating.SupportEvaluation/getSchool',
    method: 'post',
    data
  })
}