<template>
  <div class="app-container">
    <!-- 查询区域 -->
    <el-row>
      <el-form ref="queryRef" :model="query" :inline="true">
        <el-form-item label="专题名称">
          <el-input
            v-model="query.title"
            maxlength="40"
            placeholder="请输入专题名称"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="query.date_value"
            type="daterange"
            class="ya-date-value"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="发布状态">
          <el-select v-model="query.state" style="width: 140px">
            <el-option
              v-for="states in stateSelectList"
              :key="states.value"
              :label="states.label"
              :value="states.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
          <el-button @click="refresh"> 重置 </el-button>
        </el-form-item>
      </el-form>
    </el-row>

    <el-button type="primary" @click="add">新建</el-button>

    <!-- 表格区域 -->
    <el-table ref="table" v-loading="loading" :data="data" :height="height">
      <el-table-column prop="id" label="专题ID" width="80" />
      <el-table-column prop="title" label="标题" min-width="180" />
      <el-table-column prop="link" label="访问链接" min-width="220">
        <template #default="scope">
          <span style="margin-right: 8px">{{ scope.row.link }}</span>
          <el-button v-if="scope.row.link" type="primary" @click="copyLink(scope.row.link)">
            复制
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="page_views" label="浏览量" min-width="200" />
      <el-table-column prop="update_time" label="更新时间" width="160" />
      <el-table-column prop="status" label="发布状态" width="100">
        <template #default="scope">
          {{ scope.row.status === 2 ? '已发布' : '未发布' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="160" fixed="right">
        <template #default="scope">
          <el-link type="primary" class="mr-1" @click="edit(scope.row)">编辑</el-link>
          <el-link
            type="primary"
            v-if="scope.row.status === 1"
            @click="changeStateButton(scope.row.id, 2)"
            >发布</el-link
          >
          <el-link type="primary" v-else @click="changeStateButton(scope.row.id, 1)">下架</el-link>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="count > 0"
      v-model:total="count"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="list"
    />

    <!-- 新增/编辑弹窗 -->
    <el-dialog v-model="dialog" :title="dialogTitle" :close-on-click-modal="false" width="800px">
      <el-form :model="form" ref="formRef" label-position="top">
        <!-- 基础信息 -->
        <div class="filter-section">
          <div class="section-title">基础信息</div>
          <el-form-item label="主标题">
            <el-input v-model="form.title" placeholder="输入主标题建议最多12个字"></el-input>
          </el-form-item>
          <el-form-item label="副标题">
            <el-input v-model="form.subtitle" placeholder="输入副标题建议最多6-8个字"></el-input>
          </el-form-item>
          <!-- 专题头图 -->
          <el-form-item label="专题头图" prop="item_pic">
            <el-radio-group v-model="form.item_pic" @change="handleHeaderImageChange">
              <el-radio :label="1">默认红</el-radio>
              <el-radio :label="2">默认蓝</el-radio>
              <el-radio :label="3">默认橙</el-radio>
              <el-radio :label="4">自定义</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否添加水印" prop="is_watermark" v-if="form.item_pic == '4'">
            <el-radio-group v-model="form.is_watermark" style="margin-left: 10px">
              <el-radio label="否" :value="0"></el-radio>
              <el-radio label="是" :value="1"></el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- 自定义图片上传 -->
          <el-form-item label="自定义图片上传" prop="image" v-if="form.item_pic == '4'">
            <FileImage
              v-model="form.pic"
              :source="3"
              :isWatermark="form.is_watermark"
              :file-url="form.image_url"
              :height="100"
              upload
            />
            <span style="font-size: 12px; color: #999; margin-left: 10px"
              >图片尺寸要求：750x540</span
            >
          </el-form-item>
          <!-- 背景色 -->
          <el-form-item label="背景色" prop="background_color">
            <el-radio-group v-model="form.background_color">
              <el-radio :label="1">默认红</el-radio>
              <el-radio :label="2">默认蓝</el-radio>
              <el-radio :label="3">默认橙</el-radio>
              <el-radio :label="4">白色</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="描述">
            <el-input
              type="textarea"
              v-model="form.desc"
              :rows="4"
              placeholder="输入描述"
            ></el-input>
          </el-form-item>
        </div>

        <!-- 楼盘聚合配置 -->
        <div class="filter-section mt-4">
          <div class="section-title">楼盘聚合配置</div>
          <el-tabs v-model="form.aggregationType">
            <el-tab-pane label="依据搜索条件" name="searchBased">
              <el-form-item label="检索关键字">
                <el-input v-model="form.search.name" placeholder="输入检索关键字"></el-input>
              </el-form-item>
              <el-form-item label="价格">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="总价(万/套)">
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          border: 1px solid #ccc;
                          border-radius: 4px;
                          padding: 5px;
                          width: fit-content;
                        "
                      >
                        <input
                          type="text"
                          v-model="form.search.minTotalPrice"
                          minlength="0"
                          placeholder="最低价格"
                          style="border: none; outline: none; padding: 5px; flex: 1"
                          @input="onTotalPriceChange"
                        />
                        <span style="margin: 0 5px">至</span>
                        <input
                          type="text"
                          v-model="form.search.maxTotalPrice"
                          maxlength="1000000000000000"
                          placeholder="最高价格"
                          style="border: none; outline: none; padding: 5px; flex: 1"
                          @input="onTotalPriceChange"
                        />
                      </div>
                      <el-checkbox-group
                        v-model="form.search.totalPriceOpt"
                        @change="onTotalPriceOptionChange"
                      >
                        <el-checkbox
                          style="width: 40%"
                          v-for="(item, index) in totalPriceOptions"
                          :key="index"
                          :label="item.value"
                        >
                          {{ item.name }}
                        </el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="单价(元/㎡)">
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          border: 1px solid #ccc;
                          border-radius: 4px;
                          padding: 5px;
                          width: fit-content;
                        "
                      >
                        <input
                          type="text"
                          v-model="form.search.minUnitPrice"
                          minlength="0"
                          placeholder="最低价格"
                          style="border: none; outline: none; padding: 5px; flex: 1"
                          @input="onUnitPriceChange"
                        />
                        <span style="margin: 0 5px">至</span>
                        <input
                          type="text"
                          v-model="form.search.maxUnitPrice"
                          maxlength="1000000000000000"
                          placeholder="最高价格"
                          style="border: none; outline: none; padding: 5px; flex: 1"
                          @input="onUnitPriceChange"
                        />
                      </div>

                      <div style="display: flex; justify-content: flex-end; flex-wrap: wrap">
                        <el-checkbox-group
                          v-model="form.search.unitPriceOpt"
                          @change="onUnitPriceOptionChange"
                        >
                          <el-checkbox
                            style="width: 120px"
                            v-for="(item, index) in unitPriceOptions"
                            :key="index"
                            :label="item.value"
                          >
                            {{ item.name }}
                          </el-checkbox>
                        </el-checkbox-group>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form-item>
              <el-form-item label="区域">
                <el-form-item label="区域" style="display: flex; align-items: center">
                  <el-cascader
                    v-model="form.search.regions"
                    style="margin-right: 10px; width: 170px"
                    :options="regionsOptions"
                    placeholder="选择区域"
                    :props="props"
                    multiple
                    clearable
                    @change="handleAreaChange"
                    :multiple="false"
                  ></el-cascader>
                </el-form-item>

                <el-form-item label="地铁" style="display: flex; align-items: center">
                  <el-select
                    v-model="form.search.subway"
                    multiple
                    placeholder="选择地铁线路"
                    @change="handleSubwayChange"
                    style="margin-right: 10px; width: 170px"
                  >
                    <el-option
                      v-for="(item, index) in subwayLines"
                      :key="index"
                      :label="item.name"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="环线" style="display: flex; align-items: center">
                  <el-select
                    v-model="form.search.loopLine"
                    multiple
                    placeholder="选择环线"
                    style="width: 170px"
                    @change="handleRingRoadChange"
                  >
                    <el-option
                      v-for="(item, index) in ringRoadLines"
                      :key="index"
                      :label="item.name"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-form-item>
              <el-form-item label="面积">
                <el-checkbox-group v-model="form.search.unitAreaOpt">
                  <el-checkbox
                    v-for="(item, index) in areaOptions"
                    :key="index"
                    :label="item.value"
                  >
                    {{ item.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="交付时间">
                <el-checkbox-group
                  v-model="form.search.handoverTime"
                  @change="handleHandoverTimeChange"
                >
                  <el-checkbox
                    v-for="(item, index) in deliveryYears"
                    :key="index"
                    :label="item.name"
                  >
                    {{ item.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="户型">
                <el-checkbox-group v-model="form.search.unitType">
                  <el-checkbox v-for="(item, index) in layouts" :key="index" :label="item.value">
                    {{ item.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="开发品牌">
                <el-select v-model="form.search.developer" multiple placeholder="选择开发品牌">
                  <el-option
                    v-for="(item, index) in developerRangs"
                    :key="index"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="产权类型">
                <el-checkbox-group v-model="form.search.propertyType">
                  <el-checkbox
                    v-for="(right, index) in propertyRights"
                    :key="index"
                    :label="right.value"
                  >
                    {{ right.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="学校">
                <el-checkbox-group v-model="form.search.school">
                  <el-checkbox
                    v-for="(school, index) in schools"
                    :key="index"
                    :label="school.value"
                  >
                    {{ school.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="交通">
                <el-checkbox-group v-model="form.search.traffic">
                  <el-checkbox
                    v-for="(transport, index) in transportations"
                    :key="index"
                    :label="transport.value"
                  >
                    {{ transport.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="商场">
                <el-checkbox-group v-model="form.search.shop">
                  <el-checkbox
                    v-for="(mall, index) in shoppingMalls"
                    :key="index"
                    :label="mall.value"
                  >
                    {{ mall.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="特色">
                <el-select v-model="form.search.feature" multiple placeholder="选择特色">
                  <el-option
                    v-for="(item, index) in featuresRangs"
                    :key="index"
                    :label="item.name"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="排序">
                <el-select v-model="form.search.sort" placeholder="选择排序方式">
                  <el-option label="默认排序" value="0"></el-option>
                  <el-option label="价格从低到高" value="1"></el-option>
                  <el-option label="价格从高到低" value="2"></el-option>
                  <el-option label="交房时间从近到远" value="3"></el-option>
                </el-select>
              </el-form-item>
            </el-tab-pane>
            <el-tab-pane label="自定义" name="custom">
              <el-form-item label="">
                <div class="w-full" style="display: flex; align-items: center">
                  <!-- 默认的楼盘输入框 -->
                  <el-select
                    v-model="form.search.customBuildings[0]"
                    filterable
                    remote
                    reserve-keyword
                    placeholder="请输入楼盘名称"
                    :remote-method="fetchBuildings"
                    :loading="loading"
                    :limit="50"
                    :style="{ width: '433px', marginRight: '10px' }"
                  >
                    <el-option
                      v-for="item in buildingOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>

                  <!-- 加号按钮 -->
                  <el-button @click="addBuildingField" type="primary" size="small" class="add-btn">
                    新增
                  </el-button>
                </div>

                <!-- 动态添加的楼盘选择框 -->
                <div class="building-selectors">
                  <div
                    v-for="(building, index) in form.search.customBuildings.slice(1)"
                    :key="index + 1"
                    class="selector-item"
                    style="display: flex; align-items: center; margin-bottom: 10px"
                  >
                    <!-- 楼盘选择框 -->
                    <el-select
                      v-model="form.search.customBuildings[index + 1]"
                      filterable
                      remote
                      reserve-keyword
                      placeholder="请输入楼盘名称"
                      :remote-method="fetchBuildings"
                      :loading="loading"
                      :limit="50"
                      class="building-select"
                    >
                      <el-option
                        v-for="item in buildingOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>

                    <!-- 删除楼盘按钮 -->
                    <el-button
                      @click="removeBuilding(index + 1)"
                      type="danger"
                      size="small"
                      class="remove-btn"
                    >
                      删除
                    </el-button>
                  </div>
                </div>

                <!-- 提示文字 -->
                <!-- <div class="hint-text" v-if="form.search.customBuildings.length > 0">
                最多可添加50个楼盘
              </div> -->
              </el-form-item>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-form>
      <template #footer>
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox, ElButton, ElSelect, ElOption } from 'element-plus'
import {
  getList,
  addItem,
  updateItem,
  deleteItems,
  changeState,
  getAreaOptions,
  getregionsOptions,
  getMores,
  info
} from '@/api/project/househot'
import { getHouseSelectAllList } from '@/api/project/house'
import screenHeight from '@/utils/screen-height'
import Pagination from '@/components/Pagination/index.vue'
import { arrayColumn } from '@/utils/index'
import { getPageLimit } from '@/utils/settings'
import { useRouter } from 'vue-router'

const router = useRouter()
const idkey = 'id'
const height = 680
const loading = ref(false)
const dialog = ref(false)
const dialogTitle = ref('')
const data = ref([])
const count = ref(0)
const formRef = ref(null)
const table = ref(null)
const selectedItems = ref([])
const customBuildings = ref([])
const query = reactive({
  page: 1,
  limit: 20,
  date_field: '',
  date_value: [],
  sort: '',
  state: 0
})
const stateSelectList = [
  { value: 0, label: '全部' },
  { value: '2', label: '发布' },
  { value: '1', label: '下架' }
]

const model = ref({
  id: '',
  title: null,
  pic: null,
  desc: null,
  link: null,
  state: 2,
  deal_time: '',
  subtitle: '',
  item_pic: '',
  background_color: ''
})

const retmodel = reactive({
  id: null,
  title: '',
  subtitle: '',
  item_pic: '',
  background_color: '',
  aggregationType: 'searchBased', // 添加默认值
  unitAreaOpt: [],
  subway: [],
  desc: '',
  pic: '',
  search: {
    name: '',
    unitPriceOpt: [],
    totalPriceOpt: [],
    minUnitPrice: null,
    maxUnitPrice: null,
    minTotalPrice: null,
    maxTotalPrice: null,
    customBuildings: [''],
    loopLine: [],
    handoverTime: [],
    unitType: [],
    developer: [],
    propertyType: [],
    school: [],
    traffic: [],
    shop: [],
    feature: [],
    sort: '',
    regions: []
  },
  customList: []
})
const form = reactive({
  title: '',
  aggregationType: 'searchBased', // 添加默认值
  desc: '',
  pic: '',
  unitAreaOpt: [],
  subway: [],
  search: {
    name: '',
    unitPriceOpt: [],
    totalPriceOpt: [],
    customBuildings: [''],
    minUnitPrice: null,
    maxUnitPrice: null,
    minTotalPrice: null,
    maxTotalPrice: null,
    loopLine: [],
    handoverTime: [],
    unitType: [],
    developer: [],
    propertyType: [],
    school: [],
    traffic: [],
    shop: [],
    feature: [],
    sort: '',
    regions: []
  },
  customList: [],
  image_url: '',
  is_watermark: 0
})
// 查询列表
const search = () => {
  query.page = 1
  list()
}

// 重置查询
const refresh = () => {
  Object.assign(query, {
    page: 1,
    limit: 20,
    search_field: '',
    search_exp: '',
    search_value: '',
    date_field: '',
    date_value: [],
    sort: '',
    title: '',
    state: ''
  })
  list()
}
// const reset = () => {
//   form.search.customBuildings = [] // 只清空 customBuildings，不需要初始化整个 form
// }
// 获取列表
const list = async () => {
  loading.value = true
  try {
    // 初始化 params
    const params = {
      page: query.page,
      limit: query.limit
    }

    // 有条件时才添加其他参数
    if (query.state !== undefined && query.state !== null) {
      params.status = query.state
    }

    if (query.title) {
      params.title = query.title
    }

    if (query.date_value && query.date_value.length === 2) {
      params.start_date = query.date_value[0] + ' 00:00:00'
      params.end_date = query.date_value[1] + ' 23:59:59'
    }

    const res = await getList(params)
    console.log(res)
    if (res && res.data) {
      data.value = Array.isArray(res.data.list) ? res.data.list : []
      count.value = res.data.count || 0
    } else {
      data.value = []
      count.value = 0
      console.error('Invalid response format:', res)
    }
  } catch (error) {
    ElMessage.error('获取列表失败')
    data.value = []
    count.value = 0
  } finally {
    loading.value = false
  }
}
function handleHeaderImageChange() {
  console.log(form.item_pic)
  if (form.item_pic !== '4') {
    form.pic = '' // 清除已上传的图片
  }
}
// 排序
const sort = ({ prop, order }) => {
  query.sort = order ? `${prop},${order}` : ''
  list()
}
const copyLink = (text) => {
  if (navigator.clipboard) {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        ElMessage.success('复制成功')
      })
      .catch((err) => {
        ElMessage.error('复制失败')
      })
  } else {
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    try {
      const successful = document.execCommand('copy')
      if (successful) {
        ElMessage.success('复制成功')
      } else {
        ElMessage.error('复制失败')
      }
    } catch (err) {
      ElMessage.error('复制失败')
    }
    document.body.removeChild(textArea)
  }
}

// 选择
const select = (selection) => {
  selectedItems.value = selection
}

const add = () => {
  // 跳转到新页面
  router.push({
    path: '/info/househotadd'
  })
}
// // 新增
// const add = () => {
//   reset()
//   dialogTitle.value = '添加'
//   dialog.value = true
// }

// 获取面积选项
const areaOptions = ref([])
const unitPriceOptions = ref([])
const totalPriceOptions = ref([])
const subwayLines = ref([])
const ringRoadLines = ref([])
const deliveryYears = ref([])
const layouts = ref([])
const developerRangs = ref([])
const shoppingMalls = ref([])
const propertyRights = ref([])
const regionsOptions = ref([])
const regionId = ref(null) // 转换后的 regionId
const plateId = ref([]) // 转换后的 plateId
const schools = ref([])
const transportations = ref([])
const featuresRangs = ref([])
const buildingOptions = ref([])
const props = ref({
  multiple: true,
  value: 'id', // 对应每个选项的值
  label: 'name', // 对应每个选项的标签
  children: 'children' // 对应子级选项
})
// 获取楼盘列表的函数
const fetchBuildings = async (query, selectedIds = []) => {
  loading.value = true
  try {
    const params = {}

    if (query) {
      params.name = query
    }
    params.limit = 100
    if (selectedIds.length > 0) {
      params.ids = selectedIds.join(',')
    }
    const res = await getHouseSelectAllList(params)
    if (res && res.data) {
      buildingOptions.value = res.data.list.map((item) => ({
        label: item.name,
        value: item.id
      }))
    } else {
      this.buildingOptions = []
      console.error('接口返回数据格式不正确:', res)
    }
  } catch (error) {
    console.error('获取楼盘列表失败', error)
  } finally {
    loading.value = false
  }
}
// 添加新的楼盘选择框
const addBuildingField = () => {
  if (form.search.customBuildings.length < 50) {
    form.search.customBuildings.push('') // 添加新的输入框
  }
}
// 获取楼盘标签名称
const getBuildingLabel = (value) => {
  const option = buildingOptions.value.find((opt) => opt.value === value)

  return option ? option.label : value
}

// 删除楼盘
const removeBuilding = (index) => {
  if (form.search.customBuildings.length > 1) {
    form.search.customBuildings.splice(index, 1) // 删除指定的楼盘
  }
}
const fetchRegionsOptions = async () => {
  loading.value = true
  try {
    const params = {}

    const res = await getregionsOptions(params)

    if (res && res.data) {
      const regions = Array.isArray(res.data.regions) ? res.data.regions : []

      regionsOptions.value = [
        {
          id: null,
          name: '不限',
          isDisabled: false
        },
        ...regions
      ]
    } else {
      regionsOptions.value = []
      console.error('Invalid response format:', res)
    }
  } catch (error) {
    ElMessage.error('获取区域选项失败')
    regionsOptions.value = []
  } finally {
    loading.value = false
  }
}

const handleSubwayChange = (value) => {
  if (value.length > 0) {
    // 清空其他两个下拉框
    form.search.regions = []
    form.search.loopLine = []
  }
}

const handleRingRoadChange = (value) => {
  if (value.length > 0) {
    // 清空其他两个下拉框
    form.search.regions = []
    form.search.subway = []
  }
}
const fetchRingRoadLines = async () => {
  loading.value = true
  try {
    const res = await getregionsOptions()
    if (res && res.data) {
      ringRoadLines.value = Array.isArray(res.data.loopline) ? res.data.loopline : []
    } else {
      ringRoadLines.value = []
      console.error('Invalid response format:', res)
    }
  } catch (error) {
    ElMessage.error('获取环线数据失败')
    ringRoadLines.value = []
  } finally {
    loading.value = false
  }
}
// 获取地铁线路数据
const fetchSubwayLines = async () => {
  loading.value = true
  try {
    const res = await getregionsOptions()
    if (res && res.data) {
      console.log(res.data)
      subwayLines.value = Array.isArray(res.data.subways) ? res.data.subways : []
    } else {
      subwayLines.value = []
      console.error('Invalid response format:', res)
    }
  } catch (error) {
    ElMessage.error('获取地铁线路失败')
    subwayLines.value = []
  } finally {
    loading.value = false
  }
}
const fetchAreaOptions = async () => {
  loading.value = true
  try {
    const params = {}

    const res = await getAreaOptions(params)
    if (res && res.data) {
      areaOptions.value = Array.isArray(res.data.areas) ? res.data.areas : []
    } else {
      areaOptions.value = []
      console.error('Invalid response format:', res)
    }
  } catch (error) {
    ElMessage.error('获取面积选项失败')
    areaOptions.value = []
  } finally {
    loading.value = false
  }
}
function handleAreaChange(value) {
  if (value.length) {
    let id = value[value.length - 1][0]
    form.search.regions = value.filter((item) => {
      return item[0] == id
    })
    value = form.search.regions
  }

  if (value.length > 0) {
    form.search.subway = []
    form.search.loopLine = []
    // regionId 只取第一个区域 ID
    regionId.value = value[0][0] // 选择的区域ID

    // plateId 根据选择的板块 ID 可以选择多个
    plateId.value = value.map((item) => item[1]) // 获取所有板块ID

    // 将值同步到表单数据中
    form.search.regionId = regionId.value
    form.search.plateId = plateId.value
  } else {
    // 如果没有选择任何区域或板块，清空 regionId 和 plateId
    regionId.value = null
    plateId.value = []
    form.search.regionId = null
    form.search.plateId = []
  }
}

// 获取商场数据
const fetchShoppingMalls = async () => {
  loading.value = true
  try {
    const params = {}
    const res = await getMores(params)
    if (res && res.data) {
      shoppingMalls.value = Array.isArray(res.data.peitaoShop) ? res.data.peitaoShop : []
    } else {
      shoppingMalls.value = []
      console.error('Invalid response format:', res)
    }
  } catch (error) {
    ElMessage.error('获取商场选项失败')
    shoppingMalls.value = []
  } finally {
    loading.value = false
  }
}
// 获取产权类型
const fetchPropertyRights = async () => {
  loading.value = true
  try {
    const params = {}
    const res = await getMores(params)
    if (res && res.data) {
      propertyRights.value = Array.isArray(res.data.propertyType) ? res.data.propertyType : []
    } else {
      propertyRights.value = []
      console.error('Invalid response format:', res)
    }
  } catch (error) {
    ElMessage.error('获取产权类型选项失败')
    propertyRights.value = []
  } finally {
    loading.value = false
  }
}

// 获取学校
const fetchSchools = async () => {
  loading.value = true
  try {
    const params = {} // 根据需要传递的参数
    const res = await getMores(params) // 假设 getSchools 是获取学校选项的接口
    if (res && res.data) {
      schools.value = Array.isArray(res.data.peitaoSchool) ? res.data.peitaoSchool : []
    } else {
      schools.value = []
      console.error('Invalid response format:', res)
    }
  } catch (error) {
    ElMessage.error('获取学校选项失败')
    schools.value = []
  } finally {
    loading.value = false
  }
}

// 获取交通
const fetchTransportations = async () => {
  loading.value = true
  try {
    const params = {}
    const res = await getMores(params)
    if (res && res.data) {
      transportations.value = Array.isArray(res.data.peitaoTraffic) ? res.data.peitaoTraffic : []
    } else {
      transportations.value = []
      console.error('Invalid response format:', res)
    }
  } catch (error) {
    ElMessage.error('获取交通选项失败')
    transportations.value = []
  } finally {
    loading.value = false
  }
}
const fetchFeaturesRangs = async () => {
  loading.value = true
  try {
    const params = {}

    const res = await getMores(params)
    if (res && res.data) {
      featuresRangs.value = Array.isArray(res.data.peitaoFeature) ? res.data.peitaoFeature : []
    } else {
      featuresRangs.value = []
      console.error('Invalid response format:', res)
    }
  } catch (error) {
    ElMessage.error('获取面积选项失败')
    featuresRangs.value = []
  } finally {
    loading.value = false
  }
}
const fetchDeveloperRangs = async () => {
  loading.value = true
  try {
    const params = {}

    const res = await getMores(params)
    if (res && res.data) {
      developerRangs.value = Array.isArray(res.data.developer) ? res.data.developer : []
    } else {
      developerRangs.value = []
      console.error('Invalid response format:', res)
    }
  } catch (error) {
    ElMessage.error('获取面积选项失败')
    developerRangs.value = []
  } finally {
    loading.value = false
  }
}
const fetchDeliveryTimeOptions = async () => {
  loading.value = true
  try {
    const params = {}

    const res = await getMores(params)
    if (res && res.data) {
      console.log(res.data)
      deliveryYears.value = Array.isArray(res.data.handoverTime) ? res.data.handoverTime : []
    } else {
      deliveryYears.value = []
      console.error('Invalid response format:', res)
    }
  } catch (error) {
    ElMessage.error('获取交付时间选项失败')
    deliveryYears.value = []
  } finally {
    loading.value = false
  }
}
const handleHandoverTimeChange = (value) => {
  // 筛选出有效的交付时间 name
  form.search.handoverTime = value.filter((item) =>
    deliveryYears.value.some((year) => year.name === item)
  )
  console.log('选择的交付时间:', form.search.handoverTime)
}
const fetchLayouts = async () => {
  loading.value = true
  try {
    const params = {}

    const res = await getMores(params)
    if (res && res.data) {
      layouts.value = Array.isArray(res.data.houseType) ? res.data.houseType : []
    } else {
      layouts.value = []
      console.error('Invalid response format:', res)
    }
  } catch (error) {
    ElMessage.error('获取户型选项失败')
    layouts.value = []
  } finally {
    loading.value = false
  }
}
const fetchUnitPriceOptions = async () => {
  loading.value = true
  try {
    const params = {}
    const res = await getAreaOptions(params)
    if (res && res.data) {
      unitPriceOptions.value = Array.isArray(res.data.unitPrice) ? res.data.unitPrice : []
    } else {
      unitPriceOptions.value = []
      console.error('Invalid response format:', res)
    }
  } catch (error) {
    ElMessage.error('获取单价选项失败')
    unitPriceOptions.value = []
  } finally {
    loading.value = false
  }
}

const fetchTotalPriceOptions = async () => {
  loading.value = true
  try {
    const params = {}
    const res = await getAreaOptions(params)
    if (res && res.data) {
      totalPriceOptions.value = Array.isArray(res.data.totalPrice) ? res.data.totalPrice : []
    } else {
      totalPriceOptions.value = []
      console.error('Invalid response format:', res)
    }
  } catch (error) {
    ElMessage.error('获取总价选项失败')
    totalPriceOptions.value = []
  } finally {
    loading.value = false
  }
}
// 编辑
const edit = async (row) => {
  // try {
  fetchBuildings()
  const detailData = await info({ id: row.id })
  console.log(detailData)
  Object.assign(form, detailData.data)
  form.search = detailData.data.search_item
  form.search.customBuildings = form.search.customBuildings ? form.search.customBuildings : []
  await fetchBuildings('', form.search.customBuildings)
  if (detailData.data.is_custom === 1) {
    form.aggregationType = 'custom'
  }
  dialogTitle.value = '修改'

  dialog.value = true
  // } catch (error) {
  //   ElMessage.error('查询详情失败')
  // }
}

// 提交
const submit = async () => {
  //searchBased = 搜索  custom=自定义
  if (form.aggregationType == 'searchBased') {
    form.is_custom = 0
    form.search.customBuildings = []
  } else {
    var customBuildings = form.search.customBuildings
    ;(form.is_custom = 1), //自定义
      (form.search = {
        ...form.search, // 保留其他可能的属性
        name: '',
        unitPrice: [],
        totalPrice: [],
        customBuildings: customBuildings,
        minUnitPrice: null,
        maxUnitPrice: null,
        minTotalPrice: null,
        maxTotalPrice: null,
        loopLine: [],
        handoverTime: [],
        unitType: [],
        developer: [],
        propertyRights: [],
        school: [],
        traffic: [],
        shoppingMalls: [],
        feature: [],
        sort: '',
        regions: [],
        regionId: form.search.regionId, // 确保包含 regionId
        plateId: form.search.plateId // 确保包含 plateId
      })
  }
  console.log('form.aggregationType--', form)
  if (!formRef.value) return
  // try {
  const valid = await formRef.value.validate()
  if (valid) {
    loading.value = true
    if (form.id) {
      console.log(form)
      await updateItem(form)
      reset()
      ElMessage.success('修改成功')
    } else {
      console.log(form)
      await addItem(form)
      reset()
      ElMessage.success('添加成功')
    }
    dialog.value = false
    await list()
  }

  // } catch (error) {
  //   ElMessage.error('提交失败')
  // } finally {
  //   loading.value = false
  // }
}

// 取消
const cancel = () => {
  dialog.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 删除选中的项
const selectOpen = (type, rows = []) => {
  const items = rows.length > 0 ? rows : selectedItems.value
  if (items.length === 0) {
    return ElMessage.warning('请选择要操作的数据')
  }
  if (type === 'dele') {
    ElMessageBox.confirm('确定要删除选中的数据吗？', '提示', { type: 'warning' })
      .then(() => deleteSelected(items))
      .catch(() => {})
  }
}

// 删除选中的数据
const deleteSelected = async (items) => {
  loading.value = true
  try {
    await deleteItems(items.map((item) => item[idkey]))
    ElMessage.success('删除成功')
    list()
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  } finally {
    loading.value = false
  }
}

// 修改状态
const changeStateButton = async (id, status) => {
  loading.value = true
  try {
    await changeState({ id, status })
    ElMessage.success('编辑成功')
    list()
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  } finally {
    loading.value = false
  }
}

// 处理总价变化，清空单价数据和多选框
const onTotalPriceChange = () => {
  // 如果总价框有值，清空单价数据和多选框
  if (form.search.minTotalPrice || form.search.maxTotalPrice) {
    form.search.minUnitPrice = null
    form.search.maxUnitPrice = null
    form.search.unitPriceOpt = []
    form.search.totalPriceOpt = []
  }
}

// 处理单价变化，清空总价数据和多选框
const onUnitPriceChange = () => {
  // 如果单价框有值，清空总价数据和多选框
  if (form.search.minUnitPrice || form.search.maxUnitPrice) {
    form.search.minTotalPrice = null
    form.search.maxTotalPrice = null
    form.search.totalPriceOpt = []
    form.search.unitPriceOpt = []
  }
}

// 处理总价多选框变化，清空单价的数据和多选框
const onTotalPriceOptionChange = () => {
  // 如果总价的多选框有值，清空单价的输入框和多选框
  if (form.search.totalPriceOpt.length > 0) {
    form.search.minUnitPrice = null
    form.search.maxUnitPrice = null
    form.search.unitPriceOpt = []
  }

  if (form.search.totalPriceOpt.length > 0) {
    form.search.maxTotalPrice = null
    form.search.minTotalPrice = null
  }
}

// 处理单价多选框变化，清空总价的数据和多选框
const onUnitPriceOptionChange = () => {
  // 如果单价的多选框有值，清空总价的输入框和多选框
  if (form.search.unitPriceOpt.length > 0) {
    form.search.minTotalPrice = null
    form.search.maxTotalPrice = null
    form.search.totalPriceOpt = []
  }

  // 清空单价的输入框
  if (form.search.unitPriceOpt.length > 0) {
    form.search.minUnitPrice = null
    form.search.maxUnitPrice = null
  }
}

// 页面加载
onMounted(() => {
  nextTick(() => {
    console.log('Component mounted, formRef:', formRef.value)
  })
  list()
  fetchAreaOptions()
  fetchUnitPriceOptions()
  fetchTotalPriceOptions()
  fetchRegionsOptions()
  fetchSubwayLines()
  fetchRingRoadLines()
  fetchDeliveryTimeOptions()
  fetchLayouts()
  fetchDeveloperRangs()
  fetchShoppingMalls()
  fetchPropertyRights()
  fetchSchools()
  fetchTransportations()
  fetchFeaturesRangs()
})
// watch([() => form.name], () => {
//   search()
// })
// 重置表单
function reset() {
  Object.assign(form, retmodel)
}
</script>

<style scoped>
.property-filter {
  font-family: Arial, sans-serif;
  padding: 20px;
  background-color: #f5f7fa;
}

.filter-section {
  background-color: #ffffff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #303133;
}

:deep(.el-form-item__label) {
  font-weight: bold;
  color: #606266;
}

:deep(.el-checkbox-button__inner) {
  border: 1px solid #dcdfe6;
  background-color: #fff;
  color: #606266;
  border-radius: 4px !important;
  margin-right: 10px;
  margin-bottom: 10px;
}

:deep(.el-checkbox-button.is-checked .el-checkbox-button__inner) {
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.el-checkbox__label) {
  color: #606266;
}

:deep(.el-select) {
  width: 100%;
}

.mt-2 {
  margin-top: 8px;
}

.mt-4 {
  margin-top: 16px;
}

.fixed-button {
  position: fixed;
  left: 20px;
  top: 20px;
  z-index: 1000;
}

.building-selectors {
  /* display: flex;
  flex-direction: column; */
  /* gap: 15px; */
  margin-top: 26px;
  width: 100%;
  max-width: 600px;
}

.selector-item {
  width: 100%;
}

.selector-item :deep(.el-select) {
  width: 100%;
}

.hint-text {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.selected-buildings {
  margin-top: 16px;
  width: 100%;
}

.title {
  font-weight: bold;
  margin-right: 8px;
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 10px 0;
}

.building-tag {
  cursor: move;
  margin: 0;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb !important;
}

/* 拖 */
/* 添加一些样式来改进拖动体验 */
.draggable-building-selectors {
  /* display: flex; */
  /* flex-direction: column; */
}

.selector-item {
  margin-bottom: 10px;
  position: relative;
}

.draggable-select {
  width: 100%;
}

.drag-handle {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  cursor: move;
  color: #999;
}

.ghost {
  opacity: 0.4;
}

.building-selectors {
  margin-top: 10px;
}

.selector-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.hint-text {
  color: #999;
  margin-top: 10px;
}
/* .image-upload-description {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
} */
</style>
