<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-form ref="queryRef" :model="query" :inline="true" label-width="90px">
      <el-form-item label="更新时间：" prop="update_time">
        <el-date-picker
          v-model="query.update_time"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择时间"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="上线时间：" prop="online_time">
        <el-date-picker
          v-model="query.online_time"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择时间"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
        <el-button type="success" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格部分 -->
    <el-table
      v-if="tableData.length >= 0"
      ref="tableRef"
      v-loading="loading"
      :data="tableData"
      style="width: 100%; margin-top: 20px"
      row-key="id"
    >
      <el-table-column prop="id" label="编号" width="80" />
      <el-table-column prop="update_time" label="更新时间" width="180" />
      <el-table-column prop="one_year_lpr" label="一年期LPR" width="120">
        <template #default="{ row }"> {{ row.one_year_lpr }}% </template>
      </el-table-column>
      <el-table-column prop="five_year_lpr" label="五年期以上LPR" width="150">
        <template #default="{ row }"> {{ row.five_year_lpr }}% </template>
      </el-table-column>
      <el-table-column prop="first_house_rate" label="首套利率" width="120">
        <template #default="{ row }"> {{ row.first_house_rate }}% </template>
      </el-table-column>
      <el-table-column prop="second_house_rate" label="二套利率" width="120">
        <template #default="{ row }"> {{ row.second_house_rate }}% </template>
      </el-table-column>
      <el-table-column prop="other_rate" label="其他利率" width="120">
        <template #default="{ row }"> {{ row.other_rate }}% </template>
      </el-table-column>
      <el-table-column prop="housing_first_fund_rate" label="首套利率(公积金)" width="120">
        <template #default="{ row }"> {{ row.housing_first_fund_rate }}% </template>
      </el-table-column>
      <el-table-column prop="housing_second_fund_rate" label="二套利率(公积金)" width="120">
        <template #default="{ row }"> {{ row.housing_second_fund_rate }}% </template>
      </el-table-column>
      <el-table-column prop="online_time" label="上线时间" width="180" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleEdit(row)">编辑</el-button>
          <el-button
            link
            :type="row.status ? 'success' : 'primary'"
            @click="handleStatusChange(row)"
          >
            {{ row.status ? '下线' : '上线' }}
          </el-button>
          <el-button link type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      v-model:total="total"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="getList"
    />

    <!-- 编辑弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="更新时间" prop="update_time">
          <el-date-picker
            v-model="form.update_time"
            type="date"
            placeholder="选择时间"
            value-format="YYYY-MM-DD"
            :disabled-date="(time) => false"
            style="width: 50%"
          />
        </el-form-item>
        <el-form-item label="一年期LPR" prop="one_year_lpr">
          <el-input-number v-model="form.one_year_lpr" :precision="3" :step="0.001" :min="0" />
        </el-form-item>
        <el-form-item label="五年期以上LPR" prop="five_year_lpr">
          <el-input-number v-model="form.five_year_lpr" :precision="3" :step="0.001" :min="0" />
        </el-form-item>
        <el-form-item label="首套利率" prop="first_house_rate">
          <el-input-number v-model="form.first_house_rate" :precision="3" :step="0.001" :min="0" />
        </el-form-item>
        <el-form-item label="二套利率" prop="second_house_rate">
          <el-input-number v-model="form.second_house_rate" :precision="3" :step="0.001" :min="0" />
        </el-form-item>
        <el-form-item label="其他利率" prop="other_rate">
          <el-input-number v-model="form.other_rate" :precision="3" :step="0.001" :min="0" />
        </el-form-item>
        <el-form-item label="首套利率(公积金)" prop="housing_first_fund_rate">
          <el-input-number
            v-model="form.housing_first_fund_rate"
            :precision="3"
            :step="0.001"
            :min="0"
          />
        </el-form-item>
        <el-form-item label="二套利率(公积金)" prop="housing_second_fund_rate">
          <el-input-number
            v-model="form.housing_second_fund_rate"
            :precision="3"
            :step="0.001"
            :min="0"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false" :disabled="submitLoading">取 消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Pagination from '@/components/Pagination/index.vue'
import request from '@/utils/request'

// 状态变量
const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('新增')
const formRef = ref(null)
const queryRef = ref(null)
const tableData = ref([])
const total = ref(0)

// 查询参数
const query = ref({
  page: 1,
  limit: 10,
  update_time: '',
  online_time: ''
})

// 表单数据
const initForm = {
  one_year_lpr: 3.1,
  five_year_lpr: 3.6,
  first_house_rate: 3.15,
  second_house_rate: 3.33,
  other_rate: 3.6,
  update_time: '',
  status: 0,
  housing_first_fund_rate: 3.25,
  housing_second_fund_rate: 3.75
}

const form = ref({ ...initForm })

// 表单验证规则
const rules = {
  one_year_lpr: [{ required: true, message: '请输入一年期LPR', trigger: 'blur' }],
  five_year_lpr: [{ required: true, message: '请输入五年期以上LPR', trigger: 'blur' }],
  first_house_rate: [{ required: true, message: '请输入首套利率', trigger: 'blur' }],
  second_house_rate: [{ required: true, message: '请输入二套利率', trigger: 'blur' }],
  housing_first_fund_rate: [{ required: true, message: '请输入公积金一套利率', trigger: 'blur' }],
  housing_second_fund_rate: [{ required: true, message: '请输入公积金二套利率', trigger: 'blur' }],
  other_rate: [{ required: true, message: '请输入其他利率', trigger: 'blur' }],
  update_time: [{ required: true, message: '请选择更新时间', trigger: 'change' }]
}

// API调用
const api = {
  info: (params) => request.get('/admin/tools.loanCalculator/info', { params }),
  list: (params) => request.get('/admin/tools.loanCalculator/list', { params }),
  save: (data) => request.post('/admin/tools.loanCalculator/save', data),
  delete: (id) => request.get(`/admin/tools.loanCalculator/delete?id=${id}`),
  changeStatus: (id, status) =>
    request.get(`/admin/tools.loanCalculator/changeStatus`, { params: { id, status } })
}

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const res = await api.list(query.value)
    if (res.code === 200) {
      tableData.value = res.data.list
      total.value = res.data.total
    }
  } catch (error) {
    console.error('获取列表失败:', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

// 查询处理
const handleQuery = () => {
  query.value.page = 1
  getList()
}

// 重置查询
const resetQuery = () => {
  queryRef.value?.resetFields()
  query.value = {
    page: 1,
    limit: 10,
    update_time: '',
    online_time: ''
  }
  getList()
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增'
  form.value = { ...initForm }
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  dialogTitle.value = '编辑'
  form.value = { ...row }
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除该记录吗？`, '提示', {
      type: 'warning'
    })
    const res = await api.delete(row.id)
    if (res.code === 200) {
      ElMessage.success('删除成功')
      getList()
    }
  } catch (error) {
    console.error('删除失败:', error)
  }
}

// 状态变更
const handleStatusChange = async (row) => {
  try {
    const newStatus = row.status ? 0 : 1
    const res = await api.changeStatus(row.id, newStatus)
    if (res.code === 200) {
      ElMessage.success(`${newStatus ? '上线' : '下线'}成功`)
      getList()
    }
  } catch (error) {
    console.error('状态更改失败:', error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    submitLoading.value = true
    await formRef.value.validate()
    const res = await api.save(form.value)
    if (res.code === 200) {
      ElMessage.success(form.value.id ? '编辑成功' : '新增成功')
      dialogVisible.value = false
      getList()
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitLoading.value = false
  }
}

// 初始化
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.dialog-footer {
  padding: 20px 0 0;
  text-align: right;
}
</style>
