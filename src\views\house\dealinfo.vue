<template>
  <div class="app-container">
    <div class="bg-[#ffffff] p-[10px] border-rd-10px mb-[10px]">
      <el-tabs v-model="activeModel">
        <el-tab-pane label="网签数据管理" name="Netsign">
          <Netsign />
        </el-tab-pane>

        <el-tab-pane label="周度项目成交管理" lazy name="WeekDetailList">
          <WeekDetailList />
        </el-tab-pane>

        <el-tab-pane label="月度项目成交管理" lazy name="MonthDetailList">
          <MonthDetailList />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import Netsign from './components/deal/Netsign.vue'
import MonthData from './components/deal/MonthData.vue'
import WeekData from './components/deal/WeekData.vue'
import WeekDetailList from './components/deal/WeekDetailList.vue'
import MonthDetailList from './components/deal/MonthDetailList.vue'

export default {
  name: 'HouseDeal',
  components: {
    Netsign,
    MonthData,
    WeekData,
    WeekDetailList,
    MonthDetailList
  },
  data() {
    return {
      name: '网签数据管理',
      activeModel: 'Netsign'
    }
  },
  methods: {
    checkPermission
  }
}
</script>
