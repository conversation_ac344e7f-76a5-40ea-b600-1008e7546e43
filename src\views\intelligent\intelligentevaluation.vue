<template>
  <div class="app-container">
    <!-- 查询 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="18">
        <el-form :inline="true" :model="query" >
          <el-form-item label="楼盘名称">
            <el-select v-model="query.house_id" placeholder="请选择楼盘名称" :style="{width: '180px'}">
              <el-option v-for="house in houseList" :key="house.id" :label="house.name" :value="house.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="智能级别">
              <el-select v-model="query.level" placeholder="请选择" :style="{width: '180px'}">
                <el-option v-for="([value, label], index) in Object.entries(levelMap)" :key="index" :value="value" :label="label" />
              </el-select>
            </el-form-item>
          <!-- <el-form-item label="亮点功能">
            <el-select v-model="query.highlight_feature" placeholder="请选择" :style="{width: '180px'}">
              <el-option value="1" label="智能安防" />
              <el-option value="2" label="智能照明" />
              <el-option value="3" label="智能温控" />
              <el-option value="4" label="智能家电" />
            </el-select>
          </el-form-item>
          <el-form-item label="包含产品">
            <el-select v-model="query.included_product" placeholder="请选择" :style="{width: '180px'}">
              <el-option value="1" label="智能门锁" />
              <el-option value="2" label="智能开关" />
              <el-option value="3" label="智能窗帘" />
              <el-option value="4" label="智能音箱" />
            </el-select>
          </el-form-item> -->
          <el-form-item label="发布状态">
            <el-select v-model="query.release_status" placeholder="请选择" :style="{width: '180px'}">
              <el-option :value="0" label="全部" />
              <el-option :value="1" label="未发布" />
              <el-option :value="2" label="已发布" />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="query.create_time"
              type="datetimerange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD HH:mm:ss"
              :default-time="[new Date(2024, 1, 1, 0, 0, 0), new Date(2024, 1, 1, 23, 59, 59)]"
            />
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="6" style="display: flex; align-items: center; justify-content: flex-end;">
        <el-form :inline="true">
          <el-form-item>
            <el-button type="primary" @click="search()">查询</el-button>
            <el-button @click="refresh()">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <!-- 操作按钮 -->
    <!-- 操作按钮 -->
    <el-row class="mb-4">
      <el-col :span="24" class="text-right">
        <el-button type="primary" @click="openAddDialog()">添加</el-button>
      </el-col>
    </el-row>

    <!-- 表格布局 -->
    <el-table
      v-loading="loading"
      :data="evaluationList"
      style="width: 100%"
      border
    >
      <el-table-column prop="id" label="评测ID" width="80" />
      <el-table-column prop="house_name" label="楼盘名称" width="180" />
      <el-table-column prop="count_score" label="智能总评" width="80" />
      <el-table-column prop="level" label="智能级别" width="80">
        <template #default="{ row }">
          {{ levelMap[row.level] || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="cover_score" label="覆盖评分" width="80" />
      <el-table-column prop="fun_score" label="功能评分" width="80" />
      <el-table-column prop="update_time" label="更新时间" width="180" />
      <el-table-column prop="release_status" label="发布状态" width="120">
        <template #default="{ row }">
          <div class="status-container">
            <span>{{ releaseStatusMap[row.release_status] || '-' }}</span>
            <el-switch
              :model-value="row.release_status === 2"
              @update:model-value="(value) => handleStatusChange(row, value)"
              active-color="#13ce66"
              inactive-color="#ff4949"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button type="text" @click="openEditDialog(row)">编辑</el-button>
          <el-button type="text" @click="deleteItem(row)" class="text-danger">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
     
      <pagination
      v-show="count > 0"
      v-model:total="count"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="getList"
    />
    </div>

    <!-- 产品编辑对话框 -->
<el-dialog
  v-model="productDialogVisible"
  title="详细评测编辑"
  :close-on-click-modal="false"
  :close-on-press-escape="false"
  width="500px"
>
  <el-form ref="productFormRef" :model="productFormData" :rules="productFormRules" label-width="120px">
    <el-form-item label="产品" prop="ipc_id" required>
      <el-select v-model="productFormData.ipc_id" placeholder="选择产品，支持搜索" filterable @change="handleProductChange">
        <el-option v-for="item in productOptions" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
    </el-form-item>
    <el-form-item label="所在空间" prop="space_ids" required>
      <el-select v-model="productFormData.space_ids" placeholder="选择空间，支持多选" multiple>
        <el-option v-for="item in spaceOptions" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
    </el-form-item>
    <el-form-item label="选择品牌" prop="brand_id" required>
      <el-select v-model="productFormData.brand_id" placeholder="请先选择品牌">
        <el-option v-for="item in brandOptions" :key="item.id" :label="item.union_brand" :value="item.id" :disabled="item.disabled" />
      </el-select>
    </el-form-item>
    <el-form-item label="产品数量" prop="num" required>
      <el-input v-model.number="productFormData.num" type="number" placeholder="输入数量" min="1" />
    </el-form-item>
    <el-form-item label="产品功能" prop="fun_ids" required>
        <el-select v-model="productFormData.fun_ids" placeholder="请先选择产品，功能支持多选" multiple :disabled="productFormData.fun_ids_disabled">
          <el-option v-for="item in functionOptions" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
  </el-form>

  <template #footer>
    <el-button @click="productDialogVisible = false">取消</el-button>
    <el-button type="primary" @click="saveProduct" :loading="isSubmitting" :disabled="productOptions.length === 0 || spaceOptions.length === 0 || brandOptions.length === 0 || functionOptions.length === 0">确定</el-button>
  </template>
</el-dialog>

<!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="80%"
    >
      <div class="dialog-content">
        <!-- 基础信息和评分信息 -->
        <el-form ref="formRef" :model="formData" >
        <div class="info-container">
          <div class="basic-info">
            <h3>基础信息</h3>
            <el-form-item label="楼盘名称" prop="house_id">
              <el-select v-model="formData.house_id" placeholder="请选择楼盘名称" :disabled="isEditMode">
                <el-option v-for="house in houseList" :key="house.id" :label="house.name" :value="house.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="一句话点评">
              <el-input v-model="formData.remark" type="textarea" placeholder="请输入描述" :rows="4" />
            </el-form-item>
          </div>
          <div class="score-info">
            <h3>评分信息</h3>
            <el-form-item label="智能总评" prop="count_score">
              <el-input v-model="formData.count_score" type="number" placeholder="请输入智能总评分数" />
            </el-form-item>
            <el-form-item label="智能级别" prop="level">
              <el-select v-model="formData.level" placeholder="请选择智能级别">
                <el-option v-for="([value, label], index) in Object.entries(levelMap)" :key="index" :value="value" :label="label" />
              </el-select>
            </el-form-item>
            <el-form-item label="覆盖评分" prop="cover_score">
              <el-input v-model="formData.cover_score" type="number" placeholder="请输入覆盖评分" />
            </el-form-item>
            <el-form-item label="功能评分" prop="fun_score">
              <el-input v-model="formData.fun_score" type="number" placeholder="请输入功能评分" />
            </el-form-item>
          </div>
        </div>

        <!-- 智能图集 -->
        <div class="image-gallery">
          <h3>智能图集</h3>
         
                          
        <div class="upload-container w-[500px]">
        <ImgUploadsHouseType
            v-model="formData.intellect_pics"
            :upload-btn="formData.intellect_pics?.length ? '' : '上传图片'"
            file-type="image"
            :height="100"
            :width="100"

            isWatermark="0"
            source="0"
        />
        
        </div>
          <div class="file-tip">
            视频支持mp4格式，图片支持jpg、png格式，大小不超过500M
          </div>
        </div>

        <!-- 详细评测tabs - 仅编辑时显示 -->
        <el-tabs v-if="isEditMode" class="detail-tabs" tab-position="top">
          <el-tab-pane label="详细评测">
            <div class="tab-content">
              <!-- 详细评测内容 -->
              <div class="bottom-actions">
                <el-checkbox v-model="autoCalculateScore">自动刷新评分信息</el-checkbox>
                <div class="action-buttons">
                  <!-- <el-button type="primary" @click="batchImport">批量导入</el-button> -->
                  <intelligentDetailImport title="上传文件" @on-import="imports" :startRow="1" :headerRowIndex="2" />

                  <el-button type="primary" @click="openAddProductDialog">添加</el-button>
                </div>
              </div>

              <!-- 产品列表表格 -->
              <el-table :data="productList" style="width: 100%" border>
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column type="index" label="序号" width="80" />
                <el-table-column prop="product_name" label="产品" width="180" />
                <el-table-column prop="class_name" label="所属品类" width="180" />
                <el-table-column label="所在空间" width="180">
                  <template #default="scope">
                    {{ scope.row.space_name && scope.row.space_name.join('、') || '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="brand_name" label="品牌" width="180" />
                <el-table-column prop="num" label="数量" width="80" />
                <el-table-column label="功能" width="250">
                  <template #default="scope">
                    {{ scope.row.fun_name && scope.row.fun_name.join('、') || '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150" fixed="right">
                  <template #default="scope">
                    <el-button type="primary" size="small" @click="handleProductEdit(scope.row)">编辑</el-button>
                    <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 分页已移除 -->

    
              
            </div>
          </el-tab-pane>
          <el-tab-pane label="功能亮点" name="highlights">
            <div class="tab-content">
              <!-- 功能展示 -->
              <div class="function-display">
                <h3>功能展示</h3>
                <div class="upload-container">
                  <ImgUpload
                    :isWatermark="0"
                    :source="0"
                   
                    v-model:file-url="formData.fun_img_url"
                    file-type="image"
                    :height="100"
                    upload
                />
                </div>
              </div>

              <!-- 功能亮点表格 -->
              <div class="highlight-table-container">
                <h3>功能亮点</h3>
                <el-table
                  ref="highlightTableRef"
                  :data="highlightsData"
                  style="width: 100%"
                  border
                  row-key="id"
                  @selection-change="handleHighlightSelectionChange"
                >
                  <el-table-column type="selection" width="55" />
                  <el-table-column prop="name" label="亮点标题" width="180" />
                  <el-table-column prop="scene_type" label="分类" width="180" />
                  <el-table-column prop="desc" label="亮点描述" width="350" />
                  <el-table-column prop="product_list" label="关联产品" width="250">
                    <template #default="scope">
                      <div v-if="scope.row.product_list && scope.row.product_list.length" class="related-products">
                        <div class="product-blocks">
                          <div v-for="(product, index) in scope.row.product_list" :key="index" class="product-block">
                            {{ product }}
                          </div>
                        </div>
                      </div>
                      <span v-else>-</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="实拍体验">
            <div class="tab-content">
              <!-- 实拍体验内容 -->
              <el-form-item label="实拍体验">
                <!-- <el-input v-model="formData.experience" type="textarea" placeholder="请输入实拍体验" :rows="10" /> -->
                  <Editor
                  ref="editorRef"
                  v-model="formData.real_content"
                  :init="{...tinymceInit,width:'auto'}"
                  class="tinymce-editor"
                />
              </el-form-item>
            </div>
          </el-tab-pane>
          <el-tab-pane label="品牌资料">
            <div class="tab-content">
              <!-- 品牌资料内容 -->
              <el-form-item label="品牌介绍">
                <!-- <el-input v-model="formData.brand_info" type="textarea" placeholder="请输入品牌资料" :rows="10" /> -->
                  <Editor
                  ref="editorRef"
                  v-model="formData.brand_content"
                  :init="{...tinymceInit,width:'auto'}"
                  class="tinymce-editor"
                />
              </el-form-item>
            </div>
          </el-tab-pane>
        </el-tabs>

        <!-- 底部操作区已移动到详细评测tab内 -->


        </el-form>
      </div>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import Editor from '@tinymce/tinymce-vue'
import { ref, reactive, onMounted } from 'vue'
import { getHouseSelectAllList } from '@/api/project/house'
import { ElMessage, ElMessageBox } from 'element-plus'
import Pagination from '@/components/Pagination/index.vue'
import { list, add, edit,  detailDelete, disable, getDeatilList, getProductList, getSpaceList, detailAdd, detailEdit, getFunList, getLightList, info } from '@/api/intelligent/intelligentevaluation'
import { getBrandOptions } from '@/api/project/decorationitem'
import { getPageLimit } from '@/utils/settings'
import { tinymceInit } from '@/views/info/components/info_tinymceInit.js'

// 常量定义
const idkey = 'id'
const name = '智能评估'

// 智能级别映射
const levelMap = {
  1: 'S级',
  2: 'A级',
  3: 'B级',
  4: 'C级',
  5: 'D级',
  6: 'E级'
}

// 发布状态映射
const releaseStatusMap = {
  1: '未发布',
  2: '已发布'
}

// 状态管理
const evaluationList = ref([])
const houseList = ref([])
const count = ref(0)
// 产品列表数据
const productList = ref([])
// 亮点列表数据
const highlightsData = ref([])
// 亮点表格引用
const highlightTableRef = ref(null)

const query = reactive({
  house_id: '',
  level: '',
  highlight_feature: '',
  included_product: '',
  release_status: 0,
  create_time: [],
  page: 1,
  limit: getPageLimit()
})
const autoCalculateScore = ref(true)
const isEditMode = ref(false)

const dialogVisible = ref(false)
const dialogTitle = ref('')
const formData = reactive({
  selectedHighlights: []
})
const formRef = ref(null)
const fileList = ref([])
const loading = ref(false)


// 生命周期
onMounted(() => {
  getList()
  getHouseList()
})

// 获取产品列表数据
function fetchProductList(intellect_id) {
  loading.value = true
  // 这里使用占位接口地址
  getDeatilList({
    intellect_id: intellect_id
  })
    .then(res => {
      productList.value = res.data || []
    })
    .catch(err => {
      ElMessage.error('获取产品列表失败')
      console.error(err)
    })
    .finally(() => {
      loading.value = false
    })
}

// 编辑产品
function handleProductEdit(row) {
  productDialogTitle.value = '编辑产品'
  console.log(row)
  // 根据表格数据结构正确赋值
  // 存储评测详细的业务id
  productFormData.id = row.id

  Object.assign(productFormData, {
    ipc_id: row.ipc_id ? Number(row.ipc_id) : '',
    space_ids: row.space_ids ? row.space_ids.map(id => Number(id)) : [],
    brand_id: row.brand_id ? Number(row.brand_id) : '',
    num: row.num || 1,
    fun_ids: row.fun_ids ? row.fun_ids.map(id => Number(id)) : []
  })
  productDialogVisible.value = true
  // 加载所有必要的下拉选项数据
  loadProductOptions()
  loadSpaceOptions()
  loadBrandOptions()
  // 编辑时根据选中的产品加载功能
  loadFunctionOptions(productFormData.ipc_id)
}

// 删除产品
function handleDelete(row) {
  ElMessageBox.confirm('确定要删除该产品吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 调用删除API
    detailDelete({ id: row.id })
      .then(() => {
        ElMessage.success('删除成功')
        // 刷新产品列表
        fetchProductList(formData.id)
      })
      .catch(err => {
        ElMessage.error('删除失败')
        console.error(err)
      })
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 处理发布状态变更
function handleStatusChange(row, value) {
  const newStatus = value ? 2 : 1;
  // 调用API更新状态
  disable({ id: row.id, status: newStatus })
    .then(() => {
      row.release_status = newStatus;
      ElMessage.success('发布状态更新成功');
    })
    .catch(err => {
      ElMessage.error('发布状态更新失败');
      console.error(err);
    });
}




// 获取楼盘列表
function getHouseList() {
  getHouseSelectAllList()
    .then(res => {
      houseList.value = res.data?.list || []
    })
    .catch(err => {
      ElMessage.error('获取楼盘列表失败')
      console.error(err)
    })
}

// 获取列表数据
function getList() {
  loading.value = true
  const params = {
    ...query,
    page: query.page,
    limit: query.limit
  }
  list(params)
    .then(res => {
      evaluationList.value = res.data.list
  
      count.value = res.data.count // 同步数据到count
    })
    .finally(() => {
      loading.value = false
    })
}

// 搜索
function search() {
  query.page = 1

  getList()
}

// 重置
function refresh() {
  Object.keys(query).forEach(key => {
    if (key === 'create_time') {
      query[key] = []
    } else if (key === 'release_status') {
      query[key] = 0
    } else {
      query[key] = ''
    }
  })
  query.page = 1

  getList()
}

// 打开添加对话框
function openAddDialog() {
  dialogTitle.value = '添加' + name
  isEditMode.value = false
  // 重置formData，确保清除id字段
  Object.keys(formData).forEach(key => {
    delete formData[key];
  });
  Object.assign(formData, {
      house_id: '',
      remark: '',
      count_score: 0,
      level: 1,
      cover_score: 0,
      fun_score: 0,
      release_status: 1,
      intellect_pics: [],
      displayImage: '',
      selectedHighlights: [],
      real_content: '',
      brand_content: ''
    })
  // 清空亮点数据
  highlightsData.value = []
  fileList.value = []
  dialogVisible.value = true
  // 调试：查看添加对话框中selectedHighlights的初始化
  console.log('添加对话框初始化后selectedHighlights:', formData.selectedHighlights);
}

// 处理亮点选择变化
function handleHighlightSelectionChange(selection) {
  console.log('亮点选择变化:', selection)
  // 将选中的行数据存储到 formData.selectedHighlights
  formData.selectedHighlights = selection
  console.log('更新后的selectedHighlights:', formData.selectedHighlights)
}

// 打开编辑对话框
function openEditDialog(row) {
  dialogTitle.value = `【${row.id}】智能评测编辑`
  isEditMode.value = true
  dialogVisible.value = true
  loading.value = true

  // 通过接口获取详细信息
    info({ id: row.id })
      .then(res => {
        const data = res.data || {}
        Object.assign(formData, data)

        // 确保数组和字段存在
        if (!formData.intellect_pics) {
          formData.intellect_pics = []
        } else {
          // 过滤intellect_pics，只保留file_url和file_name字段
          formData.intellect_pics = formData.intellect_pics.map(item => {
            return {
              file_url: item.file_url || ''
            }
          })
        }
        if (formData.displayImage === undefined) {
          formData.displayImage = ''
        }
        if (formData.selectedHighlights === undefined) {
          formData.selectedHighlights = []
        }

        // 弹框打开后加载产品列表数据
        fetchProductList(formData.id)
        // 加载亮点列表数据
        return getLightList({ intellect_id: row.id })
      })
      .then(res => {
        highlightsData.value = res.data || []

        // 处理亮点数据的回显
        // info接口返回的selectedHighlights可能是分组格式 {scene_type_raw: [id1, id2]}
        // 需要转换为表格选中需要的格式
        if (formData.selectedHighlights) {
          let selectedIds = []

          if (Array.isArray(formData.selectedHighlights)) {
            // 如果是数组格式，直接使用
            selectedIds = formData.selectedHighlights
          } else if (typeof formData.selectedHighlights === 'object') {
            // 如果是分组格式，提取所有ID
            selectedIds = Object.values(formData.selectedHighlights).flat()
          }

          console.log('需要回显的亮点IDs:', selectedIds)

          // 延迟执行，确保表格已渲染
          setTimeout(() => {
            if (highlightTableRef.value && selectedIds.length > 0) {
              // 清除所有选中状态
              highlightTableRef.value.clearSelection()

              // 根据ID恢复选中状态
              highlightsData.value.forEach(row => {
                if (selectedIds.includes(row.id)) {
                  highlightTableRef.value.toggleRowSelection(row, true)
                  console.log('恢复选中亮点:', row.name, row.id)
                }
              })

              // 更新formData.selectedHighlights为选中的完整行对象
              const selectedRows = highlightsData.value.filter(row => selectedIds.includes(row.id))
              formData.selectedHighlights = selectedRows
              console.log('回显后的selectedHighlights:', formData.selectedHighlights)
            }
          }, 100)
        }
      })
      .catch(err => {
        ElMessage.error('获取亮点列表失败')
        console.error(err)
      })
      .finally(() => {
        loading.value = false
      })
}



// 提交表单
function submitForm() {
  // 确保数字字段为数字类型
  formData.count_score = Number(formData.count_score);
  formData.cover_score = Number(formData.cover_score);
  formData.fun_score = Number(formData.fun_score);

  // 过滤intellect_pics，只保留file_url和file_name字段
  if (formData.intellect_pics && Array.isArray(formData.intellect_pics)) {
    formData.intellect_pics = formData.intellect_pics.map(item => {
      return {
        file_url: item.file_url || '',
        file_name: item.file_name || ''
      }
    })
  }



  // 将selectedHighlights按scene_type_raw分组
  if (formData.selectedHighlights && Array.isArray(formData.selectedHighlights)) {
    const groupedHighlights = {}

    formData.selectedHighlights.forEach(item => {
      if (typeof item === 'object' && item.scene_type_raw && item.id) {
        // 如果该scene_type_raw还没有数组，创建一个
        if (!groupedHighlights[item.scene_type_raw]) {
          groupedHighlights[item.scene_type_raw] = []
        }
        // 将id添加到对应的scene_type_raw数组中
        groupedHighlights[item.scene_type_raw].push(item.id)
      }
    })

    // 将分组后的数据赋值给selectedHighlights
    formData.selectedHighlights = groupedHighlights
    console.log('按scene_type_raw分组后的selectedHighlights:', formData.selectedHighlights)
  } else {
    // 如果不是数组，确保为空对象
    formData.selectedHighlights = {}
  }

  formRef.value.validate((valid) => {
    if (valid) {
      loading.value = true
      const action = formData[idkey] ? edit : add
      action(formData)
        .then(() => {
          ElMessage.success(formData[idkey] ? '修改成功' : '添加成功')
          dialogVisible.value = false
          getList()
        })
        .finally(() => {
          loading.value = false
        })
    }
  })
}

// 批量导入
function batchImport() {
  // 这里实现批量导入逻辑
  ElMessage.info('批量导入功能待实现')
}


// 上传相关功能已移除

// 删除单个
function deleteItem(row) {
  ElMessageBox.confirm(`确定删除该${name}吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      return del({ [idkey]: row[idkey] })
    })
    .then(() => {
      ElMessage.success('删除成功')
      getList()
    })
    .catch(() => {})
}


// 分页相关函数已移除
// 产品编辑对话框状态
const productDialogVisible = ref(false)
const productDialogTitle = ref('')
const productFormData = reactive({
  fun_ids_disabled: true,
    ipc_id: null,
    space_ids: [],
    brand_id: null,
    num: 1,
    fun_ids: []
  })

  // 表单验证规则
  const productFormRules = {
    ipc_id: [
      { required: true, message: '请选择产品', trigger: 'change' }
    ],
    space_ids: [
      { required: true, message: '请选择空间', trigger: 'change' },
      { type: 'array', min: 1, message: '至少选择一个空间', trigger: 'change' }
    ],
    brand_id: [
      { required: true, message: '请选择品牌', trigger: 'change' }
    ],
    num: [
      { required: true, message: '请输入数量', trigger: 'blur' },
      { type: 'number', min: 1, message: '数量必须大于0', trigger: 'blur' }
    ],
    fun_ids: [
      { required: true, message: '请选择功能', trigger: 'change' },
      { type: 'array', min: 1, message: '至少选择一个功能', trigger: 'change' }
    ]
  }

  const productFormRef = ref(null)
const isSubmitting = ref(false)

// 产品选项数据
const productOptions = ref([])

// 空间选项数据
const spaceOptions = ref([])

// 获取空间列表数据
function loadSpaceOptions() {
  getSpaceList({})
    .then(res => {
      // 假设接口返回格式为 { data: [{ id: 1, name: '空间名称' }] }
      if (res.data && Array.isArray(res.data)) {
        spaceOptions.value = res.data
      }
    })
    .catch(err => {
      ElMessage.error('获取空间选项失败')
      console.error(err)
    })
}

// 获取产品列表数据
function loadProductOptions() {
  getProductList({})
    .then(res => {
      // 假设接口返回格式为 { data: [{ id: 1, name: '产品名称' }] }
      if (res.data && Array.isArray(res.data)) {
        productOptions.value = res.data
      }
    })
    .catch(err => {
      ElMessage.error('获取产品选项失败')
      console.error(err)
    })
}



// 品牌选项数据
const brandOptions = ref([])

// 功能选项数据
const functionOptions = ref([])

// 打开添加产品对话框
function openAddProductDialog() {
  productDialogTitle.value = '添加产品'
  // 重置productFormData，包括id
  Object.assign(productFormData, {
    id: null,
    fun_ids_disabled: true,
    ipc_id: null,
    space_ids: [],
    brand_id: null,
    num: 1,
    fun_ids: []
  })
  productDialogVisible.value = true
  // 加载所有必要的下拉选项数据
  loadProductOptions()
  loadSpaceOptions()
  loadBrandOptions()
  // 打开对话框时不自动加载功能，等待用户选择产品后再加载
  // loadFunctionOptions()
}

  // 加载品牌选项数据
function loadBrandOptions() {
  getBrandOptions()
    .then(res => {
      if (res.code === 200 && Array.isArray(res.data)) {
        // 只筛选release_status为2的品牌
        // 为每个品牌添加disabled状态，release_status为2时可选中
        brandOptions.value = res.data.map(item => ({
          id: item.id,
          union_brand: item.union_brand,
          disabled: item.release_status !== 2
        }))
      } else {
        ElMessage.error('获取品牌选项失败')
      }
    })
    .catch(err => {
      ElMessage.error('获取品牌选项失败')
      console.error(err)
    })
}

  // 处理产品选择变化
function handleProductChange(product_id) {
  // 清空之前的功能选择
  productFormData.fun_ids = []
  // 加载对应产品的功能选项
  loadFunctionOptions(product_id)
}

  // 加载功能选项数据
function loadFunctionOptions(product_id) {
  // 禁用功能选择框，直到数据加载完成
  productFormData.fun_ids_disabled = true
  // 清空功能选项
  functionOptions.value = []
  // 只有当product_id存在时才调用API
  if (product_id) {
    // 调用API获取功能数据
    getFunList({ product_id: product_id })
      .then(res => {
        if (res.code === 200) {
          // 确保数据格式正确
          functionOptions.value = res.data.map(item => ({
            id: item.id,
            name: item.name
          })) || []
        } else {
          ElMessage.error('获取功能数据失败')
        }
      })
      .catch(error => {
        ElMessage.error('获取功能数据失败')
      })
      .finally(() => {
        // 启用功能选择框
        productFormData.fun_ids_disabled = false
      })
  } else {
    // 如果没有product_id，直接启用功能选择框
    productFormData.fun_ids_disabled = false
  }
}

// 打开编辑产品对话框
function handleEdit(row) {
  productDialogTitle.value = '编辑产品'
  // 假设row中包含对应的字段
  Object.assign(productFormData, {
    ipc_id: row.product_id ? Number(row.product_id) : '',
    space_ids: row.spaces ? row.spaces.map(id => Number(id)) : [],
    brand_id: row.brand_id ? Number(row.brand_id) : '',
    num: row.quantity || 1,
    fun_ids: row.functions ? row.functions.map(id => Number(id)) : []
  })
  productDialogVisible.value = true
  // 加载所有必要的下拉选项数据
  loadProductOptions()
  loadSpaceOptions()
  loadBrandOptions()
  // 编辑时根据选中的产品加载功能
  loadFunctionOptions(productFormData.ipc_id)
}

// 保存产品信息
function saveProduct() {
  // 检查是否所有必需的下拉选项都已加载
  if (productOptions.length === 0 || spaceOptions.length === 0 || brandOptions.length === 0 || functionOptions.length === 0) {
    ElMessage.warning('请等待下拉数据加载完成后再提交');
    return;
  }

  productFormRef.value.validate((valid) => {
    if (valid) {
      isSubmitting.value = true;
      // 准备提交数据，映射到表结构字段并转换类型
        const submitData = {
          intellect_id: formData.id, // 从智能评测表单数据中获取intellect_id
          ipc_id: Number(productFormData.ipc_id), // 产品id对应品类id，转换为数字
          space_ids: productFormData.space_ids.map(id => Number(id)), // 空间数组转换为数字数组
          brand_id: Number(productFormData.brand_id), // 品牌id转换为数字
          num: productFormData.num, // 数量
          fun_ids: productFormData.fun_ids.map(id => Number(id)) // 功能数组转换为数字数组
        }

      // 根据是否有id决定调用添加还是编辑接口
      const isEdit = productFormData.id !== undefined && productFormData.id !== null

      // 编辑时添加业务id
      if (isEdit) {
        submitData.id = productFormData.id
      }

      const savePromise = isEdit ? detailEdit(submitData) : detailAdd(submitData)

      savePromise
        .then(() => {
          ElMessage.success('保存成功')
          productDialogVisible.value = false
          // 刷新产品列表
          fetchProductList(formData.id)
        })
        .catch(err => {
            ElMessage.error('保存失败')
            console.error(err)
          })
          .finally(() => {
            isSubmitting.value = false;
          })
    }
  })
}

</script>

<style lang="scss" scoped>
/* 产品编辑对话框样式 */
.el-form-item.is-required .el-form-item__label:before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

.el-form-item__label {
  font-weight: normal;
}
 .tinymce-editor {
    z-index: auto;

    .tox-editor-container {
      z-index: auto;
    }
  }
.el-dialog__footer {
  display: flex;
  justify-content: flex-end;
  padding: 15px;
  border-top: 1px solid #eee;
}

.el-button--primary {
  background-color: #e63228;
  border-color: #e63228;
}

.el-button--primary:hover,
.el-button--primary:focus {
  background-color: #c41d14;
  border-color: #c41d14;
}

.app-container {
  padding: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 对话框样式 */
.dialog-content {
  padding: 20px;
}

.info-container {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
}

.basic-info,
.score-info {
  flex: 1;
}

.basic-info h3,
.score-info h3,
.image-gallery h3 {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.image-gallery {
  margin-bottom: 20px;
}

.image-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 10px;
}

.image-item {
  width: 120px;
  height: 120px;
  position: relative;
  border: 1px solid #eee;
  border-radius: 4px;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.5);
  padding: 5px;
  text-align: center;
}

.image-actions .el-button {
  color: white;
  background: transparent;
  padding: 4px 8px;
  font-size: 12px;
}

.add-image {
  width: 120px;
  height: 120px;
  border: 1px dashed #dcdcdc;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}

.add-image:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

/* 底部操作区样式 */
.bottom-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 15px;
}

.action-buttons {
  margin-left: 10px;
}

.add-icon {
  font-size: 24px;
  color: #8c8c8c;
  margin-bottom: 5px;
}

.add-text {
  font-size: 12px;
  color: #8c8c8c;
}

.file-tip {
  font-size: 12px;
  color: #909399;
}

.detail-tabs {
  margin-top: 20px;
}

.tab-content {
  padding: 15px;
  background-color: #fafafa;
  border: 1px solid #eee;
  border-top: none;
  min-height: 300px;
}

.bottom-actions {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

/* 表格样式调整 */
.el-table .el-table__cell {
  padding: 12px 0;
}

/* 按钮样式 */
.el-button--primary {
  background-color: #e63228;
  border-color: #e63228;
}

.el-button--primary:hover,
.el-button--primary:focus {
  background-color: #c41d14;
  border-color: #c41d14;
}

/* 开关样式 */
.el-switch__core {
  width: 44px;
  height: 22px;
}

.el-switch__core .el-switch__button {
  width: 20px;
  height: 20px;
}

.el-switch.is-checked .el-switch__core {
  border-color: #13ce66;
  background-color: #13ce66;
}

  .related-products {
    padding: 5px 0;
  }
  .product-blocks {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;
  }
  .product-block {
    padding: 4px 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
  }
</style>