import request from '@/utils/request'

const url = '/admin/furnish.DecorationItem/'

export function getAllList(params) {
  return request({
    url: url + 'getAllList',
    method: 'get',
    params
  })
}

export function create(data) {
  return request({
    url: url + 'create',
    method: 'post',
    data
  })
}

export function edit(data) {
  return request({
    url: url + 'edit',
    method: 'post',
    data
  })
}

export function remove(data) {
  return request({
    url: url + 'delete',
    method: 'post',
    data
  })
}

export function info(params) {
  return request({
    url: url + 'info',
    method: 'get',
    params
  })
}

export function getClassOptions() {
  return request({
    url: url + 'getClassOptions',
    method: 'get'
  })
}

export function getPositionOptions() {
  return request({
    url: url + 'getPositionOptions',
    method: 'get'
  })
}

export function getCategoryOptions(params) {
  return request({
    url: url + 'getCategoryOptions',
    method: 'get',
    params
  })
}

export function getMaterialOptions() {
  return request({
    url: url + 'getMaterialOptions',
    method: 'get'
  })
}

export function getBrandOptions() {
  return request({
    url: url + 'getBrandOptions',
    method: 'get'
  })
}



  /**
 * 数据导入
 * @param {array} data 请求数据
 */
  export function itemexport(data) {
    return request({
      url: url + 'importItem',
      method: 'post',
      data
    })
  }