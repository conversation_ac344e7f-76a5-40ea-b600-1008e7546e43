// 智能评估管理
import request from '@/utils/request'

const url = '/admin/intelligent.IntelligentEvaluation/'
/**
 * 智能评估列表
 * @param {array} params 请求参数
 */
export function list(params) {
  return request({
    url: url + 'list',
    method: 'get',
    params: params
  })
}

/**
 * 智能评估-亮点列表
 * @param {array} params 请求参数
 */
export function getLightList(params) {
  return request({
    url: url + 'getLightList',
    method: 'get',
    params: params
  })
}
/**
 * 智能评估下的详细评测列表数据
 * @param {array} params 请求参数
 */
export function getDeatilList(params) {
  return request({
    url: url + 'getDeatilList',
    method: 'get',
    params: params
  })
}
/**
 * 智能评估下的详细评测编辑和添加的产品功能下拉
 * @param {array} params 请求参数
 */
export function getFunList(params) {
  return request({
    url: url + 'getFunList',
    method: 'get',
    params: params
  })
}
/**
 * 智能评估的详细信息的产品下拉列表
 * @param {array} params 请求参数
 */
export function getProductList(params) {
  return request({
    url: url + 'getProductList',
    method: 'get',
    params: params
  })
}
/**
 * 智能评估的详细信息的空间下拉列表
 * @param {array} params 请求参数
 */
export function getSpaceList(params) {
  return request({
    url: url + 'getSpaceList',
    method: 'get',
    params: params
  })
}

/**
 * 智能评估添加
 * @param {array} data 请求数据
 */
export function add(data) {
  return request({
    url: url + 'add',
    method: 'post',
    data
  })
}

/**
 * 智能评估的详细信息添加
 * @param {array} data 请求数据
 */
export function detailAdd(data) {
  return request({
    url: url + 'detailAdd',
    method: 'post',
    data
  })
}
/**
 * 智能评估的详细信息编辑
 * @param {array} data 请求数据
 */
export function detailEdit(data) {
  return request({
    url: url + 'detailEdit',
    method: 'post',
    data
  })
}
/**
 * 智能评估修改
 * @param {array} data 请求数据
 */
export function edit(data) {
  return request({
    url: url + 'edit',
    method: 'post',
    data
  })
}

/**
 * 只能评测详细评测删除
 * @param {array} data 请求数据
 */
export function detailDelete(data) {
  return request({
    url: url + 'detailDelete',
    method: 'post',
    data
  })
}

/**
 * 智能评估禁用
 * @param {array} data 请求数据
 */
export function disable(data) {
  return request({
    url: url + 'disable',
    method: 'post',
    data
  })
}