# 智能级别下拉框显示修复说明

## 问题描述

新建时，智能级别的下拉表单显示的是数字"1"，应该显示"1"对应的级别名称"S级"，而不是数字1。

## 问题原因

### 1. Object.entries() 类型转换问题
```javascript
// levelMap 的键是数字类型
const levelMap = {
  1: 'S级',    // 键是数字 1
  2: 'A级',
  3: 'B级',
  4: 'C级',
  5: 'D级',
}

// Object.entries() 会将键转换为字符串
Object.entries(levelMap) // [['1', 'S级'], ['2', 'A级'], ...]

// formData.level 是数字类型
formData.level = 1  // 数字 1

// 导致匹配失败：数字 1 !== 字符串 '1'
```

### 2. Element Plus 选择器匹配机制
Element Plus 的 `el-select` 使用严格相等 (`===`) 来匹配 `v-model` 的值和 `el-option` 的 `value`。当类型不匹配时，无法正确显示对应的标签。

## 修复方案

### 1. 保持数据类型为数字，转换选项值

**正确的做法：**
```javascript
// levelMap 保持数字键（符合业务逻辑）
const levelMap = {
  1: 'S级',
  2: 'A级',
  3: 'B级',
  4: 'C级',
  5: 'D级',
}

// formData.level 保持数字类型
formData.level = 1
```

**关键修复：在模板中转换选项值**
```vue
<!-- 使用 Number(value) 将字符串转换回数字 -->
<el-option
  v-for="([value, label], index) in Object.entries(levelMap)"
  :key="index"
  :value="Number(value)"
  :label="label"
>
  {{ label }}
</el-option>
```

## 数据流程分析

### 修复前的问题流程
```
1. levelMap = {1: 'S级', 2: 'A级', ...}
2. Object.entries(levelMap) = [['1', 'S级'], ['2', 'A级'], ...]
3. el-option value = '1' (字符串)
4. formData.level = 1 (数字)
5. '1' !== 1 → 匹配失败 → 显示原始值 "1"
```

### 修复后的正确流程
```
1. levelMap = {1: 'S级', 2: 'A级', ...}
2. Object.entries(levelMap) = [['1', 'S级'], ['2', 'A级'], ...]
3. Number('1') = 1 → el-option value = 1 (数字)
4. formData.level = 1 (数字)
5. 1 === 1 → 匹配成功 → 显示标签 "S级"
```

## 相关代码修改

### 1. levelMap 定义（保持数字键）
```javascript
// 文件位置：第383-391行
const levelMap = {
  1: 'S级',
  2: 'A级',
  3: 'B级',
  4: 'C级',
  5: 'D级',
}
```

### 2. 新建对话框初始化（保持数字值）
```javascript
// 文件位置：第585-598行
Object.assign(formData, {
  house_id: '',
  remark: '',
  count_score: 0,
  level: 1,          // 保持数字类型
  cover_score: 0,
  fun_score: 0,
  release_status: 1,
  intellect_pics: [],
  displayImage: '',
  selectedHighlights: [],
  real_content: '',
  brand_content: ''
})
```

### 3. 下拉框模板（关键修复）
```vue
<!-- 文件位置：第189-195行 -->
<el-form-item label="智能级别" prop="level">
  <el-select v-model="formData.level" placeholder="请选择智能级别">
    <el-option
      v-for="([value, label], index) in Object.entries(levelMap)"
      :key="index"
      :value="Number(value)"  <!-- 关键：将字符串转换为数字 -->
      :label="label"
    >
      {{ label }}
    </el-option>
  </el-select>
</el-form-item>
```

### 4. 查询表单下拉框
```vue
<!-- 文件位置：第12-16行 -->
<el-form-item label="智能级别">
  <el-select v-model="query.level" placeholder="请选择" :style="{width: '180px'}">
    <el-option
      v-for="([value, label], index) in Object.entries(levelMap)"
      :key="index"
      :value="Number(value)"  <!-- 同样转换为数字 -->
      :label="label"
    />
  </el-select>
</el-form-item>
```

## 测试验证

### 1. 新建对话框测试
1. 点击"添加"按钮打开新建对话框
2. 查看"智能级别"下拉框
3. 确认显示"S级"而不是"1"
4. 点击下拉箭头，确认选项正确显示

### 2. 编辑对话框测试
1. 点击"编辑"按钮打开编辑对话框
2. 确认智能级别正确回显对应的级别名称
3. 修改级别选择，确认显示正确

### 3. 数据提交测试
1. 选择不同的智能级别
2. 提交表单
3. 确认提交的数据格式正确

## 其他相关组件

确保其他使用 `levelMap` 的地方也保持一致：

### 1. 查询表单 (第14行)
```vue
<el-select v-model="query.level" placeholder="请选择" :style="{width: '180px'}">
  <el-option v-for="([value, label], index) in Object.entries(levelMap)" :key="index" :value="value" :label="label" />
</el-select>
```

### 2. 表格显示 (第82行)
```vue
<el-table-column prop="level" label="智能级别" width="80">
  <template #default="{ row }">
    {{ levelMap[row.level] || '-' }}
  </template>
</el-table-column>
```

## 最佳实践

### 1. 数据类型一致性
```javascript
// 推荐：统一使用字符串作为选项值
const options = {
  '1': '选项1',
  '2': '选项2'
}

// 避免：混合使用数字和字符串
const options = {
  1: '选项1',      // 数字键
  '2': '选项2'     // 字符串键
}
```

### 2. 表单初始化
```javascript
// 确保初始值类型与选项值类型一致
formData.level = '1'  // 字符串，匹配 levelMap 的键
```

### 3. 后端数据处理
```javascript
// 如果后端期望数字类型，在提交时转换
const submitData = {
  ...formData,
  level: Number(formData.level)  // 转换为数字
}
```

## 注意事项

1. **类型一致性**：确保 `v-model` 值和 `el-option` 的 `value` 类型完全一致
2. **Object.entries() 行为**：会将对象的键转换为字符串
3. **严格相等比较**：Element Plus 使用 `===` 进行匹配
4. **后端兼容性**：如果后端期望数字类型，需要在提交时进行类型转换

现在新建时智能级别下拉框应该正确显示"S级"而不是数字"1"了！
