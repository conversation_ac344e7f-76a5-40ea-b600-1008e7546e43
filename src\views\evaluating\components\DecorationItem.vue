<template>
  <div class="decoration-item-container">
    <!-- 搜索区域 -->
    <div class="app-container">
      <el-row>
        <el-col>
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item label="室内分类">
              <el-input 
                v-model="searchForm.category" 
                placeholder="请输入室内分类" 
                clearable 
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="入户门">
              <el-input 
                v-model="searchForm.entrance_door" 
                placeholder="请输入入户门" 
                clearable 
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="不含项目">
              <el-input 
                v-model="searchForm.excluded_items" 
                placeholder="请输入不含项目" 
                clearable 
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">
                <svg-icon icon-class="refresh" />
                重置
              </el-button>
              <el-button type="primary" @click="handleAdd">新增</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>

    <!-- 列表 -->
    <el-table
      ref="table"
      v-loading="loading"
      :data="data"
      :height="height"
      @sort-change="sort"
      @selection-change="select"
    >
      <el-table-column type="selection" width="42" title="全选/反选" />
      <el-table-column :prop="idkey" label="序号" width="80" sortable="custom" />
      <el-table-column prop="category" label="分类" min-width="120" show-overflow-tooltip />
      <el-table-column prop="entrance_door" label="位置" min-width="100" show-overflow-tooltip />
      <el-table-column prop="excluded_items" label="不含项目" min-width="150" show-overflow-tooltip />
      <el-table-column prop="todo_items" label="TODO项目" min-width="120" show-overflow-tooltip />
      <el-table-column prop="unit_price" label="小计单价" min-width="100" show-overflow-tooltip />
      <el-table-column prop="total_amount" label="总金额" min-width="100" show-overflow-tooltip />
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template #default="scope">
          <el-text v-if="scope.row.status == 1" type="success">启用</el-text>
          <el-text v-else-if="scope.row.status == 0" type="info">禁用</el-text>
          <el-text v-else>-</el-text>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="scope">
          <el-link 
            type="primary" 
            class="mr-1" 
            :underline="false" 
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-link>
          <el-link 
            type="danger" 
            :underline="false" 
            @click="handleDelete(scope.row)"
          >
            删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>

  
    <!-- 编辑弹窗 -->
    <el-dialog
      v-model="dialog"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="cancel"
      width="600px"
      top="5vh"
    >
      <el-form
        ref="formRef"
        :model="model"
        :rules="rules"
        label-width="100px"
        label-position="right"
      >
        <el-tabs>
          <el-tab-pane label="基本信息">
            <el-scrollbar native :height="400">
              <el-form-item label="大类" prop="category">
                <el-select 
                  v-model="model.category" 
                  placeholder="请选择大类" 
                  style="width: 100%"
                >
                  <el-option label="室内分类1" value="室内分类1" />
                  <el-option label="室内分类2" value="室内分类2" />
                  <el-option label="室内分类3" value="室内分类3" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="位置" prop="entrance_door">
                <el-select 
                  v-model="model.entrance_door" 
                  placeholder="请选择位置" 
                  style="width: 100%"
                >
                  <el-option label="入户门" value="入户门" />
                  <el-option label="客厅" value="客厅" />
                  <el-option label="卧室" value="卧室" />
                  <el-option label="厨房" value="厨房" />
                  <el-option label="卫生间" value="卫生间" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="类型" prop="type">
                <el-select 
                  v-model="model.type" 
                  placeholder="请选择类型" 
                  style="width: 100%"
                >
                  <el-option label="不含项目1" value="不含项目1" />
                  <el-option label="不含项目2" value="不含项目2" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="品牌" prop="brand">
                <el-checkbox-group v-model="model.brand">
                  <el-checkbox label="品牌全部" />
                  <el-checkbox label="TODO项目" />
                  <el-checkbox label="小计单价" />
                </el-checkbox-group>
              </el-form-item>

              <el-form-item label="不含项目" prop="excluded_items">
                <el-input 
                  v-model="model.excluded_items" 
                  placeholder="请输入不含项目" 
                  type="textarea" 
                  :rows="3"
                />
              </el-form-item>

              <el-form-item label="TODO项目" prop="todo_items">
                <el-input 
                  v-model="model.todo_items" 
                  placeholder="请输入TODO项目" 
                  type="textarea" 
                  :rows="3"
                />
              </el-form-item>

            

             

              <el-form-item label="状态" prop="status">
                <el-radio-group v-model="model.status">
                  <el-radio :label="1">启用</el-radio>
                  <el-radio :label="0">禁用</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-scrollbar>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      
      <template #footer>
        <el-button :loading="submitLoading" @click="cancel">取消</el-button>
        <el-button :loading="submitLoading" type="primary" @click="submit">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Pagination from '@/components/Pagination/index.vue'
import { getPageLimit } from '@/utils/settings'
import * as DecorationItemApi from '@/api/project/decorationitem'
import screenHeight from '@/utils/screen-height'

// Props
const props = defineProps({
  id: {
    type: [String, Number],
    required: true
  }
})

// 响应式数据
const table = ref()
const formRef = ref()
const loading = ref(false)
const dialog = ref(false)
const submitLoading = ref(false)
const data = ref([])
const count = ref(0)
const height = ref(560)
const idkey = ref('id')
const selection = ref([])

// 搜索表单
const searchForm = reactive({
  category: '',
  entrance_door: '',
  excluded_items: ''
})

// 查询参数
const query = ref({
  page: 1,
  limit: getPageLimit(),
  decoration_id: props.id
})

// 表单数据
const model = ref({})

// 弹窗标题
const dialogTitle = computed(() => {
  return model.value.id ? '编辑装修条目' : '新增装修条目'
})

// 表单验证规则
const rules = {
  category: [
    { required: true, message: '请选择大类', trigger: 'change' }
  ],
  entrance_door: [
    { required: true, message: '请选择位置', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'change' }
  ]
}

// 监听props.id变化
watch(() => props.id, (newId) => {
  if (newId) {
    query.value.decoration_id = newId
    // getDecorationItemList()
  }
}, { immediate: true })

// 方法
const getDecorationItemList = async () => {
  if (!props.id) {
    ElMessage.warning('缺少必要参数ID')
    return
  }

  loading.value = true
  try {
    const params = {
      ...query.value,
      ...searchForm
    }

    const data = await DecorationItemApi.getAllList(params)

    data.value = result.list || []
    count.value = result.total || 0
  } catch (error) {
    ElMessage.error('获取数据失败')
    console.error(error)
    // 模拟数据作为降级处理
    data.value = [
      {
        id: 1,
        category: '室内分类',
        entrance_door: '入户门',
        excluded_items: '不含项目',
        todo_items: 'TODO项目',
        unit_price: '小计单价',
        total_amount: '总金额',
        status: 1
      }
    ]
    count.value = 1
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  query.value.page = 1
  getDecorationItemList()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  query.value.page = 1
  getDecorationItemList()
}

const handleAdd = () => {
  reset()
  dialog.value = true
}

const handleEdit = async (row) => {
  try {
    const { data: result } = await DecorationItemApi.info({ id: row.id })
    reset(result)
    dialog.value = true
  } catch (error) {
    ElMessage.error('获取详情失败')
    console.error(error)
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await DecorationItemApi.remove({ id: row.id })

    ElMessage.success('删除成功')
    getDecorationItemList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error(error)
    }
  }
}

const submit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true

    const submitData = {
      ...model.value,
      decoration_id: props.id
    }

    if (model.value.id) {
      await DecorationItemApi.edit(submitData)
    } else {
      await DecorationItemApi.add(submitData)
    }

    ElMessage.success('保存成功')
    dialog.value = false
    getDecorationItemList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('保存失败')
      console.error(error)
    }
  } finally {
    submitLoading.value = false
  }
}

const cancel = () => {
  dialog.value = false
  reset()
}

// 重置表单
const reset = (row) => {
  if (row) {
    model.value = { ...row }
  } else {
    model.value = {
      id: null,
      category: '',
      entrance_door: '',
      type: '',
      brand: [],
      excluded_items: '',
      todo_items: '',
      unit_price: '',
      total_amount: '',
      status: 1
    }
  }
  formRef.value?.resetFields()
}

// 排序
const sort = (sortInfo) => {
  query.value.sort_field = sortInfo.prop
  query.value.sort_value = ''
  if (sortInfo.order === 'ascending') {
    query.value.sort_value = 'asc'
  }
  if (sortInfo.order === 'descending') {
    query.value.sort_value = 'desc'
  }
  getDecorationItemList()
}

// 选择
const select = (selectedRows) => {
  selection.value = selectedRows
}

// 生命周期
onMounted(() => {
  height.value = screenHeight(310)
  if (props.id) {
    getDecorationItemList()
  }
})

// 暴露方法给父组件
defineExpose({
  refresh: getDecorationItemList
})
</script>

<style lang="scss" scoped>
.decoration-item-container {
  .app-container {
    margin-bottom: 20px;

    .search-form {
      :deep(.el-form-item) {
        margin-bottom: 10px;
      }
    }
  }
}
</style>
