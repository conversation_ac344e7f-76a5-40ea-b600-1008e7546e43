<template>
  <div class="decoration-item-container">
    <div class="header">
      <el-button type="primary" @click="handleCreate">添加</el-button>
    </div>
    
    <el-dialog 
      v-model="dialogVisible"
      :title="formType === 'create' ? '添加装饰项' : '编辑装饰项'"
      width="50%"
    >
      <el-form :model="currentItem" label-width="100px">
        <el-form-item label="大类">
          <el-select v-model="currentItem.class_id" placeholder="请选择大类" clearable>
            <el-option
              v-for="item in classOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="位置">
          <el-select v-model="currentItem.position_id" placeholder="请选择位置" clearable @change="handlePositionChange">
            <el-option
              v-for="item in positionOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="currentItem.category_id" placeholder="请选择类型" clearable>
            <el-option
              v-for="item in categoryOptions"
              :key="item.category_id"
              :label="item.category_name"
              :value="item.category_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="材质">
          <el-select v-model="currentItem.material_id" multiple placeholder="请选择材质" clearable :multiple-limit="4">
            <el-option
              v-for="item in materialOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="品牌">
          <el-radio-group v-model="currentItem.brand_type">
            <el-radio :label="1">具体品牌</el-radio>
            <el-radio :label="2">品牌定制</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item v-if="currentItem.brand_type === 1" label=" ">
          <el-select 
            v-model="currentItem.brand_list" 
            multiple
            placeholder="请选择品牌"
            style="width: 100%"
            class="brand-select"
            filterable
            :multiple-limit="4"
          >
            <el-option
              v-for="item in brandOptions"
              :key="item.id"
              :label="item.union_name"
              :value="item.id"
              :disabled="item.release_status !== 2"
            />

            <template #tag>
              
              <el-tag v-for="item in currentItem.brand_list" :key="item.id" type="primary"> {{ getUnionBrand(item) }}</el-tag>
               <!-- <span v-for="(item,index) in currentItem.brand_list" :key="index">
                {{item.union_brand}}
               </span> -->
            </template>
          </el-select>
        </el-form-item>
        
        <el-form-item v-if="currentItem.brand_type === 1" label=" ">
          <el-checkbox 
            v-model="currentItem.is_or_brand"
            :true-label="1"
            :false-label="2"
            class="same-brand-checkbox"
          >
            或同档品牌
          </el-checkbox>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="isSubmitting">确定</el-button>
      </template>
    </el-dialog>
    
    <el-table v-loading="loading" :data="tableData" style="width: 100%">
      <el-table-column prop="id" label="序号" width="80" />
      <el-table-column prop="class_name" label="大类" />
      <el-table-column prop="position_name" label="位置" />
      <el-table-column prop="category_name" label="类型" />
      <el-table-column prop="material_name" label="材质" />
      <el-table-column prop="brand_name_list" label="品牌">
        <template #default="{row}">
          <span v-if="row.brand_type === 2">品牌定制</span>
          <span v-else>
            {{ row.brand_name_list?.join(', ') }}
            <span v-if="row.is_or_brand === 1"> 或同等品牌</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180">
        <template #default="scope">
          <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDeleteConfirm(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { 
  getAllList,
  create as createDecorationItem,
  edit as updateDecorationItem,
  remove as deleteDecorationItem,
  getClassOptions,
  getPositionOptions,
  getCategoryOptions,
  getMaterialOptions,
  getBrandOptions
} from '@/api/project/decorationitem'

const props = defineProps({
  decorationId: {
    type: [String, Number],
    required: true
  }
})

const tableData = ref([])
const loading = ref(false)
const dialogVisible = ref(false)
const formType = ref('create')
const isSubmitting = ref(false)
const currentItem = ref({
  brand_type: 1,
  brand_list: [],
  same_level_brand: 2
})
const classOptions = ref([])
const positionOptions = ref([])
const categoryOptions = ref([])
const materialOptions = ref([])
const brandOptions = ref([])

const handleCreate = async () => {
  formType.value = 'create'
  currentItem.value = {}
  dialogVisible.value = true
  
}

const getUnionBrand = (id)=>{
  let brand = brandOptions.value.find(item=>item.id === id)
  return brand ? brand.union_brand : '';
}

const handleEdit = async (row) => {
  formType.value = 'edit'
  currentItem.value = { ...row }
  dialogVisible.value = true
  
  // 先加载选项
  await loadOptions()
  
  // 如果已有位置和类型，确保类型选项已加载并设置当前值
  if (currentItem.value.position_id && currentItem.value.category_id) {
    await handlePositionChange(currentItem.value.position_id)
    // 确保类型选项加载完成后再设置当前值
    await nextTick()
    // 强制重新设置当前选中的类型值
    currentItem.value.category_id = row.category_id
  }
}
const handleDeleteConfirm = (row) => {
      ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        handleDelete(row);
      }).catch(() => {});
    };

const handleDelete = async (row) => {
  try {
    await deleteDecorationItem({ 
      id: row.id, 
      decoration_id: props.decorationId 
    });
    ElMessage.success('删除成功');
    await getDecorationItemList();
  } catch (error) {
    console.error('删除失败', error);
    ElMessage.error('删除失败');
  }
};

const handlePositionChange = async (positionId) => {
  try {
    if (positionId) {
      const categoryRes = await getCategoryOptions({ 
        position_id: positionId,
        decoration_id: props.decorationId 
      })
      categoryOptions.value = categoryRes.data
    } else {
      categoryOptions.value = []
    }
    
    // 重置当前选择的类型
    currentItem.value.category_id = null
  } catch (error) {
    console.error('加载类型选项失败', error)
  }
}

const getDecorationItemList = async () => {
  try {
    loading.value = true
    const { data } = await getAllList({ decoration_id: props.decorationId })
    tableData.value = data
  } catch (error) {
    console.error('获取数据失败', error)
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  if (isSubmitting.value) return
  isSubmitting.value = true
  
  try {
    const params = {
      ...currentItem.value,
      decoration_id: props.decorationId
    }
    
    // 如果是品牌定制，不提交品牌相关数据
    if (params.brand_type === 2) {
      delete params.brand_list
      delete params.same_level_brand
    } else {
      // 过滤掉不在品牌选项中的品牌ID
      if (params.brand_list) {
        const validBrandIds = brandOptions.value.map(brand => brand.id)
        params.brand_list = params.brand_list.filter(id => validBrandIds.includes(id))
      }
    }
    
    if (formType.value === 'create') {
      await createDecorationItem(params)
    } else {
      await updateDecorationItem(params)
    }
    
    dialogVisible.value = false
    await getDecorationItemList()
  } catch (error) {
    console.error('保存失败', error)
  } finally {
    isSubmitting.value = false
  }
}

const loadOptions = async () => {
  try {
    const [classRes, positionRes, materialRes, brandRes] = await Promise.all([
      getClassOptions(),
      getPositionOptions(),
      getMaterialOptions(),
      getBrandOptions()
    ])
    
    classOptions.value = classRes.data
    positionOptions.value = positionRes.data
    brandOptions.value = brandRes.data
    materialOptions.value = materialRes.data
    
    // 清空类型选项
    categoryOptions.value = []
  } catch (error) {
    console.error('加载选项失败', error)
  }
}

onMounted(async () => {
  await getDecorationItemList()
  await loadOptions();
})
</script>

<style scoped>
.decoration-item-container {
  padding: 20px;
}
.header {
  margin-bottom: 20px;
}

.el-form-item {
  margin-bottom: 22px;
}
</style>