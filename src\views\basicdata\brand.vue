<template>
  <div class="app-container">
    <div class="bg-[#ffffff] p-[10px] border-rd-10px mb-[10px]">
      <el-form ref="queryRef" :model="query" :inline="true" label-width="90px">
        <el-form-item label="品牌名称：" prop="name">
          <el-input
            v-model="query.name"
            maxlength="20"
            placeholder="请输入名称"
            style="width: 140px"
          />
        </el-form-item>
        <el-form-item label="禁用状态：" prop="is_disable">
          <el-select v-model="query.is_disable" style="width: 120px">
            <el-option value="-1" label="不限" />
            <el-option value="0" label="未禁用" />
            <el-option value="1" label="已禁用" />
          </el-select>
        </el-form-item>
        <!-- 时间 -->
        <el-form-item>
          <el-select v-model="query.date_field" class="ya-date-field" placeholder="时间类型">
            <el-option value="" label="请选择" />
            <el-option value="create_time" label="添加时间" />
            <el-option value="update_time" label="修改时间" />
          </el-select>
          <el-date-picker
            v-model="query.date_value"
            type="datetimerange"
            class="ya-date-value"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD HH:mm:ss"
            :default-time="[new Date(2024, 1, 1, 0, 0, 0), new Date(2024, 1, 1, 23, 59, 59)]"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" title="搜索" @click="handleQuery">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button type="primary" title="新增" @click="add">新增</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 搜索end -->
    <el-table
      ref="table"
      v-loading="loading"
      :data="data"
      :height="height"
      @sort-change="sort"
      @selection-change="select"
    >
      <el-table-column type="selection" width="42" title="全选/反选" />
      <el-table-column :prop="idkey" label="ID" width="80" sortable="custom" />
      <el-table-column prop="name" label="品牌名称" min-width="100" />
      <el-table-column prop="desc" label="品牌介绍" min-width="150" show-overflow-tooltip />
      <el-table-column prop="remark" label="品牌备注" min-width="150" show-overflow-tooltip />
      <el-table-column label="禁用状态" align="center">
        <template #default="scope">
          <el-text v-if="scope.row.is_disable == 0" type="warning">未禁用</el-text>
          <el-text v-else-if="scope.row.is_disable == 1" type="success">已禁用</el-text>
          <el-text v-else>-</el-text>
        </template>
      </el-table-column>
      <el-table-column prop="create_time" label="新增时间" min-width="165" sortable="custom" />
      <el-table-column prop="update_time" label="更新时间" min-width="165" sortable="custom" />
      <el-table-column label="操作" width="95">
        <template #default="scope">
          <el-link type="success" class="mr-1" :underline="false" @click="infoEdits(scope.row)">
            编辑
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="count > 0"
      v-model:total="count"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="list"
    />

    <!-- 添加修改 -->
    <el-dialog
      v-model="dialog"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="cancel"
      top="5vh"
    >
      <el-form ref="ref" :rules="rules" :model="model" label-width="100px">
        <el-tabs>
          <el-tab-pane label="信息">
            <el-scrollbar native :height="height - 80">
              <el-form-item label="品牌名称" prop="name">
                <el-input v-model="model.name" placeholder="请输入名称" />
              </el-form-item>
              <el-form-item label="简介" prop="desc">
                <el-input v-model="model.desc" placeholder="请输入简介" />
              </el-form-item>
              <el-form-item label="备注" prop="remark">
                <el-input v-model="model.remark" placeholder="备注" clearable />
              </el-form-item>
              <el-form-item label="禁用">
                <el-switch v-model="model.is_disable" :active-value="1" :inactive-value="0" />
                <el-text size="default"> </el-text>
              </el-form-item>

              <el-form-item v-if="model[idkey]" label="添加时间" prop="create_time">
                <el-input v-model="model.create_time" disabled />
              </el-form-item>
              <el-form-item v-if="model[idkey]" label="修改时间" prop="update_time">
                <el-input v-model="model.update_time" disabled />
              </el-form-item>
              <el-form-item v-if="model.delete_time" label="删除时间" prop="delete_time">
                <el-input v-model="model.delete_time" disabled />
              </el-form-item>
            </el-scrollbar>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <el-button :loading="loading" @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submit">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import checkPermission from '@/utils/permission'
import screenHeight from '@/utils/screen-height'
import Pagination from '@/components/Pagination/index.vue'
import { getPageLimit } from '@/utils/settings'
import * as Project from '@/api/project/brand'
import { useFormDisabled } from 'element-plus'
import { ElMessage } from 'element-plus'
const model = ref({})
const rowInfo = ref({})

const loading = ref(true)
const data = ref([])
const height = ref(680)
const query = ref({
  page: 1,
  limit: getPageLimit()
})
const count = ref(0)
const idkey = ref('id')

const dialog = ref(false)
const dialogTitle = ref('')
function list() {
  loading.value = true
  Project.list(query.value)
    .then((res) => {
      data.value = res.data.list
      count.value = res.data.count
      loading.value = false
    })
    .catch((error) => {
      console.log(error)
      loading.value = false
    })
}
function infoEdits(row) {
  rowInfo.value = row
  loading.value = true
  dialog.value = true
  dialogTitle.value = row.name + '详情：' + row[idkey.value]
  var id = {}
  id[idkey.value] = row[idkey.value]
  Project.info(id)
    .then((res) => {
      reset(res.data)
    })
    .catch(() => {})
  loading.value = false
}

function add() {
  loading.value = true
  dialog.value = true
  dialogTitle.value = '新增'
  loading.value = false
}
function submit() {
  loading.value = true
  // 验证
  let dataEdit = {}

  if (!model.value.name.trim()) {
    ElMessage.error('品牌名称不能为空')
    loading.value = false
    return
  }

  // 备注
  if (rowInfo.value.remark != model.value.remark) {
    dataEdit['remark'] = model.value.remark
  }

  // 备注
  if (rowInfo.value.desc != model.value.desc) {
    dataEdit['desc'] = model.value.desc
  }

  // 排序
  if (rowInfo.value.name != model.value.name) {
    dataEdit['name'] = model.value.name
  }
  // 是否已经回访
  if (rowInfo.value.is_disable != model.value.is_disable) {
    dataEdit['is_disable'] = model.value.is_disable
  }
  if (Object.keys(dataEdit).length === 0) {
    ElMessage.success('修改成功')
    dialog.value = false
  } else {
    dataEdit[idkey.value] = model.value[idkey.value]

    Project.edit(dataEdit)
      .then((res) => {
        list()
        dialog.value = false
      })
      .catch(() => {
        loading.value = false
      })
  }

  loading.value = false
}
// 排序
// 排序
function sort(sort) {
  query.value.sort_field = sort.prop
  query.value.sort_value = ''
  console.log('query.value:', query.value)
  if (sort.order === 'ascending') {
    query.value.sort_value = 'asc'
    list()
  }
  if (sort.order === 'descending') {
    query.value.sort_value = 'desc'
    list()
  }
}
// 操作
function select(selection) {
  this.selection = selection
  this.selectIds = this.selectGetIds(selection).toString()
}

function cancel() {
  dialog.value = false
  reset()
}
//搜索
const queryRef = ref(null)
function resetQuery() {
  query.value.is_disable = '-1'
  query.value.name = ''
  query.value.date_field = ''
  query.value.date_value[0] = ''
  query.value.date_value[1] = ''
  queryRef.value.resetFields()
  handleQuery()
}
function handleQuery() {
  query.value.pageNum = 1
  list()
}
//搜索end
// 重置
function reset(row) {
  if (row) {
    model.value = row
  } else {
    model.value = {}
  }
}
onMounted(() => {
  height.value = screenHeight(310)
  list()
})
</script>