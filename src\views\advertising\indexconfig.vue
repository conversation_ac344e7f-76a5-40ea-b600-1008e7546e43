<script setup>
import { ref, reactive, watch, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import ImgUploadsHouseType from '@/components/FileManage/ImgUploadsHouseType.vue'
import request from '@/utils/request'

// API URL配置
const apiUrl = '/admin/advertise.IndexConfig/'

// 跳转类型选项
const jumpTypeOptions = [
  { label: '小程序', value: 1 },
  { label: 'H5', value: 2 }
]

// 是否上新选项
const isNewOptions = [
  { label: '是', value: 1 },
  { label: '否', value: 0 }
]
// const is_watermark = 0
const isWatermarks = [
  { label: '是', value: 1 },
  { label: '否', value: 0 }
]
// 初始化数据结构
const initConfigItems = [
  {
    id: 1,
    title: '价格评测',
    icon: [],
    text: '',
    jump_type: 1,
    link: '',
    sort: 1,
    is_new: false,
    is_watermark: 0
  },
  {
    id: 2,
    title: '日照评测',
    icon: [],
    text: '',
    jump_type: 1,
    link: '',
    sort: 2,
    is_new: false,
    is_watermark: 0
  },
  {
    id: 3,
    title: '噪声评测',
    icon: [],
    text: '',
    jump_type: 1,
    link: '',
    sort: 3,
    is_new: false,
    is_watermark: 0
  },
  {
    id: 4,
    title: '景观评测',
    icon: [],
    text: '',
    jump_type: 1,
    link: '',
    sort: 4,
    is_new: false,
    is_watermark: 0
  },
  {
    id: 5,
    title: '配套评测',
    icon: [],
    text: '',
    jump_type: 1,
    link: '',
    sort: 5,
    is_new: false,
    is_watermark: 0
  },
  {
    id: 6,
    title: '户型评测',
    icon: [],
    text: '',
    jump_type: 1,
    link: '',
    sort: 6,
    is_new: false,
    is_watermark: 0
  },
  {
    id: 7,
    title: '板块评测',
    icon: [],
    text: '',
    jump_type: 1,
    link: '',
    sort: 7,
    is_new: false,
    is_watermark: 0
  },
  {
    id: 8,
    title: '产业评测',
    icon: [],
    text: '',
    jump_type: 1,
    link: '',
    sort: 8,
    is_new: false,
    is_watermark: 0
  },
  {
    id: 9,
    title: '直播讲房',
    icon: [],
    text: '',
    jump_type: 1,
    link: '',
    sort: 9,
    is_new: false,
    is_watermark: 0
  },
  {
    id: 10,
    title: '楼市热点',
    icon: [],
    text: '',
    jump_type: 1,
    link: '',
    sort: 10,
    is_new: false,
    is_watermark: 0
  }
]

const configItems = ref([...initConfigItems])
const formRef = ref(null)
const loading = ref(false)
const maxId = ref(10) // 追踪最大的ID，用于添加新卡片

// 获取按照sort排序后的指定范围的项目
const getRowItems = computed(() => {
  return (start, end) => {
    // 按照sort从小到大排序
    const sorted = [...configItems.value].sort((a, b) => a.sort - b.sort)

    // 获取指定范围的项目
    const result = []
    for (let i = start; i < end && i < sorted.length; i++) {
      result.push(sorted[i])
    }

    // 如果结果不足end-start个，用空项目填充
    while (result.length < end - start) {
      result.push({
        id: `empty-${result.length + start}`,
        title: `位置${result.length + start + 1}`,
        text: '',
        icon: [],
        is_new: false,
        link: ''
      })
    }

    return result
  }
})

// 获取配置数据
const getConfigData = async () => {
  loading.value = true
  try {
    const res = await request({
      url: apiUrl + 'info',
      method: 'get',
      params: {
        type: 1
      }
    })
    if (res.code === 200 && res.data) {
      // 处理返回的数据，转换为组件需要的格式
      const newData = res.data.map((item) => ({
        ...item,
        icon: item.icon ? [{ file_url: item.icon }] : [], // 转换图片格式为数组
        is_new: item.is_new,
        jump_type: item.jump_type || 1, // 确保有默认值
        sort: item.sort || 1, // 确保有默认值
        is_watermark: item.is_watermark || 0
      }))

      // 更新最大ID值
      newData.forEach((item) => {
        if (item.id > maxId.value) {
          maxId.value = item.id
        }
      })

      // 如果返回的数据少于10条，用初始数据补齐
      if (newData.length < 10) {
        const remainingItems = initConfigItems.slice(newData.length)
        configItems.value = [...newData, ...remainingItems]
      } else {
        configItems.value = newData
      }
    }
  } catch (error) {
    ElMessage.error('获取配置数据失败')
  } finally {
    loading.value = false
  }
}

// 监听图片变化
watch(
  configItems,
  (newVal) => {
    newVal.forEach((item) => {
      if (item.icon.length > 1) {
        item.icon = [item.icon[item.icon.length - 1]]
      }
    })
  },
  { deep: true }
)

const rules = {
  icon: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (!value || value.length === 0) {
          callback(new Error('请选择图标'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  text: [
    { required: true, message: '请输入文案描述', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入文案描述'))
        } else {
          const chinese = /^[\u4e00-\u9fff\w]{1,4}$/u
          if (!chinese.test(value)) {
            callback(new Error('请输入1-4个汉字'))
          } else {
            callback()
          }
        }
      },
      trigger: 'blur'
    }
  ],
  jump_type: [{ required: true, message: '请选择跳转类型', trigger: 'change' }],
  sort: [
    { required: true, message: '请输入排序号', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入排序号'))
        } else {
          const num = parseInt(value)
          if (isNaN(num) || num < 1) {
            callback(new Error('排序号必须是正整数'))
          } else {
            callback()
          }
        }
      },
      trigger: 'blur'
    }
  ]
}

// 添加新卡片方法
const addNewCard = () => {
  maxId.value += 1
  const newItem = {
    id: maxId.value,
    title: ``,
    icon: [],
    text: '',
    jump_type: 1,
    link: '',
    sort: configItems.value.length + 1,
    is_new: false,
    is_watermark: 0
  }
  configItems.value.push(newItem)
}

// 删除卡片方法
const removeCard = (index) => {
  configItems.value.splice(index, 1)
  // 重新排序
  configItems.value.forEach((item, idx) => {
    item.sort = idx + 1
  })
}

// 提交方法
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 处理提交数据格式
    const submitData = configItems.value.map((item) => ({
      id: item.id,
      title: item.title,
      icon: item.icon[0]?.file_url || '',
      text: item.text,
      jump_type: item.jump_type,
      link: item.link,
      sort: item.sort,
      is_new: item.is_new,
      type: 1, // 添加固定参数
      is_watermark: item.is_watermark // 固定参数
    }))

    const res = await request({
      url: apiUrl + 'save',
      method: 'post',
      data: submitData
    })

    if (res.code === 200) {
      ElMessage.success('保存成功')
      await getConfigData()
    } else {
      ElMessage.error(res.msg || '保存失败')
    }
  } catch (error) {
    ElMessage.error('表单验证失败，请检查输入')
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  getConfigData()
})
</script>

<template>
  <div class="config-container p-6">
    <el-card class="mb-4" v-loading="loading">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-xl font-bold">H5页面配置管理</span>
          <div class="flex gap-3">
            <!-- <el-button type="primary" @click="handleSubmit" :loading="loading">保存配置</el-button> -->
          </div>
        </div>
      </template>

      <el-form ref="formRef" :model="configItems" :rules="rules" label-width="120px">
        <div class="grid grid-cols-2 gap-6">
          <div v-for="(item, index) in configItems" :key="item.id" class="config-item">
            <el-card class="mb-4">
              <template #header>
                <div class="flex justify-between items-center">
                  <div class="text-lg font-medium">{{ item.title }}</div>
                  <el-button
                    v-if="index >= 10"
                    type="danger"
                    size="small"
                    @click="removeCard(index)"
                  >
                    删除
                  </el-button>
                </div>
              </template>

              <el-form-item label="是否添加水印" prop="is_watermark">
                <el-radio-group v-model="item.is_watermark" style="margin-left: 10px">
                  <el-radio
                    v-for="option in isWatermarks"
                    :key="option.value"
                    :label="option.value"
                  >
                    {{ option.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item :prop="`[${index}].icon`" :rules="rules.icon" label="图标">
                <ImgUploadsHouseType
                  v-model="item.icon"
                  source="3"
                  :isWatermark="item.is_watermark"
                  :upload-btn="item.icon.length ? '' : '上传图标'"
                  file-type="image"
                  :height="100"
                />
              </el-form-item>

              <el-form-item :prop="`[${index}].text`" :rules="rules.text" label="文案描述">
                <el-input
                  v-model="item.text"
                  maxlength="4"
                  show-word-limit
                  placeholder="请输入1-4个汉字"
                />
              </el-form-item>

              <el-form-item
                :prop="`[${index}].jump_type`"
                :rules="rules.jump_type"
                label="跳转类型"
              >
                <el-radio-group v-model="item.jump_type">
                  <el-radio
                    v-for="option in jumpTypeOptions"
                    :key="option.value"
                    :label="option.value"
                  >
                    {{ option.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item :prop="`[${index}].link`" :rules="rules.link" label="跳转链接">
                <el-input
                  v-model="item.link"
                  maxlength="200"
                  show-word-limit
                  placeholder="请输入跳转链接"
                  type="textarea"
                  :rows="2"
                />
              </el-form-item>

              <el-form-item :prop="`[${index}].sort`" :rules="rules.sort" label="排序">
                <el-input-number
                  v-model="item.sort"
                  :min="1"
                  :controls="false"
                  placeholder="请输入正整数"
                />
              </el-form-item>

              <el-form-item label="是否上新">
                <el-radio-group v-model="item.is_new">
                  <el-radio
                    v-for="option in isNewOptions"
                    :key="option.value"
                    :label="option.value"
                  >
                    {{ option.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-card>
          </div>
        </div>

        <!-- 添加新增卡片按钮到表单底部右侧 -->
        <div class="flex justify-end mt-4">
          <el-button type="success" @click="addNewCard">新增卡片</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="loading">保存配置</el-button>
        </div>
      </el-form>
    </el-card>

    <!-- 预览区域 -->
    <el-card class="preview-container mb-4">
      <template #header>
        <div class="text-xl font-bold">效果预览</div>
      </template>

      <div class="preview-wrapper">
        <el-scrollbar horizontal>
          <div class="preview-content">
            <!-- 所有项目按照排序从小到大排列，每行5个 -->
            <div class="preview-grid">
              <div
                v-for="(item, index) in [...configItems].sort((a, b) => a.sort - b.sort)"
                :key="item.id"
                class="preview-item"
                @click="item.link && window.open(item.link)"
              >
                <div class="icon-wrapper">
                  <img
                    :src="item.icon[0]?.file_url || '/api/placeholder/48/48'"
                    class="icon-image"
                    alt="icon"
                  />
                  <span v-if="item.is_new" class="new-badge">新</span>
                </div>
                <div class="item-title">{{ item.text || item.title }}</div>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </el-card>
  </div>
</template>

<style scoped>
.preview-item:hover {
  opacity: 0.8;
  cursor: pointer;
}

.config-item :deep(.file-row) {
  min-height: 142px;
}

/* 预览区域样式 */
.preview-container {
  overflow: hidden;
  margin-bottom: 20px;
}

.preview-wrapper {
  width: 100%;
  position: relative;
}

.preview-content {
  width: max-content;
  min-width: 100%;
  padding: 10px;
}

.preview-grid {
  display: grid;
  grid-template-rows: repeat(2, auto);
  grid-auto-flow: column;
  grid-template-columns: repeat(5, 320px);
  gap: 30px;
  min-width: 100%;
}

.preview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 160px;
  min-width: 160px;
  text-align: center;
}

.icon-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 12px;
  width: 64px;
  height: 64px;
  background-color: #f8f8f8;
  border-radius: 16px;
  overflow: hidden;
}

.icon-image {
  width: 40px;
  height: 40px;
  object-fit: contain;
  margin: 12px;
}

.new-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background-color: #f56c6c;
  color: white;
  font-size: 12px;
  padding: 1px 6px;
  border-radius: 8px;
  line-height: 1.5;
}

.item-title {
  font-size: 14px;
  color: #333;
  max-width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

/* 滚动容器样式 */
.preview-wrapper :deep(.el-scrollbar__wrap) {
  overflow-x: auto;
  overflow-y: hidden;
}

.preview-wrapper :deep(.el-scrollbar__bar.is-horizontal) {
  height: 6px;
}

/* 添加滚动提示阴影 */
.preview-wrapper::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 30px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.9));
  pointer-events: none;
  z-index: 10;
  display: none;
}

/* 当有溢出内容时显示阴影 */
.preview-wrapper:hover::after {
  display: block;
}
</style>
