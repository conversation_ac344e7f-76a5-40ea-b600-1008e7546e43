<template>
  <div class="test-container">
    <h2>类型清空问题调试</h2>
    
    <el-card style="margin-bottom: 20px;">
      <template #header>
        <span>调试控制面板</span>
      </template>
      
      <div style="margin-bottom: 20px;">
        <el-button type="primary" @click="simulateEdit">模拟编辑（客厅-吊顶）</el-button>
        <el-button type="warning" @click="switchToKitchen">切换到厨房（应该清空）</el-button>
        <el-button type="success" @click="switchToBedroom">切换到卧室（应该保持）</el-button>
        <el-button @click="manualClear">手动清空</el-button>
        <el-button @click="forceUpdate">强制更新</el-button>
      </div>
      
      <div style="background: #f0f0f0; padding: 10px; border-radius: 4px;">
        <div><strong>表单数据：</strong> {{ JSON.stringify(form) }}</div>
        <div><strong>位置列表：</strong> {{ positions.length }} 项</div>
        <div><strong>类型列表：</strong> {{ categories.length }} 项</div>
        <div><strong>选择器引用：</strong> {{ categorySelectRef ? '存在' : '不存在' }}</div>
        <div><strong>选择器值：</strong> {{ categorySelectRef?.modelValue }}</div>
      </div>
    </el-card>

    <el-card>
      <template #header>
        <span>表单测试</span>
      </template>
      
      <el-form :model="form" label-width="120px">
        <el-form-item label="位置">
          <el-select 
            v-model="form.position_id" 
            placeholder="请选择位置"
            @change="handlePositionChange"
            style="width: 300px;"
          >
            <el-option
              v-for="item in positions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="类型">
          <el-select 
            ref="categorySelectRef"
            v-model="form.category_id" 
            placeholder="请选择类型"
            :disabled="!form.position_id"
            :loading="categoriesLoading"
            :key="`category-${form.position_id}-${categories.length}-${updateKey}`"
            style="width: 300px;"
            @change="onCategoryChange"
          >
            <el-option
              v-for="item in categories"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <div style="margin-top: 20px;">
        <h4>实时监控：</h4>
        <div style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace;">
          <div>form.position_id: {{ form.position_id }}</div>
          <div>form.category_id: {{ form.category_id }}</div>
          <div>categories: {{ categories.map(c => `${c.id}:${c.name}`).join(', ') }}</div>
          <div>updateKey: {{ updateKey }}</div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'CategoryClearTest'
})

// 模拟数据
const mockPositions = [
  { id: 1, name: '客厅' },
  { id: 2, name: '卧室' },
  { id: 3, name: '厨房' }
]

const mockCategories = {
  1: [ // 客厅
    { id: 1, name: '地板' },
    { id: 2, name: '墙面' },
    { id: 3, name: '吊顶' }
  ],
  2: [ // 卧室 - 包含地板，不包含吊顶
    { id: 1, name: '地板' },
    { id: 2, name: '墙面' },
    { id: 4, name: '衣柜' }
  ],
  3: [ // 厨房 - 不包含地板和吊顶
    { id: 2, name: '墙面' },
    { id: 5, name: '橱柜' },
    { id: 6, name: '台面' }
  ]
}

// 响应式数据
const form = ref({
  position_id: '',
  category_id: ''
})

const positions = ref(mockPositions)
const categories = ref([])
const categoriesLoading = ref(false)
const categorySelectRef = ref()
const updateKey = ref(0)

// 监听表单变化
watch(() => form.value.category_id, (newVal, oldVal) => {
  console.log(`🔍 category_id 变化: ${oldVal} -> ${newVal}`)
}, { immediate: true })

// 模拟加载类型数据
const loadCategories = async (positionId) => {
  categoriesLoading.value = true
  console.log(`📡 开始加载位置 ${positionId} 的类型数据`)
  
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  categories.value = mockCategories[positionId] || []
  categoriesLoading.value = false
  
  console.log(`✅ 类型数据加载完成:`, categories.value)
  return categories.value
}

// 位置改变处理
const handlePositionChange = async (positionId) => {
  console.log('\n=== 位置改变处理开始 ===')
  console.log('新位置ID:', positionId)
  console.log('当前类型ID:', form.value.category_id)
  
  const currentCategoryId = form.value.category_id
  
  if (positionId) {
    await loadCategories(positionId)
    
    if (currentCategoryId) {
      const categoryExists = categories.value.find(item => item.id == currentCategoryId)
      console.log('类型是否存在:', categoryExists)
      
      if (categoryExists) {
        console.log('✅ 保持类型选择')
        form.value.category_id = currentCategoryId
      } else {
        console.log('❌ 清空类型选择')
        
        // 尝试多种清空方法
        console.log('方法1: 直接赋值空字符串')
        form.value.category_id = ''
        
        await nextTick()
        console.log('nextTick后的值:', form.value.category_id)
        
        if (form.value.category_id !== '') {
          console.log('方法2: 重新创建对象')
          const newForm = { ...form.value, category_id: '' }
          form.value = newForm
          
          await nextTick()
          console.log('重新创建后的值:', form.value.category_id)
        }
        
        if (form.value.category_id !== '') {
          console.log('方法3: 强制更新key')
          updateKey.value++
          
          await nextTick()
          form.value.category_id = ''
          
          await nextTick()
          console.log('强制更新后的值:', form.value.category_id)
        }
      }
    }
  } else {
    categories.value = []
    form.value.category_id = ''
  }
  
  console.log('=== 位置改变处理结束 ===')
  console.log('最终类型ID:', form.value.category_id)
  console.log('最终表单:', JSON.stringify(form.value))
}

// 类型选择改变
const onCategoryChange = (value) => {
  console.log('🎯 类型选择改变:', value)
}

// 测试函数
const simulateEdit = async () => {
  console.log('\n🧪 模拟编辑：客厅-吊顶')
  form.value.position_id = 1
  await loadCategories(1)
  await nextTick()
  form.value.category_id = 3 // 吊顶
  ElMessage.info('已设置为：客厅-吊顶')
}

const switchToKitchen = () => {
  console.log('\n🧪 切换到厨房（应该清空类型）')
  form.value.position_id = 3
  ElMessage.info('切换到厨房，观察类型是否被清空')
}

const switchToBedroom = () => {
  console.log('\n🧪 切换到卧室（应该保持类型）')
  form.value.position_id = 2
  ElMessage.info('切换到卧室，观察类型是否保持')
}

const manualClear = () => {
  console.log('\n🧪 手动清空')
  form.value.category_id = ''
  updateKey.value++
  ElMessage.info('手动清空类型')
}

const forceUpdate = () => {
  console.log('\n🧪 强制更新')
  updateKey.value++
  ElMessage.info('强制更新组件')
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

h2 {
  color: #303133;
  margin-bottom: 20px;
}

h4 {
  margin: 10px 0;
  color: #606266;
}
</style>
