import request from '@/utils/request'
// 会员优惠券
const url = '/admin/member.MemberCoupon/'
/**
 * 获取会员优惠券列表
 * @param {array} params 请求参数
 */
export function list(params) {
  return request({
    url: url + 'list',
    method: 'get',
    params: params
  })
}


/**
 * 获取优惠券详细信息
 * @param {array} params 请求参数
 */
export function info(params) {
  return request({
    url: url + 'info',
    method: 'get',
    params: params
  })
}



/**
 * 优惠券核销
 * @param {array} params 请求参数
 */
export function edit(data) {
    return request({
        url: url + 'edit',
        method: 'post',
        data
      })
}