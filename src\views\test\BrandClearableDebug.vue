<template>
  <div class="test-container">
    <h2>品牌清除功能调试</h2>
    
    <el-card style="margin-bottom: 20px;">
      <template #header>
        <span>调试信息</span>
      </template>
      
      <div style="background: #f0f0f0; padding: 10px; border-radius: 4px; margin-bottom: 20px;">
        <div><strong>currentItem 存在：</strong> {{ !!currentItem }}</div>
        <div><strong>brand_list 存在：</strong> {{ !!(currentItem && currentItem.brand_list) }}</div>
        <div><strong>brand_list 长度：</strong> {{ currentItem?.brand_list?.length || 0 }}</div>
        <div><strong>brand_list 内容：</strong> {{ JSON.stringify(currentItem?.brand_list || []) }}</div>
      </div>
      
      <div style="margin-bottom: 20px;">
        <el-button type="primary" @click="initializeData">初始化数据</el-button>
        <el-button type="success" @click="addTestBrand">添加测试品牌</el-button>
        <el-button type="warning" @click="clearBrands">清空品牌</el-button>
        <el-button @click="logData">打印数据</el-button>
      </div>
    </el-card>

    <el-card>
      <template #header>
        <span>品牌选择器</span>
      </template>
      
      <el-form label-width="120px">
        <el-form-item label="选择品牌">
          <el-select
            v-model="selectedBrandIds"
            multiple
            placeholder="请选择品牌"
            style="width: 100%;"
            @change="handleBrandSelectionChange"
          >
            <!-- 自定义标签显示 -->
            <template #tag>
              <el-tag 
                v-for="item in (currentItem?.brand_list || [])" 
                :key="item.id" 
                type="primary" 
                closable
                @close="handleRemoveBrand(item.id)"
                class="brand-tag"
                style="margin: 2px;"
              >
                {{ getBrandName(item.id) }}
              </el-tag>
            </template>
            
            <el-option
              v-for="brand in brandOptions"
              :key="brand.id"
              :label="brand.union_brand"
              :value="brand.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <div style="margin-top: 20px;">
        <h4>操作日志：</h4>
        <div style="background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto;">
          <div v-for="(log, index) in operationLogs" :key="index" style="margin-bottom: 5px;">
            <span style="color: #999;">{{ log.time }}</span> - {{ log.message }}
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'BrandClearableDebug'
})

// 模拟品牌数据
const brandOptions = ref([
  { id: 1, union_brand: '苹果 Apple' },
  { id: 2, union_brand: '三星 Samsung' },
  { id: 3, union_brand: '华为 Huawei' },
  { id: 4, union_brand: '小米 Xiaomi' },
  { id: 5, union_brand: '索尼 Sony' }
])

// 当前项目数据
const currentItem = ref(null)

// 操作日志
const operationLogs = ref([])

// 计算选中的品牌ID列表
const selectedBrandIds = computed({
  get() {
    return currentItem.value?.brand_list?.map(item => item.id) || []
  },
  set(newIds) {
    if (!currentItem.value) {
      addLog('错误：currentItem 不存在')
      return
    }
    
    if (!currentItem.value.brand_list) {
      currentItem.value.brand_list = []
    }
    
    // 更新品牌列表
    currentItem.value.brand_list = newIds.map(id => ({ id }))
    addLog(`更新品牌列表：${newIds.join(', ')}`)
  }
})

// 添加日志
const addLog = (message) => {
  const time = new Date().toLocaleTimeString()
  operationLogs.value.push({ time, message })
  console.log(`[${time}] ${message}`)
}

// 获取品牌名称
const getBrandName = (id) => {
  const brand = brandOptions.value.find(item => item.id === id)
  return brand ? brand.union_brand : `未知品牌(${id})`
}

// 处理品牌选择变化
const handleBrandSelectionChange = (newIds) => {
  addLog(`品牌选择变化：${newIds.join(', ')}`)
}

// 处理移除品牌
const handleRemoveBrand = (brandId) => {
  addLog(`尝试移除品牌ID：${brandId}`)
  
  // 安全检查
  if (!currentItem.value) {
    addLog('错误：currentItem.value 不存在')
    ElMessage.error('数据错误：currentItem 不存在')
    return
  }
  
  if (!currentItem.value.brand_list) {
    addLog('错误：currentItem.value.brand_list 不存在')
    ElMessage.error('数据错误：brand_list 不存在')
    return
  }
  
  // 移除品牌
  const originalLength = currentItem.value.brand_list.length
  currentItem.value.brand_list = currentItem.value.brand_list.filter(item => item && item.id !== brandId)
  
  const newLength = currentItem.value.brand_list.length
  
  if (newLength < originalLength) {
    const brandName = getBrandName(brandId)
    addLog(`成功移除品牌：${brandName} (${brandId})`)
    ElMessage.success(`已移除品牌：${brandName}`)
  } else {
    addLog(`移除失败：品牌ID ${brandId} 不存在`)
    ElMessage.warning(`品牌不存在或已被移除`)
  }
}

// 测试函数
const initializeData = () => {
  currentItem.value = {
    id: 1,
    name: '测试项目',
    brand_list: []
  }
  addLog('初始化数据完成')
  ElMessage.success('数据初始化完成')
}

const addTestBrand = () => {
  if (!currentItem.value) {
    ElMessage.warning('请先初始化数据')
    return
  }
  
  if (!currentItem.value.brand_list) {
    currentItem.value.brand_list = []
  }
  
  // 添加几个测试品牌
  const testBrands = [
    { id: 1 }, // 苹果
    { id: 3 }, // 华为
    { id: 5 }  // 索尼
  ]
  
  currentItem.value.brand_list = testBrands
  addLog('添加测试品牌：苹果、华为、索尼')
  ElMessage.success('已添加测试品牌')
}

const clearBrands = () => {
  if (currentItem.value) {
    currentItem.value.brand_list = []
    addLog('清空所有品牌')
    ElMessage.success('已清空所有品牌')
  }
}

const logData = () => {
  console.log('=== 当前数据状态 ===')
  console.log('currentItem:', currentItem.value)
  console.log('brand_list:', currentItem.value?.brand_list)
  console.log('selectedBrandIds:', selectedBrandIds.value)
  addLog('数据已打印到控制台')
}

// 监听数据变化
watch(currentItem, (newVal) => {
  addLog(`currentItem 变化：${newVal ? '有数据' : '无数据'}`)
}, { deep: true })

// 初始化
initializeData()
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

h2 {
  color: #303133;
  margin-bottom: 20px;
}

h4 {
  margin: 10px 0;
  color: #606266;
}

.brand-tag {
  cursor: pointer;
}

.brand-tag:hover {
  opacity: 0.8;
}
</style>
