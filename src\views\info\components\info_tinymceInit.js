import tinymce from 'tinymce/tinymce'
import '@/assets/tinymce/zh_CN.js'
import '@/assets/tinymce/skin.min.css'

import 'tinymce/plugins/image' // 插入编辑图片
import 'tinymce/plugins/media' // 插入编辑视频
import 'tinymce/plugins/link' // 插入编辑图片
import 'tinymce/plugins/wordcount' // 字数统计
import 'tinymce/plugins/paste' // 字数统计
import 'tinymce/plugins/print' //
import 'tinymce/plugins/preview' //
import 'tinymce/plugins/code'
import 'tinymce/plugins/table'
import 'tinymce/themes/silver' // 主题js
import 'tinymce/icons/default' // 图标css
import { ElLoading } from 'element-plus'

import { useUserStoreHook } from '@/store/modules/user'
const toolbar =
  'code formatselect lineheight media image table  bold italic forecolor backcolor  alignleft aligncenter alignright alignjustify  bullist numlist outdent indent  link  '
// image
const getImageUrl =
  import.meta.env.VITE_APP_BASE_URL + '/admin/info.Information/getImage?image_url='
const uploadUrl = import.meta.env.VITE_APP_BASE_URL + '/admin/file.File/add'
const uploadImage = async (originalUrl, e) => {
  e.preventDefault()
  const userStore = useUserStoreHook()
  const tokenValue = userStore.token
  const response = await fetch(`${getImageUrl}${originalUrl}`, {
    method: 'GET',
    headers: {
      AdminToken: tokenValue
    }
  })
 
  const blob = await response.blob()
  const formData = new FormData()
  formData.append('file', blob, 'image.png') // 可以根据实际需要生
  const uploadResponse = await fetch(uploadUrl, {
    method: 'POST',
    headers: {
      AdminToken: tokenValue
    },
    body: formData
  })
  const result = await uploadResponse.json()
  if (result.code && result.data.file_url) {
    return result.data.file_url
  } else {
    console.error('图片上传失败', result)
    return originalUrl // 如果上传失败，保留原始 URL
  }
}

async function handlePaste(e) {
  const clipboardData = e.clipboardData || window.clipboardData
  const htmlContent = clipboardData.getData('text/html') // 获取复制的 HTML 内容
  if (!htmlContent) return
  const htmlElement = new DOMParser().parseFromString(htmlContent, 'text/html')
  const images = htmlElement.querySelectorAll('img')

  // 对所有粘贴的图片进行上传处理
  let loadingInstance
  if (images.length) {
    loadingInstance = ElLoading.service({ fullscreen: true, text: '图片替换中' })
    for (let img of images) {
      let originalUrl = img.src
      let newImageUrl
      try {
        // 上传图片并获取新 URL
        newImageUrl = await uploadImage(originalUrl, e)
      } catch {
        newImageUrl = originalUrl
      }

      img.src = newImageUrl // 替换图片链接
      img.removeAttribute('crossorigin')
      console.log(img)
    }
  } else {
    return
  }

  // 将更新后的 HTML 插入编辑器
  const updatedHtml = htmlElement.body.innerHTML
  tinymce.activeEditor.insertContent(updatedHtml)
  if (images.length) {
    loadingInstance.close()
  }
  e.preventDefault() // 阻止默认的粘贴行为
}

const tinymceInit = {
  language: 'zh_CN',
  skin: false,
  forced_root_block: 'p', // 强制使用 <p> 标签作为根标签
  force_p_newlines: true, // 强制每个回车插入 <p> 标签，而不是 <div> 或其他标签
  force_br_newlines: false, // 禁用 <br> 标签
  content_css: 'https://admin.betterhousing.cn/static/css/content.min.css',
  plugins: 'print preview table media image wordcount paste link code', // 图片上传和字数显示插件
  toolbar, // 菜单栏
  branding: false, // 禁止显示tinymce标识
  resize: false, // 禁止resize监听
  height: 800,
  width: 400,
  paste_data_images: true, // 网络上传图片，false变为base64文件
  link_context_toolbar: true,
  lineheight_formats:
    '1.0 1.2 1.5 1.8 2.0 2.5 3.0 3.5 4.0 4.5 5.0 5.5 6.0 6.5 7.0 7.5 8.0 8.5 9.0 9.5 10', // 预定义的行高选项
  images_upload_handler: (blobInfo, success, failure) => {
    const userStore = useUserStoreHook()
    const tokenValue = userStore.token

    const xhr = new XMLHttpRequest()
    xhr.open('POST', uploadUrl, true)
    xhr.withCredentials = false
    xhr.setRequestHeader('AdminToken', tokenValue)
     let loadingInstance
     loadingInstance = ElLoading.service({ fullscreen: true, text: '图片上传中' })
    xhr.onload = function () {
      let json
      json = JSON.parse(xhr.responseText)
      console.log(json)
      if (json.code !== 200) {
        failure(json.msg)
      }
      loadingInstance.close()
      if (!json || typeof json.data.file_url !== 'string') {
        failure(json.msg)
        return
      }
      success(json.data.file_url)
    }
    const formData = new FormData()
    var file = blobInfo.blob() //转化为易于理解的file对象
    console.log(file)
    formData.append('file', file, file.name) //此处与源文档不一样
    xhr.send(formData)
  },
  setup: function (editor) {
    editor.on('paste', function (e) {
      handlePaste(e)
    })
    editor.on('keydown', function (e) {
      if (e.key === 'Enter') {
        e.preventDefault() // 阻止默认的回车行为
        // 插入一个新的 <p> 标签
        editor.insertContent('<p>&nbsp;</p>')
      }
    })
  }
}

export { tinymceInit, tinymce }
