<template>
  <div class="app-container">
    <el-form ref="queryRef" :model="query" :inline="true" label-width="90px">
      <el-form-item label="推广位名称：" prop="adname">
        <el-input
          v-model="query.adname"
          maxlength="20"
          placeholder="请输入名称"
          style="width: 140px"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" title="搜索" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>

        <el-button @click="handleAdd">添加广告</el-button>
      </el-form-item>
    </el-form>
    <!-- 搜索end -->

    <el-table ref="table" v-loading="loading" :data="data" :height="600">
      <el-table-column fixed prop="id" label="ID" width="80" />
      <el-table-column prop="adname" label="广告名称" min-width="150" show-overflow-tooltip />
      <el-table-column label="广告状态" min-width="150">
        <template #default="{ row }">
          <el-text v-if="row.state == 1">有效</el-text>
          <el-text v-else-if="row.state == 2">无效</el-text>
          <el-text v-else>--</el-text>
        </template>
      </el-table-column>
      <el-table-column prop="site.name" label="广告位" min-width="150" show-overflow-tooltip />
      <el-table-column label="广告类型" min-width="150">
        <template #default="{ row }">
          <el-text v-if="row.type == 1">小程序</el-text>
          <el-text v-else-if="row.type == 2">H5</el-text>
          <el-text v-else>--</el-text>
        </template>
      </el-table-column>
      <el-table-column label="素材类型" min-width="150">
        <template #default="{ row }">
          <el-text v-if="row.materials_type == 1">图片</el-text>
          <el-text v-else-if="row.materials_type == 2">视频</el-text>
          <el-text v-else>--</el-text>
        </template>
      </el-table-column>

      <el-table-column label="广告顺序" min-width="150">
        <template #default="{ row }">
          <el-text>{{ row.sort }}</el-text>
        </template>
      </el-table-column>
      <el-table-column prop="create_time" label="创建时间" min-width="150" />
      <el-table-column prop="update_time" label="更新时间" min-width="150" />

      <el-table-column fixed="right" label="操作" width="125">
        <template #default="scope">
          <el-link type="primary" class="mr-1" :underline="false" @click="handleEdit(scope.row)">
            编辑
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="count > 0"
      v-model:total="count"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="getList"
    />
    <el-dialog
      v-model="dialogshow"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="dialogcancel"
      width="30%"
      top="5vh"
    >
      <el-form ref="formref" :model="model" label-width="100px">
        <el-form-item label="广告名称" prop="adname">
          <el-input key="adname" v-model="model.adname" placeholder="请输入名称" clearable />
        </el-form-item>
        <el-form-item label="广告链接" prop="adurl">
          <el-input key="adurl" v-model="model.adurl" placeholder="请输入广告链接" clearable />
        </el-form-item>
        <el-form-item label="是否添加水印" prop="is_watermark">
          <el-radio-group v-model="model.is_watermark" style="margin-left: 10px">
            <el-radio label="否" :value="0"></el-radio>
            <el-radio label="是" :value="1"></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="guanggaolable" prop="adimg">
          <ImgUpload
            :isWatermark="model.is_watermark"
            :source="resetmodel.source"
            v-model:file-url="model.adimg"
            :file-type="selectfiletype"
            :height="200"
            :width="200"
            upload
          />
        </el-form-item>
        <el-form-item v-show="cover_image_show" label="视频封面" prop="cover_image">
          <ImgUpload
            v-model:file-url="model.cover_image"
            :isWatermark="model.is_watermark"
            :source="resetmodel.source"
            file-type="image"
            :height="100"
            :width="100"
            upload
          />
        </el-form-item>
        <el-form-item label="广告排序" prop="sort">
          <el-input key="sort" v-model="model.sort" placeholder="广告排序" clearable />
        </el-form-item>
        <el-form-item label="是否有效" prop="state">
          <el-radio-group v-model="model.state">
            <el-radio :value="1">正常</el-radio>
            <el-radio :value="2">下架</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="广告类型" prop="type">
          <el-radio-group v-model="model.type">
            <el-radio :value="1">小程序</el-radio>
            <el-radio :value="2">H5</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="素材类型" prop="materials_type" @change="changeType">
          <el-radio-group v-model="model.materials_type">
            <el-radio :value="1">图片</el-radio>
            <el-radio :value="2">视频</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button :loading="loading" @click="dialogcancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submit">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { getPageLimit } from '@/utils/settings'
import { list, add, show, edit } from '@/api/advertising/advertising'
import { useRouter } from 'vue-router'
import Pagination from '@/components/Pagination/index.vue'

import { ref } from 'vue'
const route = useRoute()

const routeQuery = computed(() => route.query)

const loading = ref(true)
const formref = ref(null)
const dialogshow = ref(false)
const cover_image_show = ref(false)
const dialogTitle = ref('添加广告')
const data = ref([])
const resetmodel = {
  adname: '',
  adurl: '',
  state: 1,
  sort: 100,
  adimg: '',
  site_id: 0,
  type: 1,
  materials_type: 1,
  cover_image: '',
  is_watermark: 0,
  source: 3
}
// const model = ref({adname:'',adurl:'',state:1,sort:100,adimg:'',site_id:0,type:1,materials_type:1,cover_image:''});
const model = ref({ ...resetmodel })
const query = ref({
  page: 1,
  limit: getPageLimit(),
  site_id: routeQuery.value.site_id ? routeQuery.value.site_id : 0,
  adname: ''
})
const guanggaolable = ref('广告图片')
const count = ref(0)

const queryRef = ref(null)
const selectfiletype = ref('image')
function changeType() {
  if (model.value.materials_type == 2) {
    selectfiletype.value = 'video'
    guanggaolable.value = '广告视频'
    cover_image_show.value = true
  } else {
    selectfiletype.value = 'image'
    guanggaolable.value = '广告图片'
    cover_image_show.value = false
  }
}
const getList = async () => {
  loading.value = true
  try {
    const res = await list(query.value)
    data.value = res.data.list
    count.value = res.data.count
  } finally {
    loading.value = false
  }
}
const dialogcancel = () => {
  dialogshow.value = false
}
const resetQuery = () => {
  query.value.priceState = ''
  query.value.evaluating = ''
  queryRef.value.resetFields()
  handleQuery()
}

const handleQuery = () => {
  query.value.pageNum = 1
  getList()
}

const handleAdd = () => {
  model.value = resetmodel
  if (formref.value) {
    formref.value.resetFields()
  }

  dialogshow.value = true
  dialogTitle.value = '添加广告'
}

const handleEdit = async (row) => {
  const res = await show({ id: row.id })
  model.value = res.data
  model.value.is_watermark = 0
  dialogshow.value = true
  dialogTitle.value = '编辑广告'
  changeType()
}

const submit = async () => {
  loading.value = true
  try {
    if (model.value.id) {
      const res = await edit(model.value)
      ElMessage.success(res.msg)
      loading.value = false
      if (res.code == 200) {
        getList()
      }
    } else {
      const res = await add({ ...model.value, site_id: routeQuery.value.site_id })
      ElMessage.success(res.msg)
      loading.value = false
      if (res.code == 200) {
        getList()
      }
    }
    dialogshow.value = false
  } finally {
    loading.value = false
  }
}

onMounted(getList)
</script>

<style lang="scss" scoped></style>
