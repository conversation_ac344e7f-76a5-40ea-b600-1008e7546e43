<template>
  <div class="app-container">
    <el-tabs v-model="activeModel">
      <el-tab-pane label="楼盘基础信息" name="houseinfo">
        <HouseInfo :houseId="house_id" @custom-event="handleDataFromChild"/>
      </el-tab-pane>

      <el-tab-pane v-if="house_id > 0" label="楼盘相册" lazy name="houseimage">
        <HouseImageManage :houseId="house_id"/>
      </el-tab-pane>

      <el-tab-pane v-if="house_id > 0" label="楼盘动态" lazy name="housenews">
        <HouseNews :houseId="house_id"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import HouseInfo from './components/HouseInfo.vue'
import HouseImageManage from './components/HouseImage.vue'
import HouseNews from "@/views/house/components/HouseNews.vue";

export default {
  name: 'HouseInfoIndex',

  components: {
    HouseInfo,
    HouseImageManage,
    HouseNews,
  },
  data() {
    return {
      name: '楼盘基础信息',
      house_id: '',
      activeModel: "houseinfo"
    }
  },
  methods: {
    checkPermission,
    handleDataFromChild(house_id) {
      this.house_id = house_id; // John
      this.activeModel = "houseimage"
    }
  },
  mounted() {
	  if(this.$route.query.id){
		  this.house_id = this.$route.query.id; // Johnelse{}
	  }else{
		this.house_id = 0  
	  }
    
  }
}
</script>
