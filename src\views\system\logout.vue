<template>
  <div v-loading.fullscreen.lock="loading" element-loading-text="正在退出"></div>
</template>

<script>
import { handleSystemRedirectLogin } from '@/utils/index';

export default {
  name: 'SystemLogout',
  data() {
    return {
      name: '退出',
      loading: true
    }
  },
  created() {
    this.logout()
  },
  methods: {
    async logout() {
      await this.$store.dispatch('user/logout')

      handleSystemRedirectLogin(window.location.href.split('#')[1]);
      // this.$router.push('/login')
      // this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    }
  }
}
</script>
