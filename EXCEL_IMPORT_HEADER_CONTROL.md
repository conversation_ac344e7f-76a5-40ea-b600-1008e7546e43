# Excel导入组件表头行控制功能说明

## 功能概述

Excel导入组件现在支持父组件控制表头从第几行开始读取，并且从表头下一行开始读取数据。

## 使用场景

### 典型的Excel文件结构
```
第1行: 智能评测详细数据导入模板 (解释说明)
第2行: *产品名称  位置  类型  材质  品牌  备注 (表头)
第3行: 智能门锁  客厅  安防  金属  海康威视  高端产品 (数据)
第4行: 智能开关  卧室  照明  塑料  小米  性价比高 (数据)
...
```

在这种情况下：
- `headerRowIndex = 1` (第2行是表头，索引从0开始)
- 数据从第3行开始读取 (headerRowIndex + 1)

## 组件属性

### headerRowIndex 属性
```javascript
props: {
  headerRowIndex: { 
    type: Number, 
    default: 1, 
    validator: (value) => value >= 0,
    description: 'Excel中表头行的索引(从0开始计数)，数据将从表头行的下一行开始读取'
  }
}
```

### 属性说明
| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| headerRowIndex | Number | 1 | 表头行索引(从0开始)，数据从此行的下一行开始读取 |
| limitSize | Number | 1 | 文件大小限制(MB) |
| title | String | '导入' | 按钮显示文本 |

## 使用方法

### 1. 在父组件中使用

```vue
<template>
  <div>
    <!-- 默认情况：第2行是表头，第3行开始是数据 -->
    <intelligent-detail-import 
      title="导入智能评测数据"
      :header-row-index="1"
      @on-import="handleImport"
    />
    
    <!-- 自定义情况：第3行是表头，第4行开始是数据 -->
    <intelligent-detail-import 
      title="导入自定义数据"
      :header-row-index="2"
      @on-import="handleImport"
    />
    
    <!-- 无说明行：第1行是表头，第2行开始是数据 -->
    <intelligent-detail-import 
      title="导入简单数据"
      :header-row-index="0"
      @on-import="handleImport"
    />
  </div>
</template>

<script>
import IntelligentDetailImport from '@/components/ExcelImport/intelligentDetailImport.vue'

export default {
  components: {
    IntelligentDetailImport
  },
  methods: {
    handleImport(excelData) {
      console.log('导入的数据:', excelData)
      // 处理导入的数据
      // excelData.header - 表头数组
      // excelData.results - 数据数组
      // excelData.filename - 文件名
    }
  }
}
</script>
```

### 2. 在智能评测页面中使用

```vue
<!-- src/views/intelligent/intelligentevaluation.vue -->
<template>
  <div>
    <!-- 其他内容 -->
    
    <intelligent-detail-import 
      title="批量导入评测数据"
      :header-row-index="1"
      @on-import="handleBatchImport"
    />
  </div>
</template>

<script>
import IntelligentDetailImport from '@/components/ExcelImport/intelligentDetailImport.vue'

export default {
  components: {
    IntelligentDetailImport
  },
  methods: {
    handleBatchImport(excelData) {
      // 处理批量导入的数据
      console.log('批量导入数据:', excelData)
      
      // 可以在这里调用API提交数据
      // this.submitBatchData(excelData.results)
    }
  }
}
</script>
```

## 技术实现

### 1. 表头读取逻辑
```javascript
getHeaderRow(sheet) {
  const headers = []
  const range = XLSX.utils.decode_range(sheet['!ref'])
  let C
  // 使用headerRowIndex确定表头行
  const R = this.headerRowIndex
  for (C = range.s.c; C <= range.e.c; ++C) {
    const cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })]
    let hdr = 'UNKNOWN ' + C
    if (cell && cell.t) hdr = XLSX.utils.format_cell(cell)
    headers.push(hdr)
  }
  return headers
}
```

### 2. 数据读取逻辑
```javascript
// 设置读取数据的起始行
const results = XLSX.utils.sheet_to_json(worksheet, {
  defval: '',
  range: this.headerRowIndex + 1  // 从表头行的下一行开始读取数据
})
```

### 3. 错误处理
```javascript
reader.onload = (e) => {
  try {
    // Excel解析逻辑
    const data = e.target.result
    const workbook = XLSX.read(data, { type: 'array', cellDates: true })
    // ...
  } catch (error) {
    this.loading = false
    ElMessage.error('Excel文件解析失败')
    reject(error)
  }
}

reader.onerror = (error) => {
  this.loading = false
  ElMessage.error('文件读取失败')
  reject(error)
}
```

## 不同场景示例

### 场景1：标准模板 (headerRowIndex = 1)
```
行0: 智能评测数据导入模板说明
行1: *产品名称  位置  类型  材质  品牌  ← 表头行
行2: 智能门锁  客厅  安防  金属  海康威视  ← 数据开始
行3: 智能开关  卧室  照明  塑料  小米
```

### 场景2：多行说明 (headerRowIndex = 2)
```
行0: 智能评测数据导入模板
行1: 请按照以下格式填写数据
行2: *产品名称  位置  类型  材质  品牌  ← 表头行
行3: 智能门锁  客厅  安防  金属  海康威视  ← 数据开始
```

### 场景3：无说明行 (headerRowIndex = 0)
```
行0: *产品名称  位置  类型  材质  品牌  ← 表头行
行1: 智能门锁  客厅  安防  金属  海康威视  ← 数据开始
行2: 智能开关  卧室  照明  塑料  小米
```

## 数据验证

组件会对导入的数据进行验证：

### 1. 产品名称验证
```javascript
<template v-if="item === '*产品名称'" #default="scope">
  <span v-if="keyExists('productList',scope.row[item])">{{ scope.row[item] }}</span>
  <span v-else color="red">{{ scope.row[item] }}</span>
</template>
```

### 2. 位置、类型、材质验证
```javascript
<template v-else-if="item === '位置'" #default="scope">
  <span v-if="keyExists('positionList',scope.row[item])">{{ scope.row[item] }}</span>
  <span v-else color="red">{{ scope.row[item] }}</span>
</template>
```

### 3. 品牌验证 (支持多品牌)
```javascript
<template v-else-if="item === '品牌'" #default="scope">
  <div v-html="brandExists('brandList', scope.row[item])"></div>
</template>
```

## 注意事项

1. **索引从0开始**：headerRowIndex使用0基索引，第1行对应索引0
2. **数据自动偏移**：数据读取会自动从表头行的下一行开始
3. **文件格式**：仅支持 .xlsx、.xls、.csv 格式
4. **错误处理**：包含完整的文件读取和解析错误处理
5. **数据验证**：会验证导入数据的有效性，无效数据会标红显示

## 返回数据格式

```javascript
{
  header: ['*产品名称', '位置', '类型', '材质', '品牌'],
  results: [
    {
      '*产品名称': '智能门锁',
      '位置': '客厅',
      '类型': '安防',
      '材质': '金属',
      '品牌': '海康威视'
    },
    // ... 更多数据
  ],
  filename: 'import_data.xlsx'
}
```

## 最佳实践

1. **模板设计**：建议在Excel第1行添加说明，第2行作为表头
2. **数据验证**：导入前确保基础数据(位置、类型、材质、品牌)已在系统中存在
3. **错误处理**：注意查看标红的无效数据，及时修正
4. **批量操作**：大量数据建议分批导入，避免超时

现在Excel导入组件支持灵活的表头行控制，可以适应不同格式的Excel文件！
