
<template>
  <div class="app-container">
    <div class="bg-[#ffffff] p-[10px] border-rd-10px mb-[10px]">
      <el-tabs v-model="activeModel">
        <el-tab-pane label="热度榜" name="view">
          <View :queryType="'hot'" @custom-event="handleDataFromChild" />
        </el-tab-pane>
        <el-tab-pane label="销量榜" lazy name="sale_count">
          <OtherRank :queryType="'sale_count'" />
        </el-tab-pane>
        <el-tab-pane label="金额榜" lazy name="sale_amount">
          <OtherRank :queryType="'sale_amount'" />
        </el-tab-pane>
        <el-tab-pane label="上新榜" lazy name="upnew">
          <OtherRank :queryType="'upnew'" />
        </el-tab-pane>
        <el-tab-pane label="日照榜" lazy name="sunshine">
          <OtherRank :queryType="'sunshine'" />
        </el-tab-pane>
        <el-tab-pane label="噪音榜" lazy name="noise">
          <OtherRank :queryType="'noise'" />
        </el-tab-pane>
        <el-tab-pane label="景观榜" lazy name="landscape">
          <OtherRank :queryType="'landscape'" />
        </el-tab-pane>
        <el-tab-pane label="配套榜" lazy name="facility">
          <OtherRank :queryType="'facility'" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import View from './components/rank/View.vue'
import OtherRank from './components/rank/OtherRank.vue'

export default {
  name: 'Rank',

  components: {
    View,
    OtherRank
  },
  data() {
    return {
      name: '热度榜',
      activeModel: 'view'
    }
  },
  methods: {
    checkPermission,
    handleDataFromChild(house_id) {
      this.activeModel = 'view'
    }
  },
  mounted() {
    // this.house_id = this.$route.query.id; // John
  }
}
</script>
