import request from '@/utils/request'
// 装修分类管理
const url = '/admin/furnish.DecorationCategory/'

/**
 * 获取所有分类列表
 * @param {array} data 请求数据
 */
export function getAllList(data) {
  return request({
    url: url + 'getAllList',
    method: 'post',
    data
  })
}
/**
 * 分类列表
 * @param {array} params 请求参数
 */
export function list(params) {
  return request({
    url: url + 'list',
    method: 'get',
    params: params
  })
}

/**
 * 创建分类
 * @param {array} data 请求数据
 */
export function create(data) {
  return request({
    url: url + 'create',
    method: 'post',
    data
  })
}

/**
 * 更新分类
 * @param {array} data 请求数据
 */
export function update(data) {
  return request({
    url: url + 'update',
    method: 'post',
    data
  })
}

/**
 * 删除分类
 * @param {array} params 请求参数
 */
export function deleteItem(params) {
  return request({
    url: url + 'delete',
    method: 'get',
    params: params
  })
}

/**
 * 变更分类状态
 * @param {array} data 请求数据
 */
export function changeStatus(data) {
  return request({
    url: url + 'changeStatus',
    method: 'post',
    data
  })
}
