import request from '@/utils/request'

// 装饰分类管理
const url = '/admin/furnish.DecorationCategory/'

/**
 * 获取装饰分类列表
 * @param {Object} params 请求参数
 */
export function getList(params) {
  return request({
    url: url + 'getList',
    method: 'get',
    params: params
  })
}

/**
 * 添加装饰分类
 * @param {Object} data 请求数据
 */
export function add(data) {
  return request({
    url: url + 'add',
    method: 'post',
    data
  })
}

/**
 * 编辑装饰分类
 * @param {Object} data 请求数据
 */
export function edit(data) {
  return request({
    url: url + 'edit',
    method: 'post',
    data
  })
}

/**
 * 删除装饰分类
 * @param {Object} data 请求数据
 */
export function del(data) {
  return request({
    url: url + 'del',
    method: 'post',
    data
  })
}

/**
 * 获取位置列表
 */
export function getPositionList() {
  return request({
    url: url + 'getPositionList',
    method: 'get'
  })
}




/**
 * 获取分类列表
 * @param {Object} params 请求参数
 */
export function getCategoryList(params) {
  return request({
    url: url + 'getCategoryList',
    method: 'get',
    params: params
  })
}






/**
 * 更新发布状态
 * @param {Object} data 请求数据
 */
export function updateReleaseStatus(data) {
  return request({
    url: url + 'updateReleaseStatus',
    method: 'post',
    data
  })
}
