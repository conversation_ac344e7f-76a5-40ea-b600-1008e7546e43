import request from '@/utils/request';

const url = '/admin/basicdata.Subway/';

/**
 * @param {array} params 请求参数
 */
export function list(params) {
    return request({
      url: url + 'list',
      method: 'get',
      params: params
    })
}

/**
 * 修改
 * @param {array} data 请求数据
 */
export function edit(data) {
    return request({
      url: url + 'edit',
      method: 'post',
      data
    })
}

/**
 * 信息
 * @param {array} params 请求参数
 */
export function info(params) {
  return request({
    url: url + 'info',
    method: 'get',
    params: params
  })
}


/**
 * 信息
 * @param {array} params 请求参数
 */
export function getlinesite(params) {
  return request({
    url: url + 'getlineSite',
    method: 'get',
    params: params
  })
}
/**
 * 信息
 * @param {array} params 请求参数
 */
export function getsiteinfo(params) {
  return request({
    url: url + 'getSiteInfo',
    method: 'get',
    params: params
  })
}
