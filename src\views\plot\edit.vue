<template>
  <el-scrollbar native class="wrapper">
    <el-form :model="model" ref="landForm" :rules="landFormRules" label-width="100px">
      <el-row :gutter="10" style="margin-top: 5px; margin-left: 5px; margin-right: 5px">
        <!-- 左侧列 -->
        <el-col :span="11">
          <!-- 宗地信息 Card -->
          <el-card header="宗地信息" shadow="hover" class="card">
            <el-form-item label="宗地名称" prop="plot_name" required>
              <el-input v-model="model.plot_name" placeholder="请输入宗地名称" />
            </el-form-item>

            <el-form-item label="交易状况" prop="transaction_status" required>
              <el-select
                v-model="model.transaction_status"
                placeholder="请选择交易状况"
                class="w-full"
              >
                <el-option
                  v-for="item in params_data.plotTradingStatus"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="宗地编号" prop="parcel_number">
              <el-input v-model="model.parcel_number" placeholder="请输入宗地编号" />
            </el-form-item>

            <el-form-item label="集中供地批次" prop="batch_id" required>
              <el-select v-model="model.batch_id" placeholder="请选择批次" class="w-full" clearable>
                <el-option
                  v-for="item in batchOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="拟出让时间段" prop="launch_period">
              <el-date-picker
                v-model="model.launch_period_start"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择时间"
              />
              —
              <el-date-picker
                v-model="model.launch_period_end"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择时间"
              />
            </el-form-item>

            <!-- 特色标签字段 -->
            <el-form-item label="特色标签" prop="feature">
              <el-col :span="20">
                <el-select
                  v-model="model.feature"
                  class="w-full"
                  multiple
                  clearable
                  filterable
                  :value-key="'id'"
                >
                  <el-option
                    v-for="item in params_data.peitaoFeature"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-col>
              <el-col :span="3" :offset="1">
                <el-button @click="addOption">添加</el-button>
              </el-col>
            </el-form-item>

            <el-form-item label="所属项目" prop="house_name">
              <el-input v-model="model.house_name" class="w-full" disabled="disabled" />
            </el-form-item>
          </el-card>

          <!-- 规划信息 Card -->
          <el-card header="规划信息" shadow="hover" class="card">
            <el-form-item label="出让面积(㎡)" prop="land_transfer_area" required>
              <el-col :span="10">
                <el-input
                  v-model="model.land_transfer_area"
                  type="number"
                  placeholder="请输入出让面积"
                >
                  <template #append>㎡</template>
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="建筑面积(㎡)" prop="build_area" required>
              <el-col :span="10">
                <el-input v-model="model.build_area" type="number" placeholder="建筑面积">
                  <template #append>㎡</template>
                </el-input>
              </el-col>
            </el-form-item>

            <!-- 规划用途组动态添加 -->
            <div v-for="(landUseGroup, index) in model.land_use_groups" :key="'land-use-' + index">
              <el-form-item
                :label="index === 0 ? '规划用途' : ''"
                :prop="'land_use_groups.' + index + '.land_use'"
                :rules="
                  index === 0
                    ? { required: true, message: '请选择规划用途', trigger: 'change' }
                    : {}
                "
              >
                <el-row :gutter="8" class="land-use-row">
                  <!-- 规划用途选择框 -->
                  <el-col :span="19">
                    <el-select
                      v-model="landUseGroup.land_use"
                      placeholder="请选择或输入土地用途"
                      class="w-full"
                      filterable
                      allow-create
                      default-first-option
                    >
                      <el-option
                        v-for="item in params_data.plotPlannedUse"
                        :key="item.value"
                        :label="item.name"
                        :value="item.name"
                      />
                    </el-select>
                  </el-col>

                  <!-- 添加/删除按钮 -->
                  <el-col :span="2" :offset="1">
                    <el-button v-if="index === 0" @click="addLandUseGroup">+</el-button>
                    <el-button v-else @click="removeLandUseGroup(index)">-</el-button>
                  </el-col>
                </el-row>
              </el-form-item>

              <el-form-item label="容积率">
                <el-col :span="10">
                  <el-input v-model="landUseGroup.plot_ratio" placeholder="请输入容积率" />
                </el-col>
              </el-form-item>

              <el-form-item label="限制高度">
                <el-col :span="10">
                  <el-input v-model="landUseGroup.max_height" placeholder="请输入限制高度">
                    <template #append>米</template>
                  </el-input>
                </el-col>
              </el-form-item>
            </div>

            <el-form-item label="现房销售比例" prop="current_sales_ratio">
              <el-col :span="10">
                <el-input v-model="model.current_sales_ratio" placeholder="请输入现房销售比例">
                  <template #append>%</template>
                </el-input>
              </el-col>
              <el-col :span="5" :offset="1">
                <el-checkbox v-model="model.no_current_sales_ratio">无</el-checkbox>
              </el-col>
            </el-form-item>

            <div
              v-for="(construction_areas, index) in model.construction_areas"
              :key="'construction-' + index"
            >
              <el-form-item :label="index === 0 ? '配建类用地规划建面' : ''">
                <el-row :gutter="8" class="construction-row">
                  <!-- 规划输入框 -->
                  <el-col :span="10">
                    <el-input v-model="construction_areas.plan" placeholder="请输入规划" />
                  </el-col>

                  <!-- 连接符 -->
                  <el-col :span="1" class="text-center">
                    <span>-</span>
                  </el-col>

                  <!-- 面积输入框 -->
                  <el-col :span="10">
                    <el-input
                      v-model="construction_areas.area"
                      type="number"
                      placeholder="规划建面"
                    >
                      <template #append>㎡</template>
                    </el-input>
                  </el-col>

                  <!-- 添加/删除按钮 -->
                  <el-col :span="2" :offset="1">
                    <!-- 使用 + 文本代替图标 -->
                    <el-button v-if="index === 0" @click="addConstructionArea">+</el-button>
                    <el-button v-else @click="removeConstructionArea(index)">-</el-button>
                  </el-col>
                </el-row>
              </el-form-item>
            </div>

            <el-form-item label="配套物业总面积" prop="supporting_property_area">
              <el-col :span="10">
                <el-input
                  v-model="model.supporting_property_area"
                  type="number"
                  placeholder="请输入总面积"
                >
                  <template #append>㎡</template>
                </el-input>
              </el-col>
            </el-form-item>

            <el-form-item label="配套规划条件" prop="planning_conditions">
              <el-input
                v-model="model.planning_conditions"
                type="textarea"
                :rows="3"
                placeholder="请输入配套规划条件"
              />
            </el-form-item>

            <el-form-item label="不利因素" prop="disadvantages">
              <FileUploads
                isWatermark="0"
                v-model="model.disadvantages"
                upload-btn="上传PDF"
                file-type="word"
                file-tip="PDF文件"
              />
            </el-form-item>
          </el-card>

          <!-- 推出信息 Card -->
          <el-card header="推出信息" shadow="hover" class="card">
            <el-form-item label="挂牌日期" prop="listing_date">
              <el-col :span="19">
                <el-date-picker
                  v-model="model.listing_date"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择日期"
                  style="width: 100%"
                />
              </el-col>
            </el-form-item>

            <el-form-item label="保证金" prop="deposit">
              <el-col :span="10">
                <el-input v-model="model.deposit" type="number" placeholder="请输入保证金">
                  <template #append>万元</template>
                </el-input>
              </el-col>
            </el-form-item>

            <el-form-item label="起始报价" prop="starting_price">
              <el-col :span="10">
                <el-input v-model="model.starting_price" type="number" placeholder="请输入起始报价">
                  <template #append>万元</template>
                </el-input>
              </el-col>
            </el-form-item>

            <el-form-item label="最小加价幅度" prop="min_price_increment">
              <el-col :span="10">
                <el-input
                  v-model="model.min_price_increment"
                  type="number"
                  placeholder="请输入最小加价幅度"
                >
                  <template #append>万元</template>
                </el-input>
              </el-col>
            </el-form-item>

            <el-form-item label="推出楼面价" prop="offering_area_price">
              <el-col :span="10">
                <el-input
                  v-model="model.offering_area_price"
                  type="number"
                  placeholder="请输入推出楼面价"
                >
                  <template #append>元/平方米</template>
                </el-input>
              </el-col>
              <el-col :span="5" :offset="1">
                <el-button type="primary" @click="calculateOfferingAreaPrice">计算</el-button>
              </el-col>
            </el-form-item>

            <el-form-item label="推出亩单价" prop="offering_unit_price">
              <el-col :span="10">
                <el-input
                  v-model="model.offering_unit_price"
                  type="number"
                  placeholder="请输入推出亩单价"
                >
                  <template #append>万元/亩</template>
                </el-input>
              </el-col>
              <el-col :span="5" :offset="1">
                <el-button type="primary" @click="calculateOfferingUnitPrice">计算</el-button>
              </el-col>
            </el-form-item>

            <el-form-item label="楼面价上限" prop="max_floor_price">
              <el-col :span="10">
                <el-input
                  v-model="model.max_floor_price"
                  type="number"
                  placeholder="请输入楼面价上限"
                >
                  <template #append>元/㎡</template>
                </el-input>
              </el-col>
              <el-col :span="10" :offset="1">
                <el-checkbox v-model="model.no_max_floor_price">无</el-checkbox>
              </el-col>
            </el-form-item>

            <el-form-item label="溢出率上限" prop="max_overflow_rate">
              <el-col :span="10">
                <el-input
                  v-model="model.max_overflow_rate"
                  type="number"
                  placeholder="请输入溢出率"
                >
                  <template #append>%</template>
                </el-input>
              </el-col>
              <el-col :span="10" :offset="1">
                <el-checkbox v-model="model.no_max_overflow_rate">无</el-checkbox>
              </el-col>
            </el-form-item>

            <el-form-item label="限售价" prop="max_selling_price">
              <el-col :span="10">
                <el-input
                  v-model="model.max_selling_price"
                  type="number"
                  placeholder="请输入限售价"
                >
                  <template #append>元/㎡</template>
                </el-input>
              </el-col>
              <el-col :span="10" :offset="1">
                <el-checkbox v-model="model.no_max_selling_price">无</el-checkbox>
              </el-col>
            </el-form-item>

            <el-form-item label="限地价" prop="max_land_price">
              <el-col :span="10">
                <el-input v-model="model.max_land_price" type="number" placeholder="请输入限地价">
                  <template #append>万元</template>
                </el-input>
              </el-col>
              <el-col :span="10" :offset="1">
                <el-checkbox v-model="model.no_max_land_price">不限价</el-checkbox>
              </el-col>
            </el-form-item>

            <el-form-item label="销售指导价" prop="guide_price">
              <el-col :span="10">
                <el-input v-model="model.guide_price" type="number" placeholder="请输入销售指导价">
                  <template #append>元/㎡</template>
                </el-input>
              </el-col>
              <el-col :span="10" :offset="1">
                <el-checkbox v-model="model.no_guide_price">无</el-checkbox>
              </el-col>
            </el-form-item>

            <el-form-item label="出让时间" prop="transfer_time">
              <el-col :span="19">
                <el-date-picker
                  v-model="model.transfer_time"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择时间"
                  style="width: 100%"
                />
              </el-col>
            </el-form-item>
          </el-card>

          <el-card header="竞买规则" shadow="hover" class="card">
            <div v-for="(prefabItem, index) in model.prefab_buildings" :key="'prefab-' + index">
              <el-form-item :label="index === 0 ? '装配式建筑比例' : ''">
                <el-row :gutter="8" class="dynamic-row">
                  <!-- 建筑类型输入框 -->
                  <el-col :span="8">
                    <el-input v-model="prefabItem.building_type" placeholder="建筑类型" />
                  </el-col>

                  <!-- 类型比例输入框 -->
                  <el-col :span="8">
                    <el-input v-model="prefabItem.proportion" type="number" placeholder="类型比例">
                      <template #append>%</template>
                    </el-input>
                  </el-col>

                  <!-- 无选项复选框 -->
                  <el-col :span="3" :offset="1">
                    <el-checkbox v-model="prefabItem.is_none">无</el-checkbox>
                  </el-col>

                  <!-- 添加/删除按钮 -->
                  <el-col :span="2" :offset="1">
                    <el-button v-if="index === 0" @click="addPrefabBuilding">+</el-button>
                    <el-button v-else @click="removePrefabBuilding(index)">-</el-button>
                  </el-col>
                </el-row>
              </el-form-item>
            </div>

            <el-form-item label="成交原则原文" prop="transaction_principles">
              <el-input
                v-model="model.transaction_principles"
                type="textarea"
                :rows="3"
                placeholder="成交原则原文"
              />
            </el-form-item>
            <el-form-item label="付款要求" prop="payment_request">
              <el-input
                v-model="model.payment_request"
                type="textarea"
                :rows="3"
                placeholder="付款要求"
              />
            </el-form-item>
          </el-card>
        </el-col>

        <!-- 右侧列 -->
        <el-col :span="12" :offset="1">
          <el-card header="位置信息" shadow="hover" class="card">
            <el-form-item label="地块地址" prop="land_address">
              <el-col :span="16">
                <el-input v-model="model.land_address" placeholder="输入地块地址" />
              </el-col>
              <el-col :span="7" :offset="1">
                <el-button :loading="searchLoading" @click="handleGetLng">获取坐标</el-button>
              </el-col>
            </el-form-item>

            <el-form-item label="地块坐标">
              <div id="mapContainer" style="width: 100%; height: 250px"></div>
            </el-form-item>

            <el-form-item label="经度" prop="longitude">
              <el-input readonly v-model="model.longitude" type="number" placeholder="输入经度" />
            </el-form-item>

            <el-form-item label="纬度" prop="latitude">
              <el-input readonly v-model="model.latitude" type="number" placeholder="输入纬度" />
            </el-form-item>

            <el-form-item label="区域板块" prop="region_id" class="region_plate">
              <el-col :span="11">
                <el-select
                  v-model="model.region_id"
                  placeholder="请选择区域"
                  @change="changeRegion"
                >
                  <el-option
                    v-for="item in regionData"
                    :key="item.region_id"
                    :label="item.region_name"
                    :value="item.region_id"
                  />
                </el-select>
              </el-col>
              <el-col :span="11" :offset="2">
                <el-select v-model="model.plate_id" placeholder="请选择板块" @change="changePlate">
                  <!-- 为0值添加特殊选项 -->
                  <el-option
                    v-for="item in plateData"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-col>
            </el-form-item>

            <el-form-item label="所属环线" prop="loop_line">
              <el-radio-group v-model="model.loop_line">
                <el-radio
                  v-for="(value, key) in params_data.houseLoopLine"
                  :key="key"
                  :label="String(key)"
                >
                  {{ value }}
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="四至" prop="surroundings">
              <el-input v-model="model.surroundings" placeholder="请输入四至信息" />
            </el-form-item>
          </el-card>

          <!-- 成交信息 Card -->
          <el-card header="成交信息" shadow="hover" class="card">
            <!-- 竞得企业动态添加 -->
            <div v-for="(winner, index) in model.winners" :key="'winner-' + index">
              <el-form-item :label="index === 0 ? '竞得企业' : ''">
                <el-row :gutter="8" class="dynamic-row">
                  <el-col :span="19">
                    <el-input v-model="winner.name" placeholder="请输入竞得企业" />
                  </el-col>
                  <el-col :span="2" :offset="1">
                    <el-button v-if="index === 0" @click="addWinner">+</el-button>
                    <el-button v-else @click="removeWinner(index)">-</el-button>
                  </el-col>
                </el-row>
              </el-form-item>
            </div>

            <!-- 品牌名称字段 -->
            <el-form-item label="品牌名称" prop="brand">
              <el-select
                v-model="model.brand"
                class="w-full"
                multiple
                clearable
                filterable
                @change="changeBrands"
              >
                <el-option
                  v-for="item in params_data.brands"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="竞拍企业家数">
              <el-col :span="19">
                <el-input
                  v-model="model.bidding_companies_count"
                  placeholder="请输入竞拍企业家数"
                />
              </el-col>
            </el-form-item>

            <!-- 竞拍企业明细动态添加 -->
            <div v-for="(bidder, index) in model.bidders" :key="'bidder-' + index">
              <el-form-item :label="index === 0 ? '竞拍企业明细' : ''">
                <el-row :gutter="8" class="dynamic-row">
                  <el-col :span="19">
                    <el-input v-model="bidder.detail" placeholder="请输入企业明细" />
                  </el-col>
                  <el-col :span="2" :offset="1">
                    <el-button v-if="index === 0" @click="addBidder">+</el-button>
                    <el-button v-else @click="removeBidder(index)">-</el-button>
                  </el-col>
                </el-row>
              </el-form-item>
            </div>

            <el-form-item label="联合拿地">
              <el-radio-group v-model="model.joint_acquisition">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>

            <div v-for="(equity, index) in model.equities" :key="'equity-' + index">
              <el-form-item :label="index === 0 ? '股权占比' : ''">
                <el-row :gutter="8" class="dynamic-row">
                  <el-col :span="10">
                    <el-input v-model="equity.company" placeholder="请输入竞得企业" />
                  </el-col>
                  <el-col :span="8" :offset="1">
                    <el-input v-model="equity.percentage" placeholder="请输入占比">
                      <template #append>%</template>
                    </el-input>
                  </el-col>
                  <el-col :span="2" :offset="1">
                    <el-button v-if="index === 0" @click="addEquity">+</el-button>
                    <el-button v-else @click="removeEquity(index)">-</el-button>
                  </el-col>
                </el-row>
              </el-form-item>
            </div>

            <el-form-item label="成交日期" prop="transaction_date">
              <el-col :span="19">
                <el-date-picker
                  v-model="model.transaction_date"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择时间"
                  style="width: 100%"
                />
              </el-col>
            </el-form-item>

            <el-form-item label="成交价" prop="transaction_price">
              <el-col :span="10">
                <el-input
                  v-model="model.transaction_price"
                  type="number"
                  placeholder="请输入成交价"
                >
                  <template #append>万元</template>
                </el-input>
              </el-col>
            </el-form-item>

            <el-form-item label="成交楼面价" prop="transaction_floor_price">
              <el-col :span="10">
                <el-input
                  v-model="model.transaction_floor_price"
                  type="number"
                  placeholder="请输入成交楼面价"
                >
                  <template #append>元/平米</template>
                </el-input>
              </el-col>
              <el-col :span="5" :offset="1">
                <el-button type="primary" @click="calculateTransactionFloorPrice">计算</el-button>
              </el-col>
            </el-form-item>

            <el-form-item label="溢价率" prop="premium_rate">
              <el-col :span="10">
                <el-input v-model="model.premium_rate" type="number" placeholder="请输入溢价率">
                  <template #append>%</template>
                </el-input>
              </el-col>
            </el-form-item>
          </el-card>

          <el-card header="开发进度" shadow="hover" class="card">
            <el-form-item label="土地成交日期" prop="land_transaction_date">
              <el-col :span="19">
                <el-date-picker
                  v-model="model.land_transaction_date"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择日期"
                  style="width: 100%"
                />
              </el-col>
            </el-form-item>
            <el-form-item label="规划公示日期" prop="planning_publication_date">
              <el-col :span="19">
                <el-date-picker
                  v-model="model.planning_publication_date"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择日期"
                  style="width: 100%"
                />
              </el-col>
            </el-form-item>
            <el-form-item label="首个开工日期" prop="first_start_date">
              <el-col :span="19">
                <el-date-picker
                  v-model="model.first_start_date"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择日期"
                  style="width: 100%"
                />
              </el-col>
            </el-form-item>
            <el-form-item label="首个销售证件" prop="first_sales_document_date">
              <el-col :span="19">
                <el-date-picker
                  v-model="model.first_sales_document_date"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择日期"
                  style="width: 100%"
                />
              </el-col>
            </el-form-item>
            <el-form-item label="拿地-预售周期(天)" prop="pre_sale_cycle">
              <el-col :span="10">
                <el-input
                  v-model="model.pre_sale_cycle"
                  type="number"
                  placeholder="拿地-预售周期(天)"
                >
                  <template #append>天</template>
                </el-input>
              </el-col>
              <el-col :span="5" :offset="1">
                <el-button type="primary" @click="calculatePreSaleCycle">计算</el-button>
              </el-col>
            </el-form-item>
          </el-card>

          <!-- 实地照片 Card -->
          <el-card header="实地照片" shadow="hover" class="card">
            <el-form-item label="图片文件">
              <ImgUploadsHouseType
                v-model="model.progress_photos"
                upload-btn="上传照片"
                file-type="image"
                file-tip=""
                :isWatermark="1"
                :source="model.source || 2"
              />
            </el-form-item>
            <el-form-item label="是否前台展示">
              <el-radio-group v-model="model.progress_photos_status" :default-value="1">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-card>
          <el-card header="位置示意图" shadow="hover" class="card">
            <el-form-item label="图片文件">
              <ImgUploadsHouseNews
                v-model="model.location_pic"
                upload-btn="上传照片"
                file-type="image"
                file-tip=""
                :isWatermark="1"
                singleUpload
                :source="model.source || 2"
              />
            </el-form-item>
          </el-card>
        </el-col>
      </el-row>

      <div class="sub-btn">
        <el-button :loading="loading" title="返回" @click="refresh()" style="margin-right: 10px"
          >返回
        </el-button>
        <el-button :loading="loading" type="primary" @click="submit()" style="margin-right: 10px"
          >提交
        </el-button>
      </div>
    </el-form>
    <el-dialog
      v-model="addoptionDialog"
      title="添加特色标签"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      top="20vh"
      width="20%"
    >
      <el-form ref="addoptionRef" label-width="120px">
        <el-form-item label="标签名称">
          <el-input v-model="tagmodel.name" placeholder="输入标签名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button :loading="loading" @click="addoptionCancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="addoptionSubmit">提交</el-button>
      </template>
    </el-dialog>
  </el-scrollbar>
</template>

<script>
import request from '@/utils/request.js'
import { jsonp } from '@/utils/jsonp.js'
import { ElMessage } from 'element-plus'
import { useTagsViewStore } from '@/store/modules/tagsView'
import TMap from 'TMap'
import { tagsbygroup, addTag } from '@/api/project/tag'
import { list } from '@/api/project/plate.js'

let mapContainer = null
let multiMarker = null
let markerGeo = null

// Keep these variables inside the component to avoid shared state issues
export default {
  name: 'LandInformation',
  props: {
    landId: {
      type: [String, Number],
      default: 0
    }
  },
  data() {
    return {
      searchLoading: false,
      loading: false,
      isMapEvent: false,
      mapContainer: null,
      multiMarker: null,
      markerGeo: null,
      mapInitialized: false,
      addoptionDialog: false,
      tagmodel: {
        group_id: 16,
        name: ''
      },
      tagsBasics: [], // 存储基础标签数据
      model: {
        id: 0,
        // loop_line: '',
        winners: [{ name: '' }],
        bidders: [{ detail: '' }],
        equities: [{ company: '', percentage: '' }],
        land_use_groups: [{ land_use: '', plot_ratio: '', max_height: '' }],
        construction_areas: [{ plan: '', area: '' }],
        brand: [], // 添加品牌名称字段
        develop_brand: '', // 添加存储品牌名称字符串的字段
        feature: [], // 添加特色标签字段
        // region_id: 0,
        // region_name: '',
        // plate_id: 0,
        // sector_name: '',
        // 基本字段
        // plot_name: '',
        // transaction_status: '',
        // parcel_number: '',
        // batch_id: '',
        // launch_period_start: '',
        // launch_period_end: '',
        // project_id: '',
        // land_address: '',
        // longitude: '',
        // latitude: '',
        // surroundings: '',
        // land_transfer_area: '',
        // current_sales_ratio: '',
        // supporting_property_area: '',
        // planning_conditions: '',

        // 成交信息
        // bidding_companies_count: '',
        joint_acquisition: false,
        // transaction_date: '',
        // transaction_price: '',
        // transaction_floor_price: '',
        // premium_rate: '',

        // 推出信息
        // listing_date: '',
        // deposit: '',
        // starting_price: '',
        // min_price_increment: '',
        // offering_area_price: '',
        // offering_floor_price: '',
        // max_selling_price: '',
        no_max_floor_price: false,
        no_max_overflow_rate: false,
        no_current_sales_ratio: false,
        no_max_selling_price: false,
        // max_land_price: '',
        no_max_land_price: false,
        // guide_price: '',
        no_guide_price: false,
        // transfer_time: '',
        prefab_buildings: [{ building_type: '', proportion: '', is_none: false }],
        // 照片信息
        progress_photos: [],
        // location_pic: '',
        disadvantages: []
      },
      modelDetails: null, // 按需加载详细信息
      batchOptions: [],
      params_data: {
        plotTradingStatus: [],
        plotPlannedUse: [],
        landUse: {},
        houseLoopLine: {}
      },
      landFormRules: {
        plot_name: [{ required: true, message: '请输入宗地名称', trigger: 'blur' }],
        transaction_status: [{ required: true, message: '请选择交易状况', trigger: 'change' }],
        batch_id: [{ required: true, message: '请选择集中供地批次', trigger: 'change' }],
        land_address: [{ required: true, message: '请输入地块地址', trigger: 'change' }],
        land_transfer_area: [
          { required: true, message: '请输入出让面积', trigger: 'blur' },
          { validator: this.validatePositiveNumberIfProvided, trigger: 'blur' },
          { max: 16, message: '出让面积不能超过16个字符', trigger: 'blur' }
        ],
        build_area: [
          { required: true, message: '请输入建筑面积', trigger: 'blur' },
          { validator: this.validatePositiveNumberIfProvided, trigger: 'blur' },
          { max: 16, message: '建筑面积不能超过16个字符', trigger: 'blur' }
        ],
        'land_use_groups.0.land_use': [
          { required: true, message: '请选择规划用途', trigger: 'change' }
        ],
        supporting_property_area: [
          { validator: this.validatePositiveNumberIfProvided, trigger: 'blur' },
          { max: 16, message: '配套物业总面积不能超过16个字符', trigger: 'blur' }
        ],
        building_proportion: [{ validator: this.validatePercentageIfProvided, trigger: 'blur' }],
        transaction_price: [
          { validator: this.validatePositiveNumberIfProvided, trigger: 'blur' },
          { max: 16, message: '成交价不能超过16个字符', trigger: 'blur' }
        ],
        // transaction_floor_price: [
        //   { validator: this.validatePositiveNumberIfProvided, trigger: 'blur' },
        //   { max: 16, message: '成交楼面价不能超过16个字符', trigger: 'blur' }
        // ],
        // premium_rate: [{ validator: this.validatePercentageIfProvided, trigger: 'blur' }],
        deposit: [
          { validator: this.validatePositiveNumberIfProvided, trigger: 'blur' },
          { max: 16, message: '保证金不能超过16个字符', trigger: 'blur' }
        ],
        starting_price: [
          { validator: this.validatePositiveNumberIfProvided, trigger: 'blur' },
          { max: 16, message: '起始报价不能超过16个字符', trigger: 'blur' }
        ],
        min_price_increment: [
          { validator: this.validatePositiveNumberIfProvided, trigger: 'blur' },
          { max: 16, message: '最小加价幅度不能超过16个字符', trigger: 'blur' }
        ],
        // offering_area_price: [
        //   { validator: this.validatePositiveNumberIfProvided, trigger: 'blur' },
        //   { max: 16, message: '推出楼面价不能超过16个字符', trigger: 'blur' }
        // ],
        // offering_unit_price: [
        //   { validator: this.validatePositiveNumberIfProvided, trigger: 'blur' },
        //   { max: 16, message: '推出亩单价不能超过16个字符', trigger: 'blur' }
        // ],
        max_floor_price: [
          { validator: this.validatePositiveNumberIfProvided, trigger: 'blur' },
          { max: 16, message: '楼面价上限不能超过16个字符', trigger: 'blur' }
        ],
        max_overflow_rate: [
          { validator: this.validatePercentageIfProvided, trigger: 'blur' },
          { max: 16, message: '溢出率上限不能超过16个字符', trigger: 'blur' }
        ],
        max_selling_price: [{ validator: this.validatePositiveNumberIfProvided, trigger: 'blur' }],
        max_land_price: [{ validator: this.validatePositiveNumberIfProvided, trigger: 'blur' }],
        guide_price: [{ validator: this.validatePositiveNumberIfProvided, trigger: 'blur' }],
        current_sales_ratio: [{ validator: this.validatePercentageIfProvided, trigger: 'blur' }]
      },
      regionData: [],
      plateData: [],
      source: 2,
      // Track component mounting state
      isMounted: false
    }
  },

  computed: {
    actualLandId() {
      const routeId = this.$route.query.id
      return routeId ? Number(routeId) : Number(this.landId)
    },
    tagsViewStore() {
      return useTagsViewStore()
    }
  },

  watch: {
    '$route.query.id': {
      handler(newId) {
        if (newId && this.isMounted) {
          this.fetchData()
        }
      },
      immediate: false // We'll call fetchData directly in mounted
    }
  },

  mounted() {
    this.isMounted = true
    this.initComponent()
  },

  beforeUnmount() {
    if (mapContainer) mapContainer.destroy()
    if (multiMarker) multiMarker.remove(['center'])
    mapContainer = null
    multiMarker = null
    markerGeo = null
  },

  methods: {
    // 获取基础标签数据
    basicTags() {
      tagsbygroup({ groupid: 16 })
        .then((res) => {
          this.tagsBasics = res.data
        })
        .catch(() => {})
    },

    // 添加标签对话框
    addOption() {
      this.addoptionDialog = true
    },

    // 取消添加标签
    addoptionCancel() {
      this.addoptionDialog = false
      this.tagmodel.name = ''
    },

    // 提交添加标签
    addoptionSubmit() {
      if (this.tagmodel.name === '') {
        ElMessage.error('请输入标签名称')
        return
      }

      var is_exist = 0
      this.tagsBasics.forEach((item) => {
        if (item.name === this.tagmodel.name) {
          is_exist = 1
          return
        }
      })

      if (is_exist === 1) {
        ElMessage.error('标签已经存在')
        return
      }

      addTag(this.tagmodel)
        .then((res) => {
          if (res && res.data) {
            var mm = { id: res.data, name: this.tagmodel.name }
            this.params_data['peitaoFeature'].push(mm)
            this.model.feature.push(res.data)
            ElMessage.success('添加标签成功')
            this.addoptionCancel()
          } else {
            ElMessage.error('标签添加失败：无返回数据')
          }
        })
        .catch((error) => {
          console.error('标签添加失败', error)
          ElMessage.error('添加标签失败')
          this.addoptionDialog = false
        })

      this.addoptionDialog = false
    },
    changeBrands(val) {
      var m = []
      this.params_data.brands.find((item) => {
        if (val.includes(item.id)) {
          m.push(item.name)
        }
      })
      this.model.develop_brand = m.join(',')
    },
    validatePositiveNumberIfProvided(rule, value, callback) {
      if (value === undefined || value === null || value === '') {
        callback()
      } else if (isNaN(value) || parseFloat(value) <= 0) {
        callback(new Error('请输入大于0的数值'))
      } else {
        callback()
      }
    },

    validatePercentageIfProvided(rule, value, callback) {
      if (value === undefined || value === null || value === '') {
        callback()
      } else if (isNaN(value) || parseFloat(value) <= 0) {
        callback(new Error('请输入大于0的数值'))
      } else if (parseFloat(value) > 100) {
        callback(new Error('比例不能超过100'))
      } else {
        callback()
      }
    },

    // 添加推出楼面价计算方法
    calculateOfferingAreaPrice() {
      // 检查必要的输入字段
      if (!this.model.starting_price || !this.model.build_area) {
        ElMessage.warning('请先填写起始报价和建筑面积')
        return
      }

      try {
        // 转换为数字类型确保计算准确
        const startingPrice = Number(this.model.starting_price)
        const buildArea = Number(this.model.build_area)

        // 验证输入值
        if (isNaN(startingPrice) || isNaN(buildArea) || buildArea <= 0) {
          ElMessage.warning('请输入有效的起始报价和建筑面积值')
          return
        }

        // 应用公式: 起始报价/建筑面积，并四舍五入取整
        const result = Math.round((startingPrice / buildArea) * 10000)

        // 设置结果到模型
        this.model.offering_area_price = result

        ElMessage.success('推出楼面价计算完成')
      } catch (error) {
        console.error('计算推出楼面价时出错:', error)
        ElMessage.error('计算失败，请检查输入值')
      }
    },

    // 添加成交楼面价计算方法
    calculateTransactionFloorPrice() {
      // 检查必要的输入字段
      if (!this.model.transaction_price || !this.model.build_area) {
        ElMessage.warning('请先填写成交价和建筑面积')
        return
      }

      try {
        // 转换为数字类型确保计算准确
        const transactionPrice = Number(this.model.transaction_price)
        const buildArea = Number(this.model.build_area)

        // 验证输入值
        if (isNaN(transactionPrice) || isNaN(buildArea) || buildArea <= 0) {
          ElMessage.warning('请输入有效的成交价和建筑面积值')
          return
        }

        // 应用公式: 成交价/建筑面积，并四舍五入取整
        const result = Math.round((transactionPrice / buildArea) * 10000)

        // 设置结果到模型
        this.model.transaction_floor_price = result

        ElMessage.success('成交楼面价计算完成')
      } catch (error) {
        console.error('计算成交楼面价时出错:', error)
        ElMessage.error('计算失败，请检查输入值')
      }
    },

    // 添加拿地-预售周期计算方法
    calculatePreSaleCycle() {
      // 检查必要的输入字段
      if (!this.model.first_sales_document_date || !this.model.land_transaction_date) {
        ElMessage.warning('请先填写首个销售证日期和成交日期')
        return
      }

      try {
        // 解析日期字符串为Date对象
        const salesDocDate = new Date(this.model.first_sales_document_date)
        const transactionDate = new Date(this.model.land_transaction_date)

        // 验证日期有效性
        if (isNaN(salesDocDate.getTime()) || isNaN(transactionDate.getTime())) {
          ElMessage.warning('日期格式无效，请使用YYYY-MM-DD格式')
          return
        }

        // 计算日期差值（毫秒转换为天）
        const diffTime = salesDocDate.getTime() - transactionDate.getTime()
        const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24))

        // 验证结果是否为正数
        if (diffDays < 0) {
          ElMessage.warning('首个销售证日期应晚于成交日期')
          return
        }

        // 设置结果到模型
        this.model.pre_sale_cycle = diffDays

        ElMessage.success('拿地-预售周期计算完成')
      } catch (error) {
        console.error('计算拿地-预售周期时出错:', error)
        ElMessage.error('计算失败，请检查日期格式')
      }
    },

    // 添加推出楼面价计算方法
    calculateOfferingFloorPrice() {
      // 检查必要的输入字段
      if (!this.model.starting_price || !this.model.build_area) {
        ElMessage.warning('请先填写起始报价和建筑面积')
        return
      }

      try {
        // 转换为数字类型确保计算准确
        const startingPrice = Number(this.model.starting_price)
        const buildArea = Number(this.model.build_area)

        // 验证输入值
        if (isNaN(startingPrice) || isNaN(buildArea) || buildArea <= 0) {
          ElMessage.warning('请输入有效的起始报价和建筑面积值')
          return
        }

        // 应用公式: 起始报价/建筑面积，并四舍五入取整
        const result = Math.round(startingPrice / buildArea)

        // 设置结果到模型
        this.model.offering_floor_price = result

        ElMessage.success('推出楼面价计算完成')
      } catch (error) {
        console.error('计算推出楼面价时出错:', error)
        ElMessage.error('计算失败，请检查输入值')
      }
    },

    // 添加推出亩单价计算方法
    calculateOfferingUnitPrice() {
      // 检查必要的输入字段
      if (!this.model.starting_price || !this.model.land_transfer_area) {
        ElMessage.warning('请先填写起始报价和出让面积')
        return
      }

      try {
        // 转换为数字类型确保计算准确
        const startingPrice = Number(this.model.starting_price)
        const transferArea = Number(this.model.land_transfer_area)

        // 验证输入值
        if (isNaN(startingPrice) || isNaN(transferArea) || transferArea <= 0) {
          ElMessage.warning('请输入有效的起始报价和出让面积值')
          return
        }

        // 应用公式: 起始报价/出让面积/15*10000，并四舍五入取整
        const result = Math.round((startingPrice / transferArea / 15) * 10000)

        // 设置结果到模型
        this.model.offering_unit_price = result

        ElMessage.success('推出亩单价计算完成')
      } catch (error) {
        console.error('计算推出亩单价时出错:', error)
        ElMessage.error('计算失败，请检查输入值')
      }
    },

    async initComponent() {
      try {
        this.isMounted = true
        await this.loadParams()
        this.initMap()
        await this.getBatchList()

        if (this.actualLandId > 0) {
          await this.fetchData() // 然后加载数据
        }
        this.basicTags() // 添加获取基础标签的方法调用

        // Setup watch for land_use_groups length with proper context
        this.$watch('model.land_use_groups.length', this.updateLandUseRules)
      } catch (error) {
        console.error('组件初始化失败:', error)
        ElMessage.error('页面初始化失败')
      }
    },

    updateLandUseRules(newLen) {
      if (!this.isMounted) return

      // Create a new rules object with all existing rules
      const newRules = { ...this.landFormRules }

      // Add rules for each index
      for (let i = 0; i < newLen; i++) {
        const rulePath = `land_use_groups.${i}.land_use`
        newRules[rulePath] = i === 0
        i === 0 ? [{ required: true, message: '请选择规划用途', trigger: 'change' }] : []
      }

      // Replace the rules object
      this.landFormRules = newRules

      // Clear validation if form instance exists
      if (this.$refs.landForm) {
        this.$refs.landForm.clearValidate()
      }
    },

    // 获取地块详情
    async fetchData() {
      if (!this.actualLandId || !this.isMounted) {
        return
      }

      // 取消之前的请求
      if (this.abortController) {
        this.abortController.abort()
      }
      this.abortController = new AbortController()

      try {
        this.loading = true

        const res = await request({
          url: '/admin/house.Plot/info',
          method: 'get',
          params: { id: this.actualLandId }
        })

        // 组件已卸载则直接返回
        if (!this.isMounted) return

        if (res.code === 200 && res.data) {
          // 处理基础数据
          const data = res.data

          // 保存原始的plate_id，以便在加载区域后恢复
          const originalPlateId = data.plate_id ? Number(data.plate_id) : null

          if (data.region_id === 0) {
            this.model.region_id = '' // 或 ''
          } else {
            this.model.region_id = Number(data.region_id)
          }

          if (data.plate_id === 0) {
            this.model.plate_id = ''
          } else {
            this.model.plate_id = Number(data.plate_id)
          }
          if (data.region_id !== undefined && data.region_id !== null && this.isMounted) {
            // 先加载区域数据，然后在回调中恢复plate_id
            await this.changeRegion(Number(data.region_id))

            // 在区域加载完成后，恢复原始的plate_id
            if (originalPlateId) {
              this.model.plate_id = originalPlateId
            }
          }

          if (data.latitude && data.longitude && this.mapInitialized) {
            this.model.land_address = data.land_address || '地块位置'
            this.setCenter(data.latitude, data.longitude)
            this.setMarker(data.latitude, data.longitude) // Pass coordinates directly
          }

          // 确保必要的数组字段存在
          if (!Array.isArray(data.winners)) {
            data.winners = []
          }
          if (!Array.isArray(data.bidders)) {
            data.bidders = []
          }
          if (!Array.isArray(data.equities)) {
            data.equities = []
          }
          if (!Array.isArray(data.land_use_groups)) {
            data.land_use_groups = [{ land_use: '', plot_ratio: '', max_height: '' }]
          }
          if (!Array.isArray(data.construction_areas)) {
            data.construction_areas = []
          }

          if (!Array.isArray(data.prefab_buildings) || data.prefab_buildings.length === 0) {
            if (data.building_proportion) {
              data.prefab_buildings = [
                {
                  building_type: '整体比例',
                  proportion: data.building_proportion
                }
              ]
            } else {
              data.prefab_buildings = [{ building_type: '', proportion: '' }]
            }
          }

          // 处理land_use_groups - 确保都为字符串类型
          if (data.land_use_groups && data.land_use_groups.length) {
            data.land_use_groups = data.land_use_groups.map((item) => ({
              land_use: item.land_use !== undefined ? String(item.land_use) : '',
              plot_ratio: item.plot_ratio || '',
              max_height: item.max_height || ''
            }))
          } else {
            data.land_use_groups = [{ land_use: '', plot_ratio: '', max_height: '' }]
          }

          // 确保loop_line为字符串类型
          const loop_line = data.loop_line !== undefined ? String(data.loop_line) : '0'

          // 将 batch_id 为 0 的情况处理为空字符串
          const batch_id = data.batch_id === 0 ? '' : data.batch_id

          // Create a new object to avoid reactivity issues
          this.model = {
            ...this.model,
            ...data,
            // 确保将字符串转为数字或保持类型一致
            region_id:
              data.region_id !== undefined && data.region_id !== null ? Number(data.region_id) : 0,
            plate_id:
              data.plate_id !== undefined && data.plate_id !== null ? Number(data.plate_id) : 0,
            // 处理日期字段
            launch_period_start: data.launch_period_start || '',
            launch_period_end: data.launch_period_end || '',
            transaction_date: data.transaction_date || '',
            listing_date: data.listing_date || '',
            transfer_time: data.transfer_time || '',
            // 处理数组字段 - 确保至少有一个空项
            winners: data.winners?.length ? data.winners : [{ name: '' }],
            bidders: data.bidders?.length ? data.bidders : [{ detail: '' }],
            equities: data.equities?.length ? data.equities : [{ company: '', percentage: '' }],
            construction_areas: data.construction_areas?.length
              ? data.construction_areas
              : [{ plan: '', area: '' }],
            land_use_groups: data.land_use_groups?.length
              ? data.land_use_groups
              : [{ land_use: '', plot_ratio: '', max_height: '' }],
            loop_line: loop_line,
            batch_id: batch_id
          }

          // 处理照片和PDF文件
          this.processPhotoData(data)

          // Run in nextTick to ensure DOM is updated
          this.$nextTick(() => {
            // Check if component is still mounted
            if (!this.isMounted) return

            // 确保地图已初始化后再设置坐标
            if (data.latitude && data.longitude && this.mapInitialized) {
              this.setCenter(data.latitude, data.longitude)
              this.setMarker()
            }
          })

          // 更新区域数据
          if (data.region_id !== undefined && data.region_id !== null && this.isMounted) {
            await this.changeRegion(Number(data.region_id))
          }
        } else {
          ElMessage.error(res.msg || '获取地块信息失败')
        }
      } catch (error) {
        console.error('获取地块信息失败:', error)
        if (this.isMounted) {
          ElMessage.error('获取地块信息失败')
        }
      } finally {
        if (this.isMounted) {
          this.loading = false
        }
      }
    },

    // 处理照片和文件数据
    processPhotoData(data) {
      if (!this.isMounted) return

      if (data.disadvantages) {
        let disadvantages = []
        if (Array.isArray(data.disadvantages)) {
          // 如果已经是数组，确保每个元素有正确的属性
          disadvantages = data.disadvantages.map((item) => {
            if (typeof item === 'string') {
              let fullFileName = item.split('/').pop()

              // 处理带有查询参数的情况
              if (item.includes('?name=')) {
                const nameParam = item.split('?name=')[1]
                fullFileName = decodeURIComponent(nameParam)
              }

              // 分离文件名和扩展名
              const lastDotIndex = fullFileName.lastIndexOf('.')
              let fileName = fullFileName
              let fileExt = ''

              if (lastDotIndex !== -1) {
                fileName = fullFileName.substring(0, lastDotIndex)
                fileExt = fullFileName.substring(lastDotIndex + 1)
              }

              return {
                file_url: item.split('?')[0],
                name: fullFileName, // 完整文件名(含扩展名)
                file_name: fileName, // 不含扩展名的部分
                file_ext: fileExt // 扩展名部分
              }
            }
            return item
          })
        }
        this.model.disadvantages = disadvantages
      } else {
        this.model.disadvantages = []
      }

      const processFiles = (source, defaultVal = []) => {
        if (!source) return defaultVal

        if (typeof source === 'string') {
          return source.split(',').map((url) => ({
            file_url: url,
            name: url.split('/').pop()
          }))
        }

        if (Array.isArray(source)) {
          return source.map((item) => {
            if (typeof item === 'string') {
              return { file_url: item, name: item.split('/').pop() }
            }
            return item
          })
        }

        return defaultVal
      }

      this.model.progress_photos = processFiles(data.progress_photos)

      // 处理位置示意图（可能是单个文件）
      this.model.location_pic = processFiles(data.location_pic)
    },
    addPrefabBuilding() {
      this.model.prefab_buildings.push({ building_type: '', proportion: '', is_none: false })
    },

    removePrefabBuilding(index) {
      if (index > 0) {
        this.model.prefab_buildings.splice(index, 1)
      }
    },
    // 动态组件管理方法
    addWinner() {
      this.model.winners.push({ name: '' })
    },
    removeWinner(index) {
      if (index > 0) {
        this.model.winners.splice(index, 1)
      }
    },
    addBidder() {
      this.model.bidders.push({ detail: '' })
    },
    removeBidder(index) {
      if (index > 0) {
        this.model.bidders.splice(index, 1)
      }
    },
    addEquity() {
      this.model.equities.push({ company: '', percentage: '' })
    },
    removeEquity(index) {
      if (index > 0) {
        this.model.equities.splice(index, 1)
      }
    },
    addLandUseGroup() {
      this.model.land_use_groups.push({
        land_use: '',
        plot_ratio: '',
        max_height: ''
      })
    },
    removeLandUseGroup(index) {
      if (index > 0) {
        this.model.land_use_groups.splice(index, 1)
      }
    },
    addConstructionArea() {
      this.model.construction_areas.push({ plan: '', area: '' })
    },
    removeConstructionArea(index) {
      if (index > 0) {
        this.model.construction_areas.splice(index, 1)
      }
    },

    // 数据加载方法
    async loadParams() {
      if (!this.isMounted) return

      try {
        const res = await request({
          url: '/admin/house.Plot/dict',
          method: 'get'
        })

        if (!this.isMounted) return

        if (res.code === 200) {
          // 处理交易状况数据
          let plotTradingStatus = []
          if (Array.isArray(res.data.plotTradingStatus)) {
            plotTradingStatus = res.data.plotTradingStatus.map((item) => ({
              value: item.val,
              name: item.name
            }))
          } else {
            for (const key in res.data.plotTradingStatus) {
              plotTradingStatus.push({
                value: key,
                name: res.data.plotTradingStatus[key]
              })
            }
          }

          if (!res.data.peitaoFeature) {
            res.data.peitaoFeature = []
          }

          // 处理规划用途数据
          const plotPlannedUse = Array.isArray(res.data.plotPlannedUse)
            ? res.data.plotPlannedUse
            : []

          this.params_data = {
            ...this.params_data,
            plotTradingStatus,
            plotPlannedUse,
            brands: res.data.brands || [],
            peitaoFeature: res.data.peitaoFeature || [] // 确保peitaoFeature字段被正确赋值
          }

          // 处理环线数据
          if (Array.isArray(res.data.houseLoopLine)) {
            const loopLineObj = {}
            res.data.houseLoopLine.forEach((item, index) => {
              loopLineObj[index + 1] = item
            })
            this.params_data.houseLoopLine = loopLineObj
          } else if (res.data.houseLoopLine) {
            this.params_data.houseLoopLine = res.data.houseLoopLine
          }

          // 加载区域数据
          if (res.data.region && Array.isArray(res.data.region)) {
            this.regionData = res.data.region
          }
        } else {
          ElMessage.error(res.msg || '获取参数失败')
        }
      } catch (error) {
        console.error('加载参数失败', error)
        if (this.isMounted) {
          ElMessage.error('加载参数失败')
        }
      }
    },

    async getBatchList() {
      if (!this.isMounted) return

      try {
        const res = await request({
          url: '/admin/house.PlotBatch/list',
          method: 'get',
          params: { page: 1, limit: 20 }
        })

        if (!this.isMounted) return

        if (res.code === 200 && res.data.list) {
          this.batchOptions = res.data.list.map((item) => ({
            value: item.id ?? '',
            label: `${item.year}-${item.batch}`
          }))

          if (
            this.batchOptions.length > 0 &&
            (!this.model.batch_id || this.model.batch_id === 0 || this.model.batch_id === '') &&
            this.actualLandId <= 0
          ) {
            this.model.batch_id = this.batchOptions[0].value
          }
        } else {
          ElMessage.error(res.msg || '获取批次列表失败')
        }
      } catch (error) {
        console.error('获取批次列表失败', error)
        if (this.isMounted) {
          ElMessage.error('获取批次列表失败')
        }
      }
    },
    initMap() {
      const mapElement = document.getElementById('mapContainer')
      if (!mapElement) {
        console.error('Map container not found')
        return
      }
      mapContainer = new TMap.Map(mapElement, {
        zoom: 13
      })
      console.log('Map initialized:', mapContainer)
      this.mapInitialized = true
    },
    setCenter(lat, lng) {
      if (!mapContainer) return
      console.log('Setting center to:', lat, lng)
      const center = new TMap.LatLng(lat, lng)
      mapContainer.setCenter(center)
      console.log('Map center:', mapContainer.getCenter())
    },
    setMarker(lat, lng) {
      if (!mapContainer) {
        console.error('Cannot set marker: Map not initialized')
        return
      }
      let position
      if (lat !== undefined && lng !== undefined && !isNaN(lat) && !isNaN(lng)) {
        position = new TMap.LatLng(lat, lng)
      } else {
        position = mapContainer.getCenter()
      }

      if (!markerGeo) {
        markerGeo = {
          id: 'center',
          position: position,
          styleId: 'land_address',
          content: this.model.land_address || '地块位置'
        }
      } else {
        markerGeo.position = position
        markerGeo.content = this.model.land_address || '地块位置'
      }

      if (!multiMarker) {
        multiMarker = new TMap.MultiMarker({
          map: mapContainer,
          geometries: [markerGeo],
          styles: {
            land_address: new TMap.MarkerStyle({
              direction: 'bottom',
              width: 20,
              height: 30
            })
          }
        })
      } else {
        multiMarker.updateGeometries([markerGeo])
      }
    },
    handleGetLng() {
      this.searchLoading = true
      const baseUrl = 'https://apis.map.qq.com'
      const url = `${baseUrl}/ws/geocoder/v1`
      jsonp(url, {
        key: 'POYBZ-JXV6Q-3KD5A-44ZNG-SGTIO-J6FDN',
        output: 'jsonp',
        address: this.model.land_address
      })
        .then((res) => {
          if (res.status === 0) {
            const { lng, lat } = res.result.location
            this.model.latitude = lat
            this.model.longitude = lng
            if (res.result?.ad_info?.adcode) {
              this.setRegionId(Number(res.result.ad_info.adcode))
            }
            this.setCenter(lat, lng)
            this.setMarker(lat, lng)
            if (!this.isMapEvent) {
              this.updateCenter()
            }
          } else {
            ElMessage({ message: '请填写详细地块地址', type: 'info' })
          }
        })
        .finally(() => {
          this.searchLoading = false
        })
    },
    updateCenter() {
      if (!this.isMapEvent) {
        this.isMapEvent = true
        mapContainer.on('center_changed', (e) => {
          this.setMarker()
          this.model.latitude = e.center.lat
          this.model.longitude = e.center.lng
        })
      }
    },

    // Unified handler for map movement
    handleMapMoved() {
      if (!this.mapContainer) return

      try {
        const center = this.mapContainer.getCenter()

        // Update model coordinates
        this.model.latitude = center.lat
        this.model.longitude = center.lng

        // Update marker position
        this.updateMarkerPosition()
      } catch (error) {
        console.error('Error handling map movement:', error)
      }
    },

    // Update marker position based on current map center
    updateMarkerPosition() {
      if (!this.mapContainer) return

      try {
        const center = this.mapContainer.getCenter()

        // Create a simple marker geometry
        const markerGeometry = {
          id: 'center',
          position: center,
          styleId: 'land_address',
          content: this.model.land_address || '地块位置'
        }

        // Handle the marker
        if (!this.multiMarker) {
          // Create new marker if it doesn't exist
          this.multiMarker = new TMap.MultiMarker({
            map: this.mapContainer,
            geometries: [markerGeometry],
            styles: {
              land_address: new TMap.MarkerStyle({
                direction: 'bottom',
                width: 20,
                height: 30
              })
            }
          })
        } else {
          // Update existing marker
          this.multiMarker.updateGeometries([markerGeometry])
        }

        // Store the current geometry for reference
        this.markerGeo = markerGeometry
      } catch (error) {
        console.error('Error updating marker position:', error)
      }
    },

    // Clean up resources when component is unmounted
    cleanupMapResources() {
      if (!this.isMounted) return

      try {
        // Remove map event listeners
        if (this.mapContainer) {
          this.mapContainer.off('dragend', this.handleMapMoved)
          this.mapContainer.off('zoomend', this.handleMapMoved)
        }

        // Remove marker
        if (this.multiMarker) {
          try {
            this.multiMarker.setMap(null)
          } catch (e) {
            console.error('Error removing marker:', e)
          }
          this.multiMarker = null
        }

        // Destroy map instance
        if (this.mapContainer) {
          try {
            this.mapContainer.destroy()
          } catch (e) {
            console.error('Error destroying map:', e)
          }
          this.mapContainer = null
        }

        // Clean up other references
        this.markerGeo = null
        this.mapInitialized = false
        this.isMapEvent = false
      } catch (error) {
        console.error('Error cleaning up map resources:', error)
      }
    },

    // 区域板块相关方法
    setRegionId(id) {
      if (!this.isMounted) return

      if (this.regionData.findIndex((i) => i.region_id === id) !== -1) {
        this.model.region_id = id
        this.changeRegion(this.model.region_id)
      }
    },

    async changeRegion(regionId) {
      if (!this.isMounted) return

      // 保存当前的plate_id，以便在加载新板块数据后可以恢复
      const currentPlateId = this.model.plate_id

      // 清空板块选择，无论是否有新的区域选择
      this.plateData = []
      this.model.plate_id = null
      this.model.sector_name = ''

      // 当值为null或0时处理
      if (regionId === null || regionId === 0 || regionId === '') {
        this.model.region_id = null
        this.model.region_name = ''
        return
      }

      // 转为数值类型
      regionId = Number(regionId)
      this.model.region_id = regionId

      // 设置区域名称
      if (regionId) {
        const index = this.regionData.findIndex((i) => Number(i.region_id) === regionId)
        if (index !== -1) {
          this.model.region_name = this.regionData[index].region_name
        }
      }

      // 加载新区域的板块数据
      await this.getPlateByRegion(regionId)

      // 如果有之前的plate_id，并且在新加载的板块数据中存在，则恢复它
      if (
        currentPlateId &&
        this.plateData.some((plate) => Number(plate.id) === Number(currentPlateId))
      ) {
        this.$nextTick(() => {
          this.model.plate_id = currentPlateId
          // 设置板块名称
          const plateItem = this.plateData.find(
            (item) => Number(item.id) === Number(currentPlateId)
          )
          if (plateItem) {
            this.model.sector_name = plateItem.name
          }
        })
      }
    },

    async getPlateByRegion(region_id) {
      if (!this.isMounted) return

      this.loading = true

      if (region_id === undefined || region_id === null) {
        this.plateData = []
        this.model.plate_id = 0
        this.loading = false
        return
      }

      try {
        let countyidValue = region_id
        if (Array.isArray(region_id)) {
          countyidValue = region_id[0] !== undefined && region_id[0] !== null ? region_id[0] : ''
        }

        const params = {
          state: 1,
          countyid: String(countyidValue),
          page: 1,
          limit: 100
        }

        const res = await list(params)

        if (!this.isMounted) return

        if (res.code === 200 && res.data && res.data.list) {
          this.plateData = res.data.list
        } else {
          this.plateData = []
        }
      } catch (error) {
        this.plateData = []
        console.error('获取板块数据异常:', error)
      } finally {
        if (this.isMounted) {
          this.loading = false
        }
      }
    },

    changePlate(plateId) {
      if (!this.isMounted) return

      // 处理null或0值
      if (plateId === null || plateId === 0 || plateId === '') {
        this.model.plate_id = null
        this.model.sector_name = ''
        return
      }

      // 将字符串转为数字
      plateId = Number(plateId)
      this.model.plate_id = plateId

      // 设置板块名称
      const plateItem = this.plateData.find((item) => Number(item.id) === plateId)
      if (plateItem) {
        this.model.sector_name = plateItem.name
      } else {
        this.model.sector_name = ''
      }
    },

    // 表单提交方法
    async submit() {
      if (!this.isMounted || !this.$refs.landForm) return

      this.$refs.landForm.validate(async (valid) => {
        if (!valid) {
          ElMessage.warning('表单验证未通过，请检查必填项')
          return false
        }

        if (this.model.region_id && !this.model.plate_id) {
          // 如果选择了区域，则检查是否选择了板块
          ElMessage.error('已选择区域，请选择板块')
          return false
        }

        this.loading = true

        try {
          const submitData = { ...this.model }
          // 处理数组数据转换为API格式
          this.processArrayData(submitData)

          if (this.model.brand) {
            this.changeBrands(this.model.brand)
          }

          const res = await request({
            url: '/admin/house.Plot/save',
            method: 'post',
            data: submitData
          })

          if (!this.isMounted) return

          if (res.code === 200) {
            if (!submitData.id || submitData.id === 0) {
              // 更新当前model的ID，但不进行路由跳转
              this.model.id = res.data
              ElMessage.success('新建地块成功')
              // 对于新建，关闭当前页面并导航到列表
              const currentRoute = this.$router.currentRoute.value
              this.tagsViewStore.delView(currentRoute).then(() => {
                this.$router.push('/plot/list')
              })
            } else {
              ElMessage.success('更新地块成功')
              // 对于编辑，关闭当前页面并导航到列表
              const currentRoute = this.$router.currentRoute.value
              this.tagsViewStore.delView(currentRoute).then(() => {
                this.$router.push('/plot/list')
              })
            }
          } else {
            ElMessage.error(res.msg || '保存失败')
          }
        } catch (error) {
          console.error('提交失败:', error)
          if (this.isMounted) {
            ElMessage.error('提交失败: ' + (error.message || '未知错误'))
          }
        } finally {
          if (this.isMounted) {
            this.loading = false
          }
        }
      })
    },

    // 处理提交前的数据格式化
    processArrayData(submitData) {
      if (submitData.batch_id === '') {
        submitData.batch_id = 0 // 或根据后端要求设置为 null 或 0
      }

      // 处理区域和板块ID，确保0值正确传递
      if (!submitData.region_id) {
        submitData.region_id = 0
      }

      if (submitData.brand && Array.isArray(submitData.brand)) {
        // 确保已经将品牌ID转换为品牌名称字符串
        if (!submitData.develop_brand || submitData.develop_brand === '') {
          this.changeBrands(submitData.brand)
          submitData.develop_brand = this.model.develop_brand
        }
      }

      if (!submitData.plate_id) {
        submitData.plate_id = 0
      }

      if (submitData.prefab_buildings && submitData.prefab_buildings.length > 0) {
        submitData.prefab_buildings = submitData.prefab_buildings.filter(
          (item) =>
            item.building_type.trim() !== '' ||
            item.proportion.trim() !== '' ||
            item.is_none === true
        )

        submitData.prefab_buildings.forEach((item) => {
          if (item.proportion !== '' && item.is_none === true) {
            item.proportion = ''
          }
          if (item.proportion !== '' && !item.is_none) {
            item.is_none = false
          }
        })
      }
      if (submitData.prefab_buildings && submitData.prefab_buildings.length === 1) {
        submitData.building_proportion = submitData.prefab_buildings[0].is_none
          ? ''
          : submitData.prefab_buildings[0].proportion
      }

      // 过滤空数据
      if (submitData.winners && submitData.winners.length > 0) {
        submitData.winners = submitData.winners.filter((item) => item.name.trim() !== '')
      }

      if (submitData.bidders && submitData.bidders.length > 0) {
        submitData.bidders = submitData.bidders.filter((item) => item.detail.trim() !== '')
      }

      if (submitData.equities && submitData.equities.length > 0) {
        submitData.equities = submitData.equities.filter(
          (item) => item.company.trim() !== '' || item.percentage.trim() !== ''
        )
      }

      if (submitData.land_use_groups && Array.isArray(submitData.land_use_groups)) {
        submitData.land_use_groups = submitData.land_use_groups.filter(
          (item) => item.land_use !== '' || item.plot_ratio !== '' || item.max_height !== ''
        )
      }

      if (submitData.construction_areas && submitData.construction_areas.length > 0) {
        submitData.construction_areas = submitData.construction_areas.filter(
          (item) => item.plan.trim() !== '' || item.area.trim() !== ''
        )
      }

      // 处理上传的文件数据
      this.processFileUploads(submitData)
    },

    processFileUploads(submitData) {
      if (Array.isArray(submitData.location_pic)) {
        submitData.location_pic = submitData.location_pic
          .map((item) => (typeof item === 'object' ? item.file_url || '' : ''))
          .filter((url) => url)
      } else {
        submitData.location_pic = []
      }

      // 处理图片文件 - 确保数组中每项只提取 file_url
      if (Array.isArray(submitData.progress_photos)) {
        submitData.progress_photos = submitData.progress_photos
          .map((item) => (typeof item === 'object' ? item.file_url || '' : ''))
          .filter((url) => url)
      } else {
        submitData.progress_photos = []
      }

      // 处理不利因素PDF - 只提取 file_url
      // if (Array.isArray(submitData.disadvantages)) {
      //   submitData.disadvantages = submitData.disadvantages
      //     .map((item) => (typeof item === 'object' ? item.file_url || '' : ''))
      //     .filter((url) => url)
      // } else {
      //   submitData.disadvantages = []
      // }
      if (Array.isArray(submitData.disadvantages)) {
        submitData.disadvantages = submitData.disadvantages
          .map((item) => {
            if (typeof item === 'object') {
              // 使用file_url和file_name构建URL
              if (item.file_url && item.file_name) {
                // 返回格式化的URL，包含文件名参数
                return `${item.file_url}?name=${encodeURIComponent(item.file_name)}.${
                  item.file_ext || 'pdf'
                }`
              } else if (item.file_url) {
                return item.file_url
              }
            }
            return ''
          })
          .filter((url) => url)
      } else {
        submitData.disadvantages = []
      }
    },

    isActive(tag) {
      return tag.fullPath === this.$route.fullPath
    },

    // 返回按钮
    refresh() {
      // 获取当前路由
      const currentRoute = this.$router.currentRoute.value

      // 调用标签视图的删除方法
      this.tagsViewStore.delView(currentRoute).then(({ visitedViews }) => {
        // 如果当前是激活的标签，则需要导航
        if (this.isActive(currentRoute)) {
          // 强制导航到土拍管理页面
          this.$router.push('/plot/list')
        }
      })
    }
  }
}
</script>

<style scoped>
.wrapper {
  height: calc(100vh - 120px);
  overflow-y: auto;
}

.card {
  margin-bottom: 20px;
}

.w-full {
  width: 100%;
}

.sub-btn {
  margin-top: 20px;
  text-align: center;
  padding-bottom: 30px;
}

.upload-placeholder i {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 10px;
}

.text-center {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.region_plate .el-form-item__content {
  display: flex;
}

/* 确保配建类用地规划建面行正确对齐 */
.construction-row {
  display: flex;
  align-items: center;
  width: 100%;
}

/* 确保文本居中对齐 */
.text-center {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px; /* 与输入框高度匹配 */
}

.land-use-row {
  display: flex;
  align-items: center;
  width: 100%;
}

/* 确保按钮垂直居中 */
.land-use-row .el-button {
  display: flex;
  align-items: center;
  justify-content: center;
}

.dynamic-row {
  display: flex;
  align-items: center;
  width: 100%;
}

/* 确保按钮垂直居中 */
.dynamic-row .el-button {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 确保文本居中对齐 */
.text-center {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px; /* 与输入框高度匹配 */
}
</style>
