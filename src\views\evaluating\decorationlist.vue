<template>
  <div class="app-container">
    <!-- 查询 -->
    <el-form ref="queryRef" :model="query" :inline="true" label-width="90px">
      <el-form-item label="评测名称：">
        <el-input v-model="query.name" class="ya-search-value" placeholder="户型名称" clearable />
      </el-form-item>
      <el-form-item label="所属楼盘：">
        <el-select
          v-model="query.house_id"
          class="w-full ya-search-value"
          clearable
          filterable
          remote
          :remote-method="houselist"
          placeholder="请选择所属楼盘"
          :loading="loading"
        >
          <el-option
            v-for="item in search_houselist"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发布状态：">
        <el-select v-model="query.release_status" style="width: 120px" clearable placeholder="全部">
          <el-option key="1" label="已发布" value="1" />
          <el-option key="0" label="待发布" value="0" />
        </el-select>
      </el-form-item>
    </el-form>
    <el-row>
      <el-col class="mb-2">
        <el-select v-model="query.date_field" class="ya-date-field" placeholder="时间类型">
          <el-option value="create_time" label="创建时间" />
          <el-option value="update_time" label="修改时间" />
        </el-select>
        <el-date-picker
          v-model="query.date_value"
          type="datetimerange"
          class="ya-date-value"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="[new Date(2024, 1, 1, 0, 0, 0), new Date(2024, 1, 1, 23, 59, 59)]"
        />
        &nbsp;&nbsp;
        <el-button type="primary" @click="search()">查询</el-button>
        <el-button title="重置" @click="refresh()">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" @click="add()">添加</el-button>
      </el-col>
    </el-row>
    <!-- 操作 -->
    <el-row>
      
    </el-row>
    
    <!-- 列表 -->
    <el-table
      ref="table"
      v-loading="loading"
      :data="data"
      :height="height"
      @sort-change="sort"
      @selection-change="select"
    >
      <el-table-column type="selection" width="42" title="全选/反选" />
      <el-table-column :prop="idkey" label="ID" width="80" sortable="custom" />
      <el-table-column prop="name" label="评测名称" min-width="105" show-overflow-tooltip />
      <el-table-column prop="house_name" label="所属楼盘" min-width="105" show-overflow-tooltip>
       
      </el-table-column>
      <el-table-column
        prop="member_score"
        label="评测评分"
        min-width="85"
        show-overflow-tooltip
        sortable="custom"
      />

      <el-table-column prop="update_time" label="更新时间" width="165" sortable="custom" />
      <el-table-column
        prop="professional_score"
        label="用户评分"
        min-width="85"
        show-overflow-tooltip
        sortable="custom"
      />
      <el-table-column
        prop="professional_score"
        label="专家评分"
        min-width="85"
        show-overflow-tooltip
        sortable="custom"
      />
      
      <el-table-column prop="release_status" label="发布状态" min-width="85">
        <template #default="scope">
          <span v-text="search_params['releaseStatus'][scope.row.release_status]"></span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="165">
        <template #default="scope">
          <el-link type="primary" class="mr-1" :underline="false" @click="edit(scope.row)">编辑</el-link>
          |
          <el-link type="primary" class="mr-1" :underline="false" @click="selectOpen('release', scope.row, 'up')" v-if="scope.row.release_status == 2">发布</el-link>
          <el-link type="primary" class="mr-1" :underline="false" @click="selectOpen('release', scope.row, 'down')" v-else >下架</el-link>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="count > 0"
      v-model:total="count"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="list"
    />
    <el-dialog
      v-model="selectDialog"
      :title="selectTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      top="20vh"
    >
      <el-form ref="selectRef" label-width="120px">
       
        <el-form-item label="评测名称">
          <el-input v-model="selectNames" type="textarea" autosize disabled />
        </el-form-item>
        
        <el-form-item :label="name + 'ID'">
          <el-input v-model="selectIds" type="textarea" autosize disabled />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button :loading="loading" @click="selectCancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="selectSubmit(selectIds)">提交</el-button>
      </template>
    </el-dialog>
    <!-- 添加修改 -->
    <el-dialog
      v-model="dialog"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="cancel"
      destroy-on-close
      width="1400px"
      top="5vh"
    >
      <el-form ref="formRef" :rules="baseRules" :model="model" label-width="100px">
        <el-tabs>
          <el-tab-pane label="基础信息">
            <el-scrollbar native :height="height - 80">
              <el-row>
                <el-col :span="11">
                  <el-divider>
                    <h3 class="bold">基础信息</h3>
                  </el-divider>
                  <el-form-item label="所属楼盘" prop="house_id" placeholder="请选择所属楼盘">
                    <el-select
                      v-model="model.house_id"
                      class="w-full"
                      clearable
                      filterable
                      remote
                      placeholder="请选择所属楼盘"
                      :remote-method="houselist"
                      :loading="loading"
                      @change="houseChange"
                    >
                      <el-option
                        v-for="item in search_houselist"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                  
                  <el-form-item label="评测名称" prop="name" require>
                    <el-input v-model="model.name" placeholder="输入评测名称" clearable> </el-input>
                  </el-form-item>
                  <el-form-item label="绑定户型" prop="housetype">
                    <el-select
                      v-model="model.housetype"
                      class="w-full"
                      multiple
                      clearable
                      filterable
                    >
                      <el-option
                        v-for="(value, key) in houseTypes"
                        :key="value.id"
                        :label="value.name + ' | ' + (value.release_status == 0?'未发布':'已发布')"
                        :value="value.id"
                        :disabled="value.release_status == 0"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="" prop=""></el-form-item>
                  <el-divider>
                    <h3 class="bold">视频信息</h3>
                  </el-divider>
                  <el-form-item label="" prop=""></el-form-item>
                  <div class="grid-content bordered-box" v-for="(vva, kky) in model.videos">
                      <el-divider>
                        <h5 class="bold" v-if="kky == 'sample'">样板间视频</h5>
                        <h5 class="bold" v-else>交付实景视频</h5>
                      </el-divider>
                      <!-- 判断是否存在视频 -->
                      

                      <el-form-item v-if="Object.keys(model.videos[kky]).length == 0">
                        <el-col :span="14"> </el-col>
                        <el-col :span="10" :offset="7">
                          <el-button @click="addVideo(kky)" type="primary">新增视频</el-button>
                        </el-col>
                      </el-form-item>
                      <div v-else>  
                      <el-form-item label="名称" :prop="'video_name'">
                        <el-input v-model="model.videos[kky].name" placeholder="请输入名称" clearable />
                      </el-form-item>
                      <el-form-item label="关联主播" prop="related_anchor">
                    <el-select
                      v-model="model.videos[kky].related_anchor"
                      class="w-full"
                      clearable
                      filterable
                    >
                      <el-option
                        v-for="(value, key) in memberIdentityList"
                        :key="value.id"
                        :label="value.service_name + ' () ' + (value.release_status == 2?'':' | 取消身份')"
                        :value="value.id"
                        :disabled="value.release_status != 2"
                      />
                    </el-select>
                  </el-form-item>
                      <el-form-item label="类型" prop="type">
                        <el-select v-model="model.videos[kky].type" placeholder="请选择类型">
                          <el-option label="视频号直播" :value="1" />
                          <el-option label="视频号视频" :value="2" />
                          <el-option label="上传视频" :value="3" />
                        </el-select>
                      </el-form-item>

                      <!-- 视频号直播相关字段 -->
                      <template v-if="model.videos[kky].type === 1">
                        <el-form-item label="视频号ID" prop="video_num_id">
                          <el-input v-model="model.videos[kky].video_num_id" placeholder="请输入视频号ID" clearable />
                        </el-form-item>
                        <el-form-item label="开始时间" prop="start_time">
                          <el-date-picker
                            v-model="model.videos[kky].start_time"
                            type="datetime"
                            placeholder="请选择开始时间"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            timezone="Asia/Shanghai"
                          />
                        </el-form-item>
                        <el-form-item label="结束时间" prop="end_time">
                          <el-date-picker
                            v-model="model.videos[kky].end_time"
                            type="datetime"
                            placeholder="请选择结束时间"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            timezone="Asia/Shanghai"
                          />
                        </el-form-item>
                      </template>

                      <!-- 视频号视频相关字段 -->
                      <template v-if="model.videos[kky].type === 2">
                        <el-form-item label="视频号ID" prop="video_num_id">
                          <el-input v-model="model.videos[kky].video_num_id" placeholder="请输入视频号ID" clearable />
                        </el-form-item>
                        <el-form-item label="是否同主体" prop="is_same_subject">
                          <el-radio-group v-model="model.videos[kky].is_same_subject">
                            <el-radio :label="1">是</el-radio>
                            <el-radio :label="0">否</el-radio>
                          </el-radio-group>
                        </el-form-item>
                        <el-form-item label="视频ID" prop="video_id">
                          <el-input
                            v-model="model.videos[kky].video_id"
                            placeholder="视频id或feed-token（非同主体）"
                            clearable
                          />
                        </el-form-item>
                      </template>

                      <!-- 上传视频相关字段 -->
                      <template v-if="model.videos[kky].type === 3">
                        <el-form-item label="标题" prop="title">
                          <el-input v-model="model.videos[kky].title" placeholder="请输入标题" clearable />
                        </el-form-item>
                        <el-form-item label="是否添加水印" prop="is_watermark">
                          <el-radio-group v-model="initModel.is_watermark" style="margin-left: 10px">
                            <el-radio label="否" :value="0"></el-radio>
                            <el-radio label="是" :value="1"></el-radio>
                          </el-radio-group>
                        </el-form-item>
                        <el-form-item label="视频封面" prop="cover_url">
                          {{ is_watermark }}
                          <div class="upload-container">
                            <ImgUploadsHouseType
                              v-model="model.videos[kky].cover_url"
                              :upload-btn="model.videos[kky].cover_url?.length ? '' : '上传封面'"
                              file-type="image"
                              :height="200"
                              :isWatermark="initModel.is_watermark"
                              :source="model.videos[kky].source"
                            />
                          </div>
                        </el-form-item>
                        <el-form-item label="上传视频" prop="video_url">
                          <div class="upload-container">
                            <ImgUploadsHouseType
                              v-model="model.videos[kky].video_url"
                              :upload-btn="model.videos[kky].video_url?.length ? '' : '上传视频'"
                              file-type="video"
                              :height="200"
                              @update:modelValue="handleVideoChange"
                            />
                          </div>
                        </el-form-item>
                      </template>

                      <el-form-item label="状态" prop="state">
                        <el-radio-group v-model="model.videos[kky].state">
                          <el-radio :label="1">有效</el-radio>
                          <el-radio :label="2">无效</el-radio>
                        </el-radio-group>
                      </el-form-item>
                      <el-form-item label="" >
                      <el-button type="primary" :loading="loading" @click="cancelVideo(kky)">删除</el-button>
                    </el-form-item>
                    
                  </div>
                  </div>
                </el-col>
                <el-col :offset="1" :span="11">
                  <el-divider>
                    <h3 class="bold">评测信息</h3>
                  </el-divider>
                   <el-form-item label="户型图" prop="pictures">
                    <ImgUploadsHouseType
                      v-model="model.pictures"
                      upload-btn="上传图片"
                      file-type="image"
                      file-tip=""
                      :source="source"
                    />
                  </el-form-item>
                  <el-form-item label="级别(依据装修标准计算级别为：中端)" prop="level"  label-position="top">
                    <el-radio-group v-model="model.level" style="margin-left: 10px">
                      <el-radio label="经济" :value="1"></el-radio>
                      <el-radio label="中端" :value="2"></el-radio>
                      <el-radio label="高端" :value="3"></el-radio>
                      <el-radio label="豪华" :value="4"></el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item
                        label="优缺点描述"  label-position="top"
                        v-for="(item2, indx) in model.features"
                        prop="features"
                      >
                        <el-col :span="4">
                          <el-select v-model="item2.type" class="w-full" filterable>
                            <el-option key="优点" label="优点" :value="1" />
                            <el-option key="缺点" label="缺点" :value="2" />
                          </el-select>
                        </el-col>
                        <el-col :span="10">
                          <el-input v-model="item2.content" placeholder="输入描述（15个汉字以内）" />
                        </el-col>

                        <el-col :span="5" :offset="1">
                          <el-button @click="addPoints(idx)" type="primary">+</el-button>
                          <el-button @click="cancelPoints(idx, indx)" v-if="indx > 0">-</el-button>
                        </el-col>
                      </el-form-item>
                      <el-form-item label="评分描述" prop="basic_rate_desc" label-position="top">
                    <el-input
                      type="textarea"
                      :rows="20"
                      placeholder="输入基础评分描述"
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-scrollbar>
          </el-tab-pane>
          <el-divider><h3 class="bold">交付标准</h3></el-divider>
          <!-- <DecorationItem :decorationId="model.decoration_id" /> -->
        </el-tabs>
      </el-form>
      <el-form-item label="重新算分">
        <el-col :span="15">
          <el-checkbox v-model="model.caculate">是</el-checkbox>
        </el-col>
      </el-form-item>
      <el-form-item label="交付标准导入" v-if="model.id">
      <el-button
          ><a
            href="https://img.betterhousing.cn/storage/file/20240725/362e58aba89ec512b963400472f96d65186b4063.xlsx"
            >下载导入模板</a
          >
        </el-button>
        <excel-import title="批量导入" @on-import="imports" />
      </el-form-item>
      <template #footer>
        <el-button :loading="loading" @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submit">提交</el-button>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import screenHeight from '@/utils/screen-height'
import Pagination from '@/components/Pagination/index.vue'
import { arrayColumn } from '@/utils/index'
import { getPageLimit } from '@/utils/settings'

import {
  list as listApi,
  info as infoApi,
  release as releaseApi,
  edit as editApi,
} from '@/api/project/decoration'

import { houseList as houseListApi } from '@/api/project/house'
import { itemexport as itemexportApi } from '@/api/project/decorationitem'
import { getList as getHouseTypeListApi } from '@/api/project/housetype'
import { typesrolemember as typesrolememberApi } from '@/api/member/member'

const name = ref('装修评测')
const height = ref(680)
const loading = ref(false)
const idkey = ref('id')
const search_params = ref({})
const search_houselist = ref([])
const exps = ref([{ exp: 'like', name: '包含' }])
const realseOpt = ref("")
const formRef = ref(null)

const query = ref({
  page: 1,
  release_status: null,
  name: null,
  house_id: null,
  limit: getPageLimit(),
  date_field: 'create_time'
})

// 初始视频模型
const initModel = {
  name: '',
  type: undefined,
  video_num_id: '',
  video_id: '',
  start_time: '',
  end_time: '',
  cover_url: [],
  video_url: [],
  state: 1,
  is_same_subject: 1,
  is_watermark: 0,
  source: 3
}
const data = ref([])
const count = ref(0)
const dialog = ref(false)
const model = ref({
  id: 0,
  name: '',
  house_id: null,
  decoration_id:null,
  videos: {
    'sample':{},
    'charge':{}
  },
  housetype: [],
  level: 0,
  features: [
    {'type':1,'content':''}
  ],
  pictures: [],
})
const selection = ref([])
const selectIds = ref('')
const selectTitle = ref('操作')
const selectDialog = ref(false)
const selectType = ref('')

const queryRef = ref(null)
const table = ref(null)
const selectRef = ref(null)

// const ref = ref(null)
const route = useRoute()
const dialogTitle = ref('添加')
import request from '@/utils/request'
onMounted(() => {
  height.value = screenHeight()
  if (route.query.house_id) {
    query.value.house_id = parseInt(route.query.house_id)
  }
  list()
  // if (query.value.house_id) {
  //   houselistbyid(query.value.house_id)
  // }
})

const rules = {
  video_name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择类型', trigger: 'change' }],
  // 视频号直播相关验证
  video_num_id: [
    {
      required: true,
      message: '请输入视频号ID',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (model.value.type === 1 || model.value.type === 2) {
          if (!value) {
            callback(new Error('请输入视频号ID'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }
    }
  ],
  // 视频号视频相关验证
  video_id: [
    {
      required: true,
      message: '请输入视频ID',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (model.value.type === 2) {
          if (!value) {
            callback(new Error('请输入视频ID'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }
    }
  ],
  // 上传视频相关验证
  title: [
    {
      required: true,
      message: '请输入标题',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (model.value.type === 3) {
          if (!value) {
            callback(new Error('请输入标题'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }
    }
  ],
  cover_url: [
    {
      required: true,
      message: '请上传视频封面',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (model.value.type === 3) {
          if (!value || value.length === 0) {
            callback(new Error('请上传视频封面'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }
    }
  ],
  video_url: [
    {
      required: true,
      message: '请上传视频',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (model.value.type === 3) {
          if (!value || value.length === 0) {
            callback(new Error('请上传视频'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }
    }
  ]
}
const baseRules = ref({
    name: [{ required: true, message: '请填写楼盘名称', trigger: 'blur' }],
    house_id: [{ required: true, message: '请选择楼盘', trigger: 'blur' }],
    ...rules
})
const memberIdentityList = ref([])
// 列表
const list = () => {
  loading.value = true
  listApi(query.value)
    .then((res) => {
      data.value = res.data.list
      count.value = res.data.count
      exps.value = res.data.exps
      loading.value = false
      search_params.value = res.data.search_params
    })
    .catch(() => {
      loading.value = false
    })
}

// 根据楼盘名称获取楼盘列表
const houselist = (keywords) => {
  houseListApi({ name: keywords })
    .then((res) => {
      search_houselist.value = res.data.list
    })
    .catch((error) => {})
}

// 根据楼盘名称获取楼盘列表
const houselistbyid = (id) => {
  houseListApi({ id: id })
    .then((res) => {
      search_houselist.value = res.data.list
    }).then(()=>{
      houseChange(id)
    })
    .catch((error) => {})
}
const houseTypes = ref([])
// 根据楼盘名称获取楼盘列表
const houseChange = (house_id) => {

  getHouseTypeListApi({ house_id: house_id })
    .then((res) => {
      houseTypes.value = res.data
    })
    .catch((error) => {})
}

// 添加修改
const add = () => {
  dialog.value = true
  dialogTitle.value = name.value + '添加'
  reset()
}

const edit = (row) => {
  dialog.value = true
  dialogTitle.value = name.value + '修改：' + row[idkey.value]
  var id = {}
  id[idkey.value] = row[idkey.value]
  infoApi(id)
    .then((res) => {
      houselistbyid(res.data.house_id)
      reset(res.data)
      
    })
    .catch(() => {})
}

const cancel = () => {
  dialog.value = false
  reset()
}

const submit = () => {

  formRef.value.validate((valid) => { 
    if (valid) { 
        editApi(model.value).then((res) => {
        list()
        dialog.value = false
        ElMessage.success(res.msg)
      }).catch(() => {})
    }else { 
      alert('表单验证失败'); 
      return false; 
    } 
  }); 
}


// 重置
const reset = (row) => {
  if (row) {
    model.value = row
    let ids = [];
    // alert(JSON.stringify(row['videos']['sample']))
      if (Object.keys(row['videos']['sample']).length !== 0) {
        if (row['videos']['sample']['related_anchor']) {
          ids.push(row['videos']['sample']['related_anchor'])
        }
        
        model.value['videos']['sample'] = {
         ...initModel, // 先设置初始值
         ...row['videos']['sample'], // 再覆盖返回的数据
        }    
      }
      
      if (Object.keys(row['videos']['charge']).length !== 0) {
        if (row['videos']['charge']['related_anchor']) {
          ids.push(row['videos']['charge']['related_anchor'])
        }
        model.value['videos']['charge'] = {
         ...initModel, // 先设置初始值
         ...row['videos']['charge'], // 再覆盖返回的数据
          }    
      }
      getMemberIdentityList(ids.join(','))
  } else {
    model.value = {
      id: 0,
      name: '',
      house_id: null,
      videos: {
        'sample':{},
        'charge':{}
      },
      housetype: [],
      level: 0,
      features: [
        {'type':1,'content':''}
      ],
      pictures: [],
    }
  }
  
  if (ref.value !== undefined) {
    try {
      ref.value.resetFields()
      ref.value.clearValidate()
    } catch (error) {
      console.log(error)
    }
  }
}

// 查询
const search = () => {
  query.value.page = 1
  list()
}

// 刷新
const refresh = () => {
  const limit = query.value.limit
  query.value = {
    page: 1,
    release_status: null,
    name: null,
    house_id: null,
    limit: getPageLimit(),
    date_field: 'create_time'
  }
  table.value.clearSort()
  query.value.limit = limit
  list()
}

// 排序
const sort = (sort) => {
  query.value.sort_field = sort.prop
  query.value.sort_value = ''
  if (sort.order === 'ascending') {
    query.value.sort_value = 'asc'
    list()
  }
  if (sort.order === 'descending') {
    query.value.sort_value = 'desc'
    list()
  }
}

// 操作
const select = (selectionRows) => {
  selection.value = selectionRows
  selectIds.value = selectGetIds(selectionRows).toString()
}

const selectGetIds = (selectionRows) => {
  return arrayColumn(selectionRows, idkey.value)
}

const selectAlert = () => {
  ElMessageBox.alert('请选择需要操作的' + name.value, '提示', {
    type: 'warning',
    callback: () => {}
  })
}

const selectSubmit = (val) => {
  release(val, true)
  selectDialog.value = false
}


const selectOpen = (sectType, selectRow = '', opt = '') => {
  var optName = opt == 'up' ? '发布' : '下架'
  realseOpt.value = opt
  if (sectType === 'release') {
    selectTitle.value = optName
  }
  selectIds.value = selectRow.id
  selectNames.value = selectRow.name
  selectDialog.value = true
  selectType.value = sectType
}

const selectCancel = () => {
  selectDialog.value = false
}

const addPoints = (index) => {
  const prosCons = { type: 1, content: '' } // 优点
  model.value.features.push(prosCons)
}

const cancelPoints = (index, index2) => {
  model.value.features.splice(index2, 1)
}
const cancelVideo =(kky)=>{
  model.value.videos[kky] = {}
  
}
const addVideo =(kky)=>{
  model.value.videos[kky] = {...initModel}
}
// 发布时间
const release = (row) => {
  if (!row) {
    selectAlert()
  } else {
    loading.value = true
    releaseApi({
      id: row,
      release_status: realseOpt.value == 'up' ? 1 : 2
    })
      .then((res) => {
        list()
        ElMessage.success(res.msg)
      })
      .catch(() => {
        list()
      })
  }
}

// 导入，results数据，header表头
function imports({ results, header }) {
  loading.value = true
  itemexportApi({
    decoration_id: model.value.id,
    import: results
  })
    .then((res) => {
      ElMessage.success(res.msg)
    })
    .catch(() => {
      loading.value = false
    })
}

const getMemberIdentityList = (ids) => {
  typesrolememberApi({ ids: ids })
   .then((res) => {
      memberIdentityList.value = res.data
    })
   .catch((error) => {})
  
}

</script>

<style>
.bordered-box {
  border: 1px solid #d3dce6; /* 设置边框样式 */
  border-radius: 4px; /* 可选：设置圆角 */
  padding: 10px; /* 内边距 */
  box-sizing: border-box; /* 确保边框不影响内部内容的宽度 */
  margin-top: 10px;
}
</style>