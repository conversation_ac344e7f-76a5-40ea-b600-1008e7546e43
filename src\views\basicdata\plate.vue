<template>
  <div class="app-container">
    <el-form ref="queryRef" :model="query" :inline="true" label-width="90px">
      <el-form-item label="板块名称：" prop="name">
        <el-input
          v-model="query.name"
          maxlength="20"
          placeholder="请输入板块名称"
          style="width: 140px"
        />
      </el-form-item>

      <!-- <el-form-item label="所属区域：" prop="countyid">
        <el-select v-model="query.countyid" style="width: 120px">
          <el-option v-for="dict in releaseStatusList" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="所属区域：" prop="countyid">
        <el-cascader
          v-model="query.countyid"
          :options="trees"
          :props="searchcascaderprops"
          class="w-full"
          placeholder="所属区域"
          clearable
          filterable
          @change="handleChange"
          popper-class="popper-select"
          :checkStrictly="true"
        />
      </el-form-item>

      <el-form-item label="评测展示" prop="evaluating_show">
        <el-select v-model="query.is_show" style="width: 120px">
          <el-option
            v-for="dict in evaluatingShowList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发布状态" prop="evaluating_status">
        <el-select v-model="query.state" style="width: 120px">
          <el-option
            v-for="dict in evaluatingStateList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="query.date_value"
          type="datetimerange"
          class="ya-date-value"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="[new Date(2024, 1, 1, 0, 0, 0), new Date(2024, 1, 1, 23, 59, 59)]"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" title="搜索" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button @click="add">新增板块</el-button>

        <el-button @click="refresh">刷新列表</el-button>
        <el-button @click="dowonloadExcel">板块评测模版下载</el-button>
        <el-button @click="dowonloadIndustryExcel">产业评测模版下载</el-button>
        <!-- <el-button title="导出" class="float-right" @click="selectOpen('export')">导出</el-button> -->
        <el-tooltip content="导入板块评测" effect="dark" placement="left">
          <excel-import title="导入板块评测" @on-import="imports" />
        </el-tooltip>
        <el-tooltip content="导入产业评测" effect="dark" placement="left">
          <excel-import title="导入产业评测" @on-import="idustryimports" />
        </el-tooltip>
      </el-form-item>
    </el-form>
    <!-- 搜索end -->

    <!--  按钮 end-->
    <el-table ref="table" v-loading="loading" :data="data" :height="height">
      <el-table-column fixed :prop="idkey" label="板块ID" width="80" />
      <el-table-column prop="name" label="板块名称" min-width="150" show-overflow-tooltip />
      <el-table-column prop="countyname" label="区域" min-width="150" show-overflow-tooltip />
      <el-table-column prop="countscore" label="板块评分" min-width="150" show-overflow-tooltip />
      <el-table-column
        prop="interpretation_score"
        label="产业评分"
        min-width="150"
        show-overflow-tooltip
      />
      <el-table-column
        prop="updatetime"
        label="更新时间"
        min-width="150"
        :formatter="customformat('YYYY-MM-DD HH:mm:ss')"
        show-overflow-tooltip
      />
      <el-table-column label="评测展示<br />板块/产业" :render-header="renderHeader" align="center">
        <template #default="scope">
          <el-text v-if="scope.row.scoreshow == 0" type="warning">否</el-text>
          <el-text v-else-if="scope.row.scoreshow == 1" type="success">是</el-text>
          <el-text v-else type="warning">否</el-text>
          <el-text>/</el-text>
          <el-text v-if="scope.row.is_show == 0" type="warning">否</el-text>
          <el-text v-else-if="scope.row.is_show == 1" type="success">是</el-text>
          <el-text v-else type="warning">否</el-text>
        </template>
      </el-table-column>
      <el-table-column label="发布状态" align="center">
        <template #default="scope">
          <el-text v-if="scope.row.state == 0" type="warning">未发布</el-text>
          <el-text v-else-if="scope.row.state == 1" type="success">已发布</el-text>
          <el-text v-else-if="scope.row.state == 2" type="success">已下架</el-text>
          <el-text v-else>-</el-text>
        </template>
      </el-table-column>

      <el-table-column fixed="right" label="操作" width="125">
        <template #default="scope">
          <el-link type="primary" class="mr-1" :underline="false" @click="edit(scope.row)">
            编辑
          </el-link>
          <el-link
            v-if="scope.row.state == 0"
            type="primary"
            class="mr-1"
            :underline="false"
            @click="publishstate(scope.row)"
          >
            发布
          </el-link>
          <el-link
            v-if="scope.row.state == 2"
            type="primary"
            class="mr-1"
            :underline="false"
            @click="publishstate(scope.row)"
          >
            发布
          </el-link>
          <el-link
            v-if="scope.row.state == 1"
            type="primary"
            class="mr-1"
            :underline="false"
            @click="downloadstate(scope.row)"
          >
            下架
          </el-link>

          <el-link :loading="loading" type="primary" @click="handlePhsuMessage(scope.row.id)"
            >推送</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="count > 0"
      v-model:total="count"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="list"
    />

    <el-dialog
      v-model="dialog"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="cancel"
      custom-class="custom-dialog"
      width="1400px"
      top="2vh"
      @close="customcloseDialog"
    >
      <el-tabs v-model="currentTab">
        <el-tab-pane label="基础信息" name="基础信息" lazy>
          <el-scrollbar native :height="600">
            <el-form ref="ref" :rules="rules" :model="model" label-width="150px">
              <el-form-item label="板块名称" prop="name" required>
                <el-input v-model="model.name" placeholder="请输入板块名称" clearable> </el-input>
              </el-form-item>
              <el-form-item label="所属区域" prop="region_pid" required>
                <el-cascader
                  v-model="model.region_pid"
                  :options="trees"
                  :props="cascaderprops"
                  class="w-full"
                  placeholder="所属区域"
                  clearable
                  filterable
                  @change="handleChange"
                  popper-class="popper-select"
                />
              </el-form-item>

              <el-form-item label="板块地理围栏" prop="fences">
                <el-input
                  v-model="model.fences"
                  type="textarea"
                  :rows="5"
                  placeholder="请输入内容"
                  clearable
                >
                </el-input>
                <span>（腾讯地图，注意纬度在前;符号分隔）</span>
                <el-button @click="handleSetCenter(false)">自动获取中心点坐标</el-button>
              </el-form-item>
              <el-form-item label="中心点坐标拾取" v-if="dialog">
                <div id="mapContaniner"></div>
                <span>(GCJ02坐标系，腾讯地图)</span>
              </el-form-item>
              <el-form-item label="板块中心点坐标" prop="coordinate">
                <el-input
                  readonly
                  v-model="model.coordinate"
                  placeholder="输入板块中心点坐标，格式：纬度,经度"
                  clearable
                />

                <span>（腾讯地图）</span>
              </el-form-item>
              <!-- <el-form-item label="排序" prop="sort">
                <el-input
                  v-model="model.sort"
                  type="number"
                  placeholder="请输入排序，eg：2250"
                  clearable
                >
                </el-input>
              </el-form-item> -->
              <el-form-item label="是否添加水印" prop="is_watermark">
                <el-radio-group v-model="model.is_watermark" style="margin-left: 10px">
                  <el-radio label="否" :value="0" disabled></el-radio>
                  <el-radio label="是" :value="1"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="板块头图" prop="plateimg_id">
                <ImgUpload
                  :isWatermark="model.is_watermark"
                  :source="modelReset.source"
                  v-model="model.plateimg_id"
                  v-model:file-url="model.plateimg_url"
                  file-type="image"
                  :height="100"
                  upload
                />
              </el-form-item>
              <el-form-item label="相邻板块：" prop="related_plate">
                <el-select v-model="model.related_plate" multiple>
                  <el-option
                    v-for="rplate in relatedPlate"
                    :key="rplate.id"
                    :value="rplate.id"
                    :label="rplate.name"
                    :disabled="model.id == rplate.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="总点击量：" prop="viewnum">
                <el-text>{{ model.viewnum }}</el-text>
              </el-form-item>

              <el-button :loading="loading" @click="cancel">取消</el-button>
              <el-button :loading="loading" type="primary" @click="submit">提交</el-button>
            </el-form>
          </el-scrollbar>

          <!-- <template #footer>
        <el-button :loading="loading" @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submit">提交</el-button> 
      </template> -->
        </el-tab-pane>
        <el-tab-pane label="板块评测" name="板块信息" :disabled="dialogTitle === '添加'">
          <el-scrollbar native :height="600">
            <el-form ref="scoreref" :model="platescoremodel" label-width="180px" class="platscore">
              <el-row>
                <el-col :span="11">
                  <el-form-item label="板块评测可见" prop="scoreshow">
                    <el-radio-group v-model="platescoremodel.scoreshow">
                      <el-radio :value="1">展示</el-radio>
                      <el-radio :value="0">不展示</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-divider>
                    <h3 class="bold">板块解读</h3>
                  </el-divider>

                  <el-form-item label="板块总分" prop="countscore">
                    <el-input v-model="platescoremodel.countscore" placeholder="输入分值" clearable>
                    </el-input>
                  </el-form-item>
                  <el-form-item label="图片" prop="platedetailimg_id">
                    <ImgUpload
                      source="5"
                      :isWatermark="platescoremodel.is_watermark"
                      v-model="platescoremodel.platedetailimg_id"
                      v-model:file-url="platescoremodel.platedetailimg_url"
                      file-type="image"
                      :height="100"
                      upload
                    />
                  </el-form-item>
                  <el-form-item label="板块解读描述" prop="platemark">
                    <el-input
                      v-model="platescoremodel.platemark"
                      type="textarea"
                      placeholder="请输入内容"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-divider style="margin-top: 30px">
                    <h3 class="bold">当前价值-生活便利度</h3>
                  </el-divider>

                  <el-form-item label="生活便利度评分" prop="livehelpscore">
                    <el-input
                      v-model="platescoremodel.livehelpscore"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="生活便利度描述" prop="livehelpmark">
                    <el-input
                      v-model="platescoremodel.livehelpmark"
                      type="textarea"
                      placeholder="请输入内容"
                      clearable
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="开发程度评分" prop="developscore">
                    <el-input
                      v-model="platescoremodel.developscore"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="开发程度描述" prop="developmark">
                    <el-input
                      v-model="platescoremodel.developmark"
                      placeholder="输入描述"
                      clearable
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="教育资源评分" prop="eduresourcescore">
                    <el-input
                      v-model="platescoremodel.eduresourcescore"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="教育资源描述" prop="eduresourcemark">
                    <el-input
                      v-model="platescoremodel.eduresourcemark"
                      placeholder="输入描述"
                      clearable
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="交通便利度评分" prop="trafficeasyscore">
                    <el-input
                      v-model="platescoremodel.trafficeasyscore"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="交通便利度描述" prop="trafficeasymark">
                    <el-input
                      v-model="platescoremodel.trafficeasymark"
                      placeholder="输入描述"
                      clearable
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="配套消费评分" prop="matchexpendscore">
                    <el-input
                      v-model="platescoremodel.matchexpendscore"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="配套消费描述" prop="matchexpendmark">
                    <el-input
                      v-model="platescoremodel.matchexpendmark"
                      placeholder="输入描述"
                      clearable
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="医疗资源评分" prop="medicalsourcescore">
                    <el-input
                      v-model="platescoremodel.medicalsourcescore"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="医疗资源描述" prop="medicalsourcemark">
                    <el-input
                      v-model="platescoremodel.medicalsourcemark"
                      placeholder="输入描述"
                      clearable
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="景点评分" prop="scenicspotscore">
                    <el-input
                      v-model="platescoremodel.scenicspotscore"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="景点描述" prop="scenicspotmark">
                    <el-input
                      v-model="platescoremodel.scenicspotmark"
                      placeholder="输入描述"
                      clearable
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="枢纽度评分" prop="hubnessscore">
                    <el-input
                      v-model="platescoremodel.hubnessscore"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="枢纽度描述" prop="hubnessmark">
                    <el-input
                      v-model="platescoremodel.hubnessmark"
                      placeholder="输入描述"
                      clearable
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="行政中心评分" prop="admincenterscore">
                    <el-input
                      v-model="platescoremodel.admincenterscore"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="行政中心描述" prop="admincentermark">
                    <el-input
                      v-model="platescoremodel.admincentermark"
                      placeholder="输入描述"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :offset="1" :span="11">
                  <div style="margin-top: 60px"></div>
                  <el-divider>
                    <h3 class="bold">当前价值-产业发展水平</h3>
                  </el-divider>

                  <el-form-item label="产业发展水平评分" prop="industrylevelscore">
                    <el-input
                      v-model="platescoremodel.industrylevelscore"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="产业发展水平描述" prop="industrylevelmark">
                    <el-input
                      v-model="platescoremodel.industrylevelmark"
                      type="textarea"
                      placeholder="请输入内容"
                      clearable
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="产业规模评分" prop="industryscalescore">
                    <el-input
                      v-model="platescoremodel.industryscalescore"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="产业规模描述" prop="industryscalemark">
                    <el-input
                      v-model="platescoremodel.industryscalemark"
                      placeholder="输入描述"
                      clearable
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="产业质量评分" prop="industryqualityscore">
                    <el-input
                      v-model="platescoremodel.industryqualityscore"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="产业质量描述" prop="industryqualitymark">
                    <el-input
                      v-model="platescoremodel.industryqualitymark"
                      placeholder="输入描述"
                      clearable
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="产业创新评分" prop="industryinnovatescore">
                    <el-input
                      v-model="platescoremodel.industryinnovatescore"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="产业创新描述" prop="industryinnovatemark">
                    <el-input
                      v-model="platescoremodel.industryinnovatemark"
                      placeholder="输入描述"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-divider style="margin-top: 30px">
                    <h3 class="bold">当前价值-人口发展水平</h3>
                  </el-divider>
                  <el-form-item label="人口发展水平是否展示" prop="peopledevelopisshow">
                    <!-- <el-input
                      v-model="platescoremodel.peopledevelopisshow  "
                      placeholder="输入分值"
                      clearable
                    > -->
                    <!-- </el-input> -->
                    <el-radio-group v-model="platescoremodel.peopledevelopisshow" class="ml-4">
                      <el-radio :value="0" size="large">不展示</el-radio>
                      <el-radio :value="1" size="large">展示</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="人口发展水平评分" prop="peopledeveloplevelscore">
                    <el-input
                      v-model="platescoremodel.peopledeveloplevelscore"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="人口发展水平描述" prop="peopledeveloplevelmark">
                    <el-input
                      v-model="platescoremodel.peopledeveloplevelmark"
                      type="textarea"
                      placeholder="请输入内容"
                      clearable
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="人口密度评分" prop="peopledensityscore">
                    <el-input
                      v-model="platescoremodel.peopledensityscore"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="需求来源评分" prop="demandsourcescore">
                    <el-input
                      v-model="platescoremodel.demandsourcescore"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-divider style="margin-top: 30px">
                    <h3 class="bold">未来价值-板块规划</h3>
                  </el-divider>
                  <el-form-item label="板块规划评分" prop="plateplanscore">
                    <el-input
                      v-model="platescoremodel.plateplanscore"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="板块规划描述" prop="plateplanmark">
                    <el-input
                      v-model="platescoremodel.plateplanmark"
                      type="textarea"
                      placeholder="请输入内容"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-divider style="margin-top: 30px">
                    <h3 class="bold">未来价值-产业规划</h3>
                  </el-divider>

                  <el-form-item label="产业规划评分" prop="industryplanscore">
                    <el-input
                      v-model="platescoremodel.industryplanscore"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="产业规划描述" prop="industryplanmark">
                    <el-input
                      v-model="platescoremodel.industryplanmark"
                      type="textarea"
                      placeholder="请输入内容"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-button :loading="loading" @click="cancel">取消</el-button>
              <el-button :loading="loading" type="primary" @click="platescoresubmit"
                >提交</el-button
              >
            </el-form>
          </el-scrollbar>
        </el-tab-pane>

        <el-tab-pane label="产业评测" name="产业信息" :disabled="dialogTitle === '添加'">
          <el-scrollbar native :height="600">
            <el-form
              ref="industryscoreref"
              :model="plateindustryscoremodel"
              label-width="180px"
              class="platscore"
            >
              <el-row>
                <el-col :span="11">
                  <el-form-item label="产业评测可见" prop="is_show">
                    <el-radio-group v-model="plateindustryscoremodel.is_show">
                      <el-radio :value="1">展示</el-radio>
                      <el-radio :value="0">不展示</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-divider>
                    <h3 class="bold">板块解读</h3>
                  </el-divider>

                  <el-form-item label="产业总分" prop="countscore">
                    <el-input
                      v-model="plateindustryscoremodel.interpretation_score"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="图片" prop="industryimg_url">
                    <!-- v-model="plateindustryscoremodel.platedetailimg_id" -->
                    <ImgUpload
                      source="5"
                      :isWatermark="plateindustryscoremodel.is_watermark"
                      v-model:file-url="plateindustryscoremodel.industryimg_url"
                      file-type="image"
                      :height="100"
                      upload
                    />
                  </el-form-item>
                  <el-form-item label="产业解读描述" prop="interpretation">
                    <el-input
                      v-model="plateindustryscoremodel.interpretation"
                      type="textarea"
                      placeholder="请输入内容"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-divider style="margin-top: 30px">
                    <h3 class="bold">当前价值</h3>
                  </el-divider>

                  <el-form-item label="产业规模评分" prop="scale_score">
                    <el-input
                      v-model="plateindustryscoremodel.scale_score"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="产业链数据">
                    <el-table :data="plateindustryscoremodel.industrylist" style="width: 100%">
                      <el-table-column prop="industry_name" label="产业链" width="160">
                        <template #default="scope">
                          <el-input
                            v-model="scope.row.industry_name"
                            placeholder="输入分值"
                            :readonly="true"
                          >
                          </el-input>
                        </template>
                      </el-table-column>
                      <el-table-column prop="company_num" label="数量" width="130">
                        <template #default="scope">
                          <el-input
                            v-model="scope.row.company_num"
                            placeholder="输入企业数量"
                            clearable
                          >
                          </el-input>
                        </template>
                      </el-table-column>
                      <el-table-column
                        prop="company_register_money"
                        label="注册资金（万元）"
                        width="150"
                      >
                        <template #default="scope">
                          <el-input
                            v-model="scope.row.company_register_money"
                            placeholder="输入注册资金"
                            clearable
                          >
                          </el-input>
                        </template>
                      </el-table-column>
                    </el-table>
                    <!-- <table  >
                    <tr>
                        <td>产业链</td>
                        <td>数量</td>
                        <td>注册资金（元）</td>

                    </tr>  
                      
                
                 
                  </table> -->
                  </el-form-item>

                  <el-form-item label="产业规模描述" prop="scale_score_desc">
                    <el-input
                      v-model="plateindustryscoremodel.scale_score_desc"
                      placeholder="输入描述"
                      type="textarea"
                      clearable
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="产业质量评分" prop="quality_score">
                    <el-input
                      v-model="plateindustryscoremodel.quality_score"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="板块企业总量" prop="company_total">
                    <el-input
                      v-model="plateindustryscoremodel.company_total"
                      placeholder="输入内容"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="上市公司数量" prop="listed_company_num">
                    <el-input
                      v-model="plateindustryscoremodel.listed_company_num"
                      placeholder="输入内容"
                      clearable
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="外资企业数量" prop="foreign_company_num">
                    <el-input
                      v-model="plateindustryscoremodel.foreign_company_num"
                      placeholder="输入内容"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="产业质量描述" prop="quality_desc">
                    <el-input
                      v-model="plateindustryscoremodel.quality_desc"
                      placeholder="输入描述"
                      type="textarea"
                      clearable
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="产业创新评分" prop="innovation_score">
                    <el-input
                      v-model="plateindustryscoremodel.innovation_score"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="产业创新数据" prop="industryinnovatelist">
                    <el-table
                      :data="plateindustryscoremodel.industryinnovatelist"
                      style="width: 100%"
                    >
                      <el-table-column prop="industry_name" label="产业链" width="180">
                        <template #default="scope">
                          <el-input
                            v-model="scope.row.industry_name"
                            placeholder="输入分值"
                            :readonly="true"
                          >
                          </el-input>
                        </template>
                      </el-table-column>
                      <el-table-column prop="company_num" label="数量" width="140">
                        <template #default="scope">
                          <el-input
                            v-model="scope.row.company_num"
                            placeholder="输入企业数量"
                            clearable
                          >
                          </el-input>
                        </template>
                      </el-table-column>
                      <el-table-column prop="hold_rate" label="比例">
                        <template #default="scope">
                          <el-input v-model="scope.row.hold_rate" placeholder="输入比例" clearable>
                          </el-input>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-form-item>

                  <el-form-item label="产业创新描述" prop="innovation_desc">
                    <el-input
                      v-model="plateindustryscoremodel.innovation_desc"
                      placeholder="输入描述"
                      type="textarea"
                      clearable
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="产业态势评分" prop="situation_score">
                    <el-input
                      v-model="plateindustryscoremodel.situation_score"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="2023年注册企业数量" prop="register_company_num">
                    <el-input
                      v-model="plateindustryscoremodel.register_company_num"
                      placeholder="输入描述"
                      clearable
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item label="2023年注册企业数占比" prop="register_company_rate">
                    <el-input
                      v-model="plateindustryscoremodel.register_company_rate"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="产业态势描述" prop="situation_desc">
                    <el-input
                      v-model="plateindustryscoremodel.situation_desc"
                      placeholder="输入描述"
                      type="textarea"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :offset="1" :span="11">
                  <div style="margin-top: 60px"></div>

                  <el-form-item label="产业环境评分" prop="environment_score">
                    <el-input
                      v-model="plateindustryscoremodel.environment_score"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="产业环境描述" prop="environment_desc">
                    <el-input
                      v-model="plateindustryscoremodel.environment_desc"
                      type="textarea"
                      placeholder="请输入内容"
                      clearable
                    >
                    </el-input>
                  </el-form-item>

                  <el-divider style="margin-top: 30px">
                    <h3 class="bold">未来价值-产业规划</h3>
                  </el-divider>

                  <el-form-item label="产业规划评分" prop="planning_score">
                    <el-input
                      v-model="plateindustryscoremodel.planning_score"
                      placeholder="输入分值"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="空间规划描述" prop="space_planning_desc">
                    <el-input
                      v-model="plateindustryscoremodel.space_planning_desc"
                      placeholder="请输入内容"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="龙头企业带动描述" prop="leading_company_desc">
                    <el-input
                      v-model="plateindustryscoremodel.leading_company_desc"
                      placeholder="请输入内容"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="高潜力企业描述" prop="high_company_desc">
                    <el-input
                      v-model="plateindustryscoremodel.high_company_desc"
                      placeholder="请输入内容"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item label="产业规划描述" prop="planning_desc">
                    <el-input
                      v-model="plateindustryscoremodel.planning_desc"
                      type="textarea"
                      placeholder="请输入内容"
                      clearable
                    >
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-button :loading="loading" @click="cancel">取消</el-button>
              <el-button :loading="loading" type="primary" @click="plateIndustryscoresubmit"
                >提交</el-button
              >
            </el-form>
          </el-scrollbar>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, onMounted, nextTick, h } from 'vue'
import checkPermission from '@/utils/permission'

import screenHeight from '@/utils/screen-height'
import Pagination from '@/components/Pagination/index.vue'
import { getPageLimit } from '@/utils/settings'
import * as Plate from '@/api/project/plate'
import { customformat } from '@/utils/dateUtil'
import ExcelImport from '@/components/ExcelImport/index.vue'

import { evaluatingShowList, evaluatingStateList } from '@/mapping/selectOption'

const handleChange = (value) => {
  console.log(value)
}

const { proxy } = getCurrentInstance() //获取上下文实例，proxy=vue2的this

const relatedPlate = ref([])
const loading = ref(true)
const data = ref([])
const height = ref(680)
const query = ref({
  page: 1,
  limit: getPageLimit(),
  name: '', // 板块名称
  countyid: 0, // 所属板块
  is_show: 0, // 评测是否展示
  state: -1, // 发布状态
  date_value: ['', '']
})
const count = ref(0)
const idkey = ref('id')

const dialog = ref(false)
const dialogTitle = ref('')

//板块基础信息
const modelReset = {
  id: 0,
  name: '',
  region_pid: [],
  coordinate: '',
  fences: '',
  sort: 2250,
  plateimg_id: 0,
  related_plate: [],
  plateimg_url: '',
  viewnum: 0,
  is_watermark: 1,
  source: 5
}
const model = ref({ ...modelReset })
//板块评测数据
const platescoremodelReset = {
  id: 0,
  plateid: 0,
  scoreshow: 0,
  countscore: 0,
  platemark: '',
  livehelpscore: 0,
  livehelpmark: '',
  developscore: 0,
  developmark: '',
  eduresourcescore: 0,
  eduresourcemark: '',
  trafficeasyscore: 0,
  trafficeasymark: '',
  matchexpendscore: 0,
  matchexpendmark: '',
  medicalsourcescore: 0,
  medicalsourcemark: '',
  scenicspotscore: 0,
  scenicspotmark: '',
  hubnessscore: 0,
  hubnessmark: '',
  admincenterscore: 0,
  admincentermark: '',
  industrylevelscore: 0,
  industrylevelmark: '',
  industryscalescore: 0,
  industryscalemark: '',
  industryqualityscore: 0,
  industryqualitymark: '',
  industryinnovatescore: 0,
  industryinnovatemark: '',
  peopledeveloplevelscore: 0,
  peopledeveloplevelmark: '',
  peopledensityscore: 0,
  demandsourcescore: 0,
  plateplanscore: 0,
  plateplanmark: '',
  industryplanscore: 0,
  industryplanmark: '',
  platedetailimg_id: 0,
  platedetailimg_url: '',
  peopledevelopisshow: 0,
  is_watermark: 0
}
const platescoremodel = ref({ ...platescoremodelReset })

//产业评测数据
const plateindustryscoremodelReset = { is_show: 0, interpretation_score: 0 }
const plateindustryscoremodel = ref({ ...plateindustryscoremodelReset })

const trees = ref([])

const currentTab = ref('基础信息')
const cascaderprops = {
  expandTrigger: 'click',
  checkStrictly: true,
  value: 'region_id',
  label: 'region_name',
  emitPath: false,
  multiple: true
}
const searchcascaderprops = {
  expandTrigger: 'click',
  checkStrictly: true,
  value: 'region_id',
  label: 'region_name',
  emitPath: false
}
const validateDecimal = (rule, value, callback) => {
  const regex = /^\d+(\.\d+)?$/
  if (!value || !regex.test(value)) {
    return callback(new Error('请输入有效的小数'))
  }
  callback()
}
const rules = {
  lat: [
    { required: true, message: '纬度不能为空', trigger: ['blur', 'change'] },
    { validator: validateDecimal, trigger: ['blur', 'change'] }
  ],
  name: [{ required: true, message: '板块名称不能为空', trigger: ['blur', 'change'] }],
  region_pid: [{ required: true, message: '所属区域不能为空', trigger: ['blur', 'change'] }],

  lont: [
    { required: true, message: '经度不能为空', trigger: ['blur', 'change'] },
    { validator: validateDecimal, trigger: ['blur', 'change'] }
  ]
}
let mapContaniner = null
let markerGeo = null
let multiMarker = null
let multiPolygon = null
// 初始化地图
function initMap() {
  nextTick(() => {
    mapContaniner = new TMap.Map(document.getElementById('mapContaniner'), {
      zoom: 13
    })
  })
}

function destroy() {
  mapContaniner?.destroy()
  mapContaniner = null
  markerGeo = null
  multiMarker && multiMarker.remove(['center'])
  multiMarker = null
  multiPolygon && multiPolygon.remove(['polygon-layer'])
  multiPolygon = null
}

function createdPolygon() {
  const path = model.value.fences.split(';').map((i) => {
    const [latitude, longitude] = i.split(',')
    return new TMap.LatLng(latitude, longitude)
  })
  multiPolygon = new TMap.MultiPolygon({
    id: 'polygon-layer', // 图层id
    map: mapContaniner, // 显示多边形图层的底图
    styles: {
      // 多边形的相关样式
      polygon: new TMap.PolygonStyle({
        color: 'rgba(254, 112, 2, 0.5)', // 面填充色
        showBorder: false // 是否显示拔起面的边线
        // borderColor: '#00FFFF' // 边线颜色
      })
    },
    geometries: [
      {
        id: 'polygon', // 多边形图形数据的标志信息
        styleId: 'polygon', // 样式id
        paths: path, // 多边形的位置信息
        properties: {
          // 多边形的属性数据
          title: 'polygon'
        }
      }
    ]
  })
}
function setCenter(lat, lng) {
  const center = new TMap.LatLng(lat, lng)
  mapContaniner.setCenter(center)
}

function setMarker() {
  if (!markerGeo) {
    markerGeo = {
      id: 'center',
      position: mapContaniner.getCenter(),
      styleId: 'build_address'
      // content: '当前中心点'
    }
  } else {
    markerGeo.position = mapContaniner.getCenter()
    // markerGeo.content =
  }
  if (!multiMarker) {
    multiMarker = new TMap.MultiMarker({
      map: mapContaniner,
      geometries: [markerGeo],
      styles: {
        build_address: new TMap.MarkerStyle({
          direction: 'bottom',
          opacity: 0
        })
      }
    })
  } else {
    multiMarker.updateGeometries([markerGeo])
  }
}
function updateCenter() {
  mapContaniner.on(
    'center_changed',
    (e) => {
      setMarker()
      model.value.coordinate = `${e.center.lat},${e.center.lng}`
    },
    false
  )
}
function handleSetCenter(off = false) {
  if (!off) {
    destroy()
    initMap()
  }
  if (!model.value.fences) {
    return
  }
  if (off) {
    createdPolygon()
    const [latitude, longitude] = model.value.coordinate.split(',')
    setCenter(latitude, longitude)
    setMarker()
    updateCenter()
    return
  }
  Plate.getCenterpoint({ fences: model.value.fences }).then((res) => {
    model.value.coordinate = res.data.centerpoint
    const [latitude, longitude] = model.value.coordinate.split(',')
    createdPolygon()
    setCenter(latitude, longitude)
    setMarker()
    updateCenter()
  })
}

// 推送
function handlePhsuMessage(plateid) {
  Plate.sendPlateMessage({ plateid }).then((res) => {
    ElMessage.success(res.msg)
  })
}
function add() {
  dialog.value = true
  dialogTitle.value = '添加'
  currentTab.value = '基础信息'
  reset(modelReset)
  scorereset(platescoremodelReset)
  initMap()
}
function platescoresubmit() {
  loading.value = true
  if (platescoremodel.value['id']) {
    Plate.scoreedit(platescoremodel.value)
      .then((res) => {
        if (res.code) {
          list()
        }
        // dialog.value = false
        ElMessage.success(res.msg)
        // reset(modelReset)
        // scorereset(platescoremodelReset)
        // currentTab.value = '基础信息'
        // destroy()
      })
      .catch(() => {
        loading.value = false
      })
  } else {
    Plate.scoreadd(platescoremodel.value)
      .then((res) => {
        if (res.code) {
          list()
        }
        // dialog.value = false
        ElMessage.success(res.msg)
        // reset(modelReset)
        // scorereset(platescoremodelReset)
        // destroy()
        // currentTab.value = '基础信息'
      })
      .catch((error) => {
        console.log(error)
        // destroy()
        loading.value = false
      })
  }
}
function plateIndustryscoresubmit() {
  loading.value = true

  Plate.industryScoryEdit(plateindustryscoremodel.value)
    .then((res) => {
      if (res.code) {
        list()
      }
      // dialog.value = false
      ElMessage.success(res.msg)
      // reset(modelReset)
      // scorereset(platescoremodelReset)
      // currentTab.value = '基础信息'
      // destroy()
    })
    .catch(() => {
      loading.value = false
      // destroy()
    })
}
function submit() {
  proxy.$refs['ref'].validate((valid) => {
    if (!valid) {
      return
    }
    loading.value = true
    if (model.value[idkey.value]) {
      Plate.edit(model.value)
        .then((res) => {
          if (res.code) {
            list()
          }

          // dialog.value = false
          ElMessage.success(res.msg)
          // destroy()
        })
        .catch(() => {
          loading.value = false
          // destroy()
        })
    } else {
      Plate.add(model.value)
        .then((res) => {
          if (res.code) {
            list()
          }
          // dialog.value = false
          ElMessage.success(res.msg)
          // destroy()
        })
        .catch((error) => {
          console.log(error)
          loading.value = false
          // destroy()
        })
    }
  })
}

function list() {
  loading.value = true
  Plate.list(query.value)
    .then((res) => {
      data.value = res.data.list
      count.value = res.data.count
      data.value.is_watermark = 1
      loading.value = false
    })
    .catch((error) => {
      loading.value = false
    })
}
function edit(row) {
  currentTab.value = '基础信息'
  dialog.value = true
  dialogTitle.value = row.name + ' 编辑：(' + row[idkey.value] + ')'
  initMap()
  platescoremodel.value.plateid = row[idkey.value]
  var id = {}
  id[idkey.value] = row[idkey.value]

  Plate.info(id)
    .then((res) => {
      res.data.is_watermark = 1
      reset(res.data)
      nextTick(() => {
        handleSetCenter(true)
      })
    })
    .catch(() => {})
  //板块评分数据
  Plate.scoreinfo(id)
    .then((res) => {
      if (Object.keys(res.data).length === 0) {
        scorereset({ ...platescoremodelReset, plateid: row[idkey.value] })
      } else {
        scorereset({ ...res.data, plateid: row[idkey.value] })
      }
    })
    .catch((error) => {
      console.log(error)
    })
  //产业评分数据
  Plate.getIndustryInfo({ plateid: row[idkey.value] })
    .then((res) => {
      if (Object.keys(res.data).length === 0) {
        industryreset({ ...plateindustryscoremodelReset, plateid: row[idkey.value] })
      } else {
        industryreset({ ...res.data, plateid: row[idkey.value] })
      }
    })
    .catch((error) => {
      console.log(error)
    })
}

function cancel() {
  dialog.value = false
  currentTab.value = '基础信息'
  destroy()
}

// 刷新
function refresh() {
  list()
}

onMounted(() => {
  height.value = screenHeight(310)
  list()
  getPlateList()
  getRegionList()
})
function renderHeader({ column }) {
  let templable = column.label.split('<br />')
  return h('span', {}, [h('span', {}, templable[0]), h('br'), h('span', {}, templable[1])])
}
//搜索
const queryRef = ref(null)
function resetQuery() {
  query.value.priceState = ''
  query.value.evaluating = ''
  queryRef.value.resetFields()
  handleQuery()
}

// 重置
function reset(row) {
  console.log(row)
  model.value = row

  if (proxy.$refs['ref'] !== undefined) {
    try {
      proxy.$refs['ref'].resetFields()
      proxy.$refs['ref'].clearValidate()
    } catch (error) {
      console.log(error)
    }
  }
}
// 重置评分数据
function scorereset(row) {
  platescoremodel.value = row
  if (proxy.$refs['scoreref'] !== undefined) {
    try {
      proxy.$refs['scoreref'].resetFields()
      proxy.$refs['scoreref'].clearValidate()
    } catch (error) {
      console.log(error)
    }
  }
}

// 重置产业评分数据
function industryreset(row) {
  plateindustryscoremodel.value = row
  console.log(plateindustryscoremodel.value)
  /* if (proxy.$refs['industryscoreref'] !== undefined) {
    try {
      proxy.$refs['industryscoreref'].resetFields()
      proxy.$refs['industryscoreref'].clearValidate()
    } catch (error) {
      console.log(error)
    }
  } */
}
function getRegionList() {
  Plate.getRegionList()
    .then((res) => {
      trees.value = res.data
    })
    .catch((error) => {
      console.log(error)
    })
}

function getPlateList() {
  Plate.getPlateList()
    .then((res) => {
      relatedPlate.value = res.data
    })
    .catch((error) => {
      console.log(error)
    })
}
function handleQuery() {
  query.value.pageNum = 1
  list()
}
//搜索end
function publishstate(row) {
  ElMessageBox.confirm('确定要发布吗?', '发布板块', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      Plate.changestage({ id: row.id, state: 1 })
        .then((res) => {
          if (res.code == 200) {
            list()
          }

          ElMessage({
            type: 'success',
            message: res.msg
          })
        })
        .catch(() => {})
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消发布'
      })
    })
}
function downloadstate(row) {
  ElMessageBox.confirm('确定要下架吗?', '下架板块', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      Plate.changestage({ id: row.id, state: 2 })
        .then((res) => {
          if (res.code == 200) {
            list()
          }

          ElMessage({
            type: 'success',
            message: res.msg
          })
        })
        .catch(() => {})
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消'
      })
    })
}
// 导入，results数据，header表头
function imports({ results, header }) {
  loading.value = true
  Plate.platescoreimports({
    import: results
  })
    .then((res) => {
      list()
      ElMessage.success(res.msg)
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}
function idustryimports({ results, header }) {
  loading.value = true
  Plate.importIndustryScore({
    import: results
  })
    .then((res) => {
      list()
      ElMessage.success(res.msg)
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}
function dowonloadExcel() {
  loading.value = true
  import('@/components/ExcelExport/index').then((excel) => {
    const header = [
      { name: '板块名' },
      { countscore: '板块总分' },
      { platemark: '板块解读描述' },
      { livehelpscore: '生活便利度评分' },
      { livehelpmark: '生活便利度描述' },
      { developscore: '开发程度评分' },
      { developmark: '开发程度描述' },
      { eduresourcescore: '教育资源评分' },
      { eduresourcemark: '教育资源描述' },
      { trafficeasyscore: '交通便利度评分' },
      { trafficeasymark: '交通便利度描述' },
      { matchexpendscore: '配套消费评分' },
      { matchexpendmark: '配套消费描述' },
      { medicalsourcescore: '医疗资源评分' },
      { medicalsourcemark: '医疗资源描述' },
      { scenicspotscore: '景点评分' },
      { scenicspotmark: '景点描述' },
      { hubnessscore: '枢纽度评分' },
      { hubnessmark: '枢纽度描述' },
      { admincenterscore: '行政中心评分' },
      { admincentermark: '行政中心描述' },
      { industrylevelscore: '产业发展水平评分' },
      { industrylevelmark: '产业发展水平描述' },
      { industryscalescore: '产业规模评分' },
      { industryscalemark: '产业规模描述' },
      { industryqualityscore: '产业质量评分' },
      { industryqualitymark: '产业质量描述' },
      { industryinnovatescore: '产业创新评分' },
      { industryinnovatemark: '产业创新描述' },
      { peopledevelopisshow: '人口发展水平是否展示' },
      { peopledeveloplevelscore: '人口发展水平评分' },
      { peopledeveloplevelmark: '人口发展水平描述' },
      { peopledensityscore: '人口密度评分' },
      { demandsourcescore: '需求来源评分' },
      { plateplanscore: '板块规划评分' },
      { plateplanmark: '板块规划描述' },
      { industryplanscore: '产业规划评分' },
      { industryplanmark: '产业规划描述' }
    ]
    excel.excelExport([], header, '板块评测导入模版下载', 'xlsx', false)
  })
  loading.value = false
}

function dowonloadIndustryExcel() {
  console.log('sssss')

  loading.value = true
  import('@/components/ExcelExport/index').then((excel) => {
    Plate.downloadIndustryTemplate({})
      .then((res) => {
        /* const mergeRowCol = [
        {s:{r:1,c:0},e:{r:19,c:0}},
        {s:{r:1,c:1},e:{r:19,c:1}},
        {s:{r:1,c:2},e:{r:19,c:2}},
        {s:{r:1,c:32},e:{r:19,c:3}},
      ]; */
        let mergeRowCol = []
        for (var i = 0; i < 23; i++) {
          mergeRowCol.push({ s: { r: 1, c: i }, e: { r: 19, c: i } })
        }

        excel.excelExport(
          res.data.data,
          res.data.header,
          '产业评测导入模版下载',
          'xlsx',
          true,
          true,
          mergeRowCol
        )
        loading.value = false
      })
      .catch((e) => {
        loading.value = false
      })
  })
  loading.value = false
}
function customcloseDialog() {
  dialog.value = false
  currentTab.value = '基础信息'
  destroy()
}
</script>
<style>
/* .popper-select {
  li[aria-haspopup='true'] {
    .el-checkbox {
      display: none;
    }

    .el-radio {
      display: none;
    }
  }
} */
</style>
<style lang="scss" scoped>
.popper-select {
  li[aria-haspopup='true'] {
    .el-checkbox {
      display: none;
    }

    .el-radio {
      display: none;
    }
  }
}

.custom-dialog {
  :deep(.el-form-item) {
    width: 650px;
  }

  :deep(.el-input) {
    width: 450px;
  }

  :deep(.el-select) {
    width: 450px;
  }

  :deep(.el-textarea) {
    width: 450px;
  }

  :deep(.el-divider) {
    margin: 30px 0;
  }
}

#mapContaniner {
  width: 450px;
  height: 250px;
}
</style>
