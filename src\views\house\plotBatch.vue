<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-form ref="queryRef" :model="query" :inline="true" label-width="90px">
      <el-form-item label="批次：" prop="batch">
        <el-input v-model="query.batch" placeholder="请输入名称" style="width: 200px" />
      </el-form-item>
      <el-form-item label="下线状态：" prop="status">
        <el-select v-model="query.status" placeholder="请选择" style="width: 160px">
          <el-option label="全部" value="" />
          <el-option label="下线" :value="2" />
          <el-option label="上线" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="时间类型：" prop="timeType">
        <el-select v-model="query.timeType" placeholder="请选择" style="width: 160px">
          <el-option label="创建时间" value="create_time" />
          <el-option label="更新时间" value="update_time" />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="dateRange">
        <el-date-picker
          v-model="query.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 300px"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
        <el-button type="success" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      style="width: 100%; margin-top: 20px"
      row-key="id"
      @row-click="handleRowClick"
      :highlight-current-row="true"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="ID" width="60" sortable />
      <el-table-column prop="batch" label="批次" min-width="180" />
      <el-table-column prop="name" label="批次名称" min-width="180" />
      <el-table-column prop="year" label="年限" width="120" />
      <el-table-column prop="created_at" label="创建时间" width="180" sortable />
      <el-table-column prop="updated_at" label="更新时间" width="180" sortable />
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <el-button
            link
            :type="row.status === 1 ? 'success' : 'danger'"
            @click.stop="handleStatusChange(row)"
          >
            {{ row.status === 1 ? '下线' : '上线' }}
          </el-button>
          <el-button link type="primary" @click.stop="handleEdit(row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      v-model:total="total"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="getList"
    />

    <!-- 弹窗表单 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" destroy-on-close>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="批次" prop="batch">
          <el-input v-model="form.batch" placeholder="请输入批次" />
        </el-form-item>
        <el-form-item label="批次名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入批次名称" />
        </el-form-item>
        <el-form-item label="年限" prop="year">
          <el-input-number v-model="form.year" :min="2000" :max="2100" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">上线</el-radio>
            <el-radio :label="2">下线</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading"> 确 定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'
import Pagination from '@/components/Pagination/index.vue'

const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const dialogVisible = ref(false)
const dialogTitle = ref('新增批次')
const submitLoading = ref(false)
const queryRef = ref(null)
const formRef = ref(null)
const currentRow = ref(null)

// 查询参数
const query = reactive({
  page: 1,
  limit: 10,
  batch: '',
  status: '',
  timeType: 'create_time',
  dateRange: []
})

// 表单数据
const form = reactive({
  id: undefined,
  batch: '',
  name: '',
  year: new Date().getFullYear(),
  status: 1,
  remark: ''
})

// 表单校验规则
const rules = {
  batch: [
    { required: true, message: '请输入批次', trigger: 'blur' },
    { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入批次名称', trigger: 'blur' },
    { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
  ],
  year: [{ required: true, message: '请输入年限', trigger: 'change' }]
}

const getList = async () => {
  loading.value = true
  try {
    // 处理查询参数
    const params = {
      page: query.page,
      limit: query.limit,
      batch: query.batch || undefined,
      status: query.status === '' ? undefined : query.status
    }

    // 处理日期范围
    if (query.dateRange && query.dateRange.length === 2) {
      // 根据选择的时间类型确定字段名
      const timeField = query.timeType === 'create_time' ? 'created_at' : 'updated_at'
      params.start_time = query.dateRange[0]
      params.end_time = query.dateRange[1]
      params.time_field = timeField
    }

    const res = await request({
      url: '/admin/house.PlotBatch/list',
      method: 'get',
      params
    })

    if (res.code === 200) {
      tableData.value = res.data.list || []
      total.value = res.data.count || 0
      // 不在这里修改 query.page，让 Pagination 组件通过 v-model 控制
    } else {
      ElMessage.error(res.msg || '获取列表数据失败')
    }
  } catch (error) {
    console.error('获取列表失败:', error)
    ElMessage.error('获取列表数据失败')
  } finally {
    loading.value = false
  }
}

// 3. 搜索处理函数，确保搜索时重置到第一页
const handleQuery = () => {
  console.log('执行搜索，重置为第一页')
  query.page = 1
  getList()
}

// 4. 重置查询函数，明确重置页码
const resetQuery = () => {
  queryRef.value?.resetFields()
  console.log('重置查询条件，设置页码为1')
  query.page = 1
  query.limit = 10
  query.dateRange = []
  getList()
}

// 处理行点击
const handleRowClick = (row) => {
  currentRow.value = row
}

// 添加
const handleAdd = () => {
  resetForm()
  dialogTitle.value = '新增批次'
  dialogVisible.value = true
}

// 编辑 - 修改后直接使用行数据，不再发起请求
const handleEdit = (row) => {
  resetForm()
  dialogTitle.value = '编辑批次'

  // 直接使用表格行数据填充表单
  Object.assign(form, {
    id: row.id,
    batch: row.batch,
    name: row.name,
    year: row.year,
    status: row.status,
    remark: row.remark || ''
  })

  dialogVisible.value = true
}

// 状态变更
const handleStatusChange = async (row) => {
  try {
    const newStatus = row.status === 1 ? 2 : 1
    const actionText = newStatus === 1 ? '上线' : '下线'

    // 如果是要上线且 plot_count 为 0，则进行二次确认
    if (newStatus === 1 && row.plot_count === 0) {
      try {
        // 先进行常规确认
        await ElMessageBox.confirm(`确定要${actionText}该批次吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 再进行没有关联土拍专题的二次确认
        await ElMessageBox.confirm('该批次没有关联的土拍专题，是否上架？', '提示', {
          confirmButtonText: '确定上架',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 用户通过两次确认后，调用接口进行上架操作
        const res = await request({
          url: '/admin/house.PlotBatch/changeStatus',
          method: 'post',
          data: {
            id: row.id,
            status: newStatus
          }
        })

        if (res.code === 200) {
          ElMessage.success(`${actionText}成功`)
          getList()
        } else {
          ElMessage.error(res.msg || `${actionText}失败`)
        }
      } catch (error) {
        // 用户取消了操作，不做任何处理
        if (error !== 'cancel') {
          console.error('状态变更失败:', error)
        }
      }
    } else {
      // 其他情况（下线或有关联土拍专题的上线）只需要一次确认
      try {
        await ElMessageBox.confirm(`确定要${actionText}该批次吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const res = await request({
          url: '/admin/house.PlotBatch/changeStatus',
          method: 'post',
          data: {
            id: row.id,
            status: newStatus
          }
        })

        if (res.code === 200) {
          ElMessage.success(`${actionText}成功`)
          getList()
        } else {
          ElMessage.error(res.msg || `${actionText}失败`)
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('状态变更失败:', error)
        }
      }
    }
  } catch (error) {
    console.error('状态变更处理失败:', error)
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    // 确保状态是数字类型
    const data = {
      ...form,
      status: Number(form.status)
    }

    const res = await request({
      url: '/admin/house.PlotBatch/save',
      method: 'post',
      data
    })

    if (res.code === 200) {
      ElMessage.success(form.id ? '编辑成功' : '添加成功')
      dialogVisible.value = false
      query.page = 1
      getList()
    } else {
      ElMessage.error(res.msg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  form.id = undefined
  form.batch = ''
  form.year = new Date().getFullYear()
  form.status = 2
  form.remark = ''

  // 如果表单已经挂载，重置验证
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 监听对话框关闭
watch(dialogVisible, (val) => {
  if (!val) {
    resetForm()
  }
})

// 初始化
onMounted(() => {
  getList()
})
</script>

<style scoped>
.detail-popup {
  position: absolute;
  top: 200px;
  right: 20px;
  width: 300px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #ebeef5;
  z-index: 100;
}

.detail-header {
  background-color: #ffd04b;
  color: #333;
  padding: 10px 15px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  font-weight: bold;
}

.detail-content {
  padding: 15px;
}

.detail-footer {
  text-align: right;
  border-top: 1px solid #ebeef5;
  padding-top: 10px;
  margin-top: 10px;
}

/* 自定义行样式 */
:deep(.el-table__row.current-row) {
  background-color: #fdf6ec;
}

:deep(.el-table__row.current-row td:first-child) {
  border-left: 3px solid #e6a23c;
}
</style>
