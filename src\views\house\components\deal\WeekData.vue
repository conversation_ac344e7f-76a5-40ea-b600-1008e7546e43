<template>
  <div class="app-container">
    <!-- Query -->
	<el-form ref="formRef" :model="query" :inline="true" label-width="90px">
		<el-form-item label="查询区县">
		<el-select v-model="query.regions" class="w-full ya-search-value" multiple clearable filterable >
		  <el-option
		      v-for="item in regionsData"
		      :key="item.key"
		      :label="item.value"
		      :value="item.key"
		  />
		</el-select>
		  
		</el-form-item>
		<el-form-item>
		  <el-select v-model="query.date_field" class="ya-date-field" placeholder="时间类型">
		    <el-option value="dealDate" label="项目周期" />
		    <!-- <el-option value="update_time" label="修改时间" /> -->
		  </el-select>
		</el-form-item>
		<el-form-item>
		  <el-date-picker
		    v-model="query.date_value"
		    type="daterange"
		    class="ya-date-value"
		    start-placeholder="开始日期"
		    end-placeholder="结束日期"
		    value-format="YYYY-MM-DD"
		    :default-time="[new Date(2024, 1, 1), new Date(2024, 1, 1)]"
		  />
		</el-form-item>
		<el-form-item label="发布状态">
		<el-select v-model="query.release_status" class="w-full ya-search-value" clearable filterable >
		  <el-option
		      v-for="item in releaseStatus"
		      :key="item.key"
		      :label="item.value"
		      :value="item.key"
		  />
		</el-select>
		</el-form-item>
		<el-form-item>
		  <el-button type="primary" title="搜索" @click="list()">搜索</el-button>
		  <el-button @click="refresh()"><svg-icon icon-class="refresh" /></el-button>
		  <excel-import title="导入" @on-import="imports" />
		</el-form-item>
	</el-form>
    <!-- List -->
    <el-table ref="table" v-loading="loading" :data="data" :height="height" @sort-change="sort"
      @selection-change="select">
      <el-table-column type="selection" width="42" title="全选/反选" />
      <el-table-column :prop="idkey" label="ID" width="60"/>
      <el-table-column prop="create_time" label="所属周期" width="130">
	  <template #default="scope">
	      <span v-text="scope.row.start_date+'-'+scope.row.end_date"></span>
	    </template>
	  </el-table-column>
      <el-table-column prop="region_name" label="区县" min-width="70"/>
      <el-table-column prop="deal_count" label="成交套数（套）" min-width="120" />
      <el-table-column prop="deal_area" label="成交面积（万㎡）" min-width="120"/>
      <el-table-column prop="deal_price" label="成交价格（万元/㎡）" min-width="120" />
      <el-table-column prop="deal_amount" label="成交金额（亿元）" min-width="120"/>
      <el-table-column prop="deal_unit_price" label="成交套均总价（万元/套）" min-width="120"/>
      <el-table-column prop="release_status" label="发布状态" min-width="120">
	  <template #default="scope">
	      <span v-if="scope.row.release_status == 0" v-text="'未发布'"></span>
	      <span v-else-if="scope.row.release_status == 1" v-text="'已发布'"></span>
	      <span v-else v-text="'已下架'"></span>
	    </template>
	  </el-table-column>
	  <el-table-column prop="create_time" label="创建时间" min-width="120"/>
      <el-table-column label="操作" width="130">
        <template #default="scope">
          <el-link type="primary" class="mr-1" :underline="false" @click="scanDetails(scope.row)">
            查看
          </el-link>
          <el-link v-if="scope.row.release_status != 1" type="primary" :underline="false" @click="changeStateButton(scope.row,'up')">
            发布
          </el-link>
          <el-link v-else-if="scope.row.release_status == 1" type="primary" :underline="false" @click="changeStateButton(scope.row,'down')">
            下架
          </el-link>
		  <!-- &nbsp; -->
		  <!-- <el-link type="primary" :underline="false" @click="selectOpen(scope.row)">
		    删除
		  </el-link> -->
        </template>
      </el-table-column>
    </el-table>
    <!-- Pagination -->
    <pagination v-show="count > 0" v-model:total="count" v-model:page="query.page" v-model:limit="query.limit"
      @pagination="list" />
    <!-- Add/Edit Dialog -->
    <el-dialog
      v-model="dialog"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="cancel"
      width="80%"
      top="2vh"
      destroy-on-close
    >
      <el-tabs>
        <el-tab-pane
          label="楼盘周度成交"
          lazy
        >
          <WeekDetailList :region="rowInfo" />
        </el-tab-pane>
      </el-tabs>
	  <template #footer>
	    <el-button :loading="loading" @click="cancel">取消</el-button>
	    <el-button :loading="loading" type="primary" @click="cancel">确定</el-button>
	  </template>
    </el-dialog>
  </div>
</template>
<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getList,getRegionList,monthDataExport,deleteItems,changeState } from '@/api/project/housedealdata'
import screenHeight from '@/utils/screen-height'
import Pagination from '@/components/Pagination/index.vue'
import { arrayColumn } from '@/utils/index'
import { getPageLimit } from '@/utils/settings'
// import ExcelImport from '@/components/ExcelImport/indexnew.vue'
import WeekhDetailList from './WeekDetailList.vue'
export default {
  name: '楼盘月度成交',
  components: { Pagination },
  setup() {
    const idkey = 'id'
    const height = 680
    const loading = ref(false)
    const dialog = ref(false)
    const dialogTitle = ref('')
    const data = ref([])
    const count = ref(0)
    const formRef = ref(null)
    const table = ref(null)
    const selectedItems = ref([])
    const regionsData = ref([])
	const releaseStatus = ref([{"key":0,"value":"未发布"},{"key":1,"value":"已发布"},{"key":2,"value":"已下架"}])
    const query = ref({
      page: 1,
      limit: 20,
      type: "week",
	  release_status: null,
	  regions: [],
      date_field: 'dealDate',
      date_value: [null, null],
      sort: ''
    })
	const rowInfo = ref({})
   
    const search = () => {
      query.page = 1;
      const params = {
        page: query.page,
        limit: query.limit,
        type: query.type,
        release_status: query.release_status,
        regions: query.regions,
        // name: query.search_value,
        // pageNum: query.page,
        // startDate: query.date_value[0],
        // endDate: query.date_value[1]
      };

      search(params);
    }

    const refresh = () => {
      if (formRef.value) {
        formRef.value.resetFields()
      }
      list()
    }

    const list = async () => {
      loading.value = true;
      try {
        const params = query.value
        console.log(params)
        const res = await getList(params);
        if (res && res.data) {
          data.value = Array.isArray(res.data.list) ? res.data.list : [];
          count.value = res.data.count || 0;
        } else {
          data.value = [];
          count.value = 0;
          console.error('Invalid response format:', res);
        }
        await nextTick();
      } catch (error) {
      
        ElMessage.error('获取列表失败');
        data.value = [];
        count.value = 0;
      } finally {
        loading.value = false;
      }
    };
	
	const regionlist = async () => {
	  loading.value = true;
	  try {
	    const params = {}
	    const res = await getRegionList(params);
	    if (res && res.data) {
			regionsData.value = res.data
	    } else {
	      ElMessage.error('获取城区列表失败');
	    }
	  } catch (error) {
	    ElMessage.error('获取列表异常');
	  } finally {
	    loading.value = false;
	  }
	}

    const sort = ({ prop, order }) => {
      query.sort = order ? `${prop},${order}` : ''
      list()
    }

    const select = (selection) => {
      selectedItems.value = selection
    }

    const scanDetails = (row) => {
		rowInfo.value = row
		dialog.value = true
		dialogTitle.value = row.region_name + ' 详情：(' + row.start_date + '~'+ row.end_date +')'
    }

    const cancel = () => {
      dialog.value = false
      if (formRef.value) {
        formRef.value.resetFields()
      }
    }

    const selectOpen = (row) => {
		
		if (!row) {
			return ElMessage.warning('请选择要操作的数据')
		}
      
		ElMessageBox.confirm('确定要删除数据吗？', '提示', {
		  type: 'warning'
		}).then(() => {
		  deleteSelected(row)
		}).catch(() => { })
      
    }

    const deleteSelected = async (row) => {
      loading.value = true
      try {
		const params = {
			"region_name":row.region_name,
			"year":row.year,
			"week":row.week,
			"type":query.value.type,
		}
        await deleteItems(params)
        ElMessage.success('删除成功')
        list()
      } catch (error) {
        ElMessage.error('删除失败')
      } finally {
        loading.value = false
      }
    }
    const changeStateButton = async (row,opttype)=>{
		var release_status = 1;
	  if(opttype == "up"){
		  var msg = "发布"
		  if(row.release_status==1){
			ElMessage.error('已经是发布状态')
			return
		  }
	  }else{
		var msg = "下架"  
		if(row.release_status!=1){
			ElMessage.error('已经是下架状态')
			return
		}
		release_status = 2;
	  }
      loading.value = true
	  ElMessageBox.confirm('确定要'+msg+'数据吗？', '提示', {
	    type: 'warning'
	  }).then(() => {
	    try {
	      changeState({
			"region_name":row.region_name,
			"year":row.year,
			"week":row.week,
			"type":query.value.type,
			"release_status":release_status
			}).then((res) => {
			  list()
			  ElMessage.success(res.msg)
			  loading.value = false
			})
	      
	    } catch (error) {
			console.log(error)
	      ElMessage.error(msg+'失败')
	    } finally {
	      loading.value = false
	    }
	  }).catch(() => { })
      

    }

    // 导入，results数据，header表头
const imports = async ({ results, header,filename }) => {
  loading.value = true
  monthDataExport({
	filename:filename,
	type:"week",
    import: results
  })
    .then((res) => {
      list()
      ElMessage.success(res.msg)
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}
    onMounted(() => {
		regionlist()
		list()
    })

    return {
      idkey,
      height,
      loading,
      dialog,
      dialogTitle,
      data,
      count,
      formRef,
      rowInfo,
      table,
      query,
      imports,
      changeStateButton,
      search,
      refresh,
      list,
      sort,
      select,
      scanDetails,
      cancel,
	  regionsData,
	  releaseStatus,
      selectOpen
    }
  }
}
</script>