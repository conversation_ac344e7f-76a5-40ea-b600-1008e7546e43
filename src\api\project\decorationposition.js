import request from '@/utils/request';
// 装修位置管理
const url = '/admin/furnish.DecorationPosition/'

/**
 * 获取所有位置列表
 * @param {array} data 请求数据
 */
export function getAllList(data) {
  return request({
    url: url + 'getAllList',
    method: 'post',
    data
  })
}
/**
 * 位置列表
 * @param {array} params 请求参数
 */
export function list(params) {
  return request({
    url: url + 'list',
    method: 'get',
    params: params
  })
}

/**
 * 创建位置
 * @param {array} data 请求数据
 */
export function create(data) {
  return request({
    url: url + 'create',
    method: 'post',
    data
  })
}

/**
 * 更新位置
 * @param {array} data 请求数据
 */
export function update(data) {
  return request({
    url: url + 'update',
    method: 'post',
    data
  })
}

/**
 * 删除位置
 * @param {array} params 请求参数
 */
export function deleteItem(params) {
  return request({
    url: url + 'delete',
    method: 'get',
    params: params
  })
}

/**
 * 更改状态
 * @param {array} params 请求参数
 */
export function changeStatus(data) {
  return request({
    url: url + 'changeStatus',
    method: 'post',
    data
  })
}