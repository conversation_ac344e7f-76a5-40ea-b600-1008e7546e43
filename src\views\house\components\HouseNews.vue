<template>
  <el-scrollbar native class="wrapper">
    <el-form :model="model" ref="houseBase" label-width="120px">
      <el-row>
        <el-col :span="11">
          <!-- 供地 Section -->
          <el-card header="供地" shadow="hover" class="card">
            <el-form-item
              label="供地地址"
              prop="land_supply.address"
              :rules="rules['land_supply.address']"
            >
              <el-input v-model="model.land_supply.address" />
            </el-form-item>
            <el-form-item
              label="供地方位"
              prop="land_supply.direction"
              :rules="rules['land_supply.direction']"
            >
              <el-input v-model="model.land_supply.direction" />
            </el-form-item>
            <el-form-item label="建筑面积" prop="land_supply.build_area">
              <el-input v-model="model.land_supply.build_area" type="number" min="1">
                <template #append>㎡</template>
              </el-input>
            </el-form-item>
            <el-form-item label="供应总价" prop="land_supply.total_price">
              <el-input v-model="model.land_supply.total_price" type="number">
                <template #append>万元</template>
              </el-input>
            </el-form-item>
            <el-form-item label="供应楼面均价" prop="land_supply.avg_price">
              <el-input v-model="model.land_supply.avg_price" type="number"
                ><template #append>元/㎡</template></el-input
              >
            </el-form-item>
            <el-form-item label="土拍条件" prop="land_supply.auction_cond">
              <div v-for="(cond, index) in model.land_supply.auction_conds" :key="`cond-${index}`">
                <el-input v-model="cond.condition" placeholder="请输入土拍条件"></el-input>
                <el-button @click="removeLandAuctionCond(index)" type="danger" size="mini"
                  >删除</el-button
                >
              </div>
              <el-button @click="addLandAuctionCond" type="primary">新增土拍条件</el-button>
            </el-form-item>
            <el-form-item label="供地日期" prop="land_supply.date">
              <el-date-picker
                v-model="model.land_supply.date"
                type="date"
                class="ya-date-value"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item label="备注" prop="land_supply.remark">
              <el-input v-model="model.land_supply.remark" />
            </el-form-item>
          </el-card>

          <br />

          <!-- 取地 Section -->
          <el-card header="取地" shadow="hover" class="card">
            <el-form-item label="取地地址" prop="cache.address">
              <el-input v-model="model.cache.address" />
            </el-form-item>
            <el-form-item label="建筑面积" prop="cache.build_area">
              <el-input v-model="model.cache.build_area" type="number">
                <template #append>㎡</template>
              </el-input>
            </el-form-item>
            <el-form-item label="成交总价" prop="cache.total_price">
              <el-input v-model="model.cache.total_price">
                <template #append>万元</template>
              </el-input>
            </el-form-item>
            <el-form-item label="成交楼面均价" prop="cache.avg_price">
              <el-input v-model="model.cache.avg_price">
                <template #append>元/㎡</template>
              </el-input>
            </el-form-item>
            <el-form-item label="销售指导价" prop="cache.guide_price">
              <el-input v-model="model.cache.guide_price">
                <template #append>元/㎡</template>
              </el-input>
            </el-form-item>
            <el-form-item label="溢价率" prop="cache.markup_rate">
              <el-input v-model="model.cache.markup_rate" type="number">
                <template #append>%</template>
              </el-input>
            </el-form-item>
            <!-- 拿地公司 -->
            <el-form-item label="拿地公司" prop="cache.take_land_companies">
              <div
                v-for="(company, index) in model.cache.take_land_companies"
                :key="`company-${index}`"
              >
                <el-input v-model="company.name" placeholder="请输入拿地公司"></el-input>
                <el-button
                  @click="removeTakeLandCompany(index)"
                  type="danger"
                  size="mini"
                  :disabled="model.cache.take_land_companies.length === 1"
                  >删除
                </el-button>
              </div>
              <el-button @click="addTakeLandCompany" type="primary">新增拿地公司</el-button>
            </el-form-item>
            <!-- 拿地日期 -->
            <el-form-item label="拿地日期" prop="cache.date">
              <el-date-picker
                v-model="model.cache.date"
                type="date"
                class="ya-date-value"
                value-format="YYYY-MM-DD"
                :placeholder="'请选择日期'"
                :clearable="true"
              />
            </el-form-item>
            <el-form-item label="是否添加水印" prop="is_watermark">
              <el-radio-group v-model="model.cache.is_watermark" style="margin-left: 10px">
                <el-radio label="否" :value="0" disabled></el-radio>
                <el-radio label="是" :value="1"></el-radio>
              </el-radio-group>
            </el-form-item>
            <!-- 土地证 -->
            <el-form-item label="土地证" prop="cache.land_cert_img">
              <ImgUploadsHouseNews
                v-model="model.cache.land_cert_img"
                upload-btn="上传图片"
                file-type="image"
                file-tip=""
              />
            </el-form-item>
            <!-- 地块图 -->
            <el-form-item label="地块图" prop="cache.land_parcel_img">
              <ImgUploadsHouseNews
                v-model="model.cache.land_parcel_img"
                upload-btn="上传图片"
                file-type="image"
                file-tip=""
                :isWatermark="isWatermark"
                :source="source"
              />
            </el-form-item>
            <el-form-item>
              <template #label>
                <div class="bold">三证</div>
              </template>
            </el-form-item>
            <!-- 新增字段开始：移动预售中的相关字段到此处 -->
            <el-form-item label="用地许可日期" prop="cache.land_use_cert_date">
              <el-date-picker
                v-model="model.cache.land_use_cert_date"
                type="date"
                class="ya-date-value"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item label="用地许可证" prop="cache.land_use_cert">
              <ImgUploadsHouseNews
                v-model="model.cache.land_use_cert"
                upload-btn="上传图片"
                file-type="image"
                file-tip=""
              />
            </el-form-item>
            <el-form-item label="工程规划许可日期" prop="cache.project_planning_permit_date">
              <el-date-picker
                v-model="model.cache.project_planning_permit_date"
                type="date"
                class="ya-date-value"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item label="工程规划许可证" prop="cache.project_planning_permit_img">
              <ImgUploadsHouseNews
                v-model="model.cache.project_planning_permit_img"
                upload-btn="上传图片"
                file-type="image"
                file-tip=""
              />
            </el-form-item>
            <el-form-item label="工程施工许可日期" prop="cache.project_work_date">
              <el-date-picker
                v-model="model.cache.project_work_date"
                type="date"
                class="ya-date-value"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item label="工程施工许可证" prop="cache.project_plant_cert_img">
              <ImgUploadsHouseNews
                v-model="model.cache.project_plant_cert_img"
                upload-btn="上传图片"
                file-type="image"
                file-tip=""
                :single-upload="true"
              />
            </el-form-item>
            <!-- 新增字段结束 -->
          </el-card>
        </el-col>

        <el-col :offset="1" :span="12">
          <!-- 预售 Section -->
          <el-card header="预售" shadow="hover" class="card">
            <div v-for="(cert, certIndex) in model.takeCerts" :key="'cert-' + certIndex">
              <el-card header="取证" shadow="hover" class="nested-card">
                <el-form-item label="取证日期" :prop="'takeCerts[' + certIndex + '].date'">
                  <el-date-picker
                    v-model="cert.date"
                    type="date"
                    class="ya-date-value"
                    value-format="YYYY-MM-DD"
                    :placeholder="'请选择日期'"
                    :clearable="true"
                  />
                </el-form-item>
                <el-form-item label="楼栋号" :prop="'takeCerts[' + certIndex + '].build_no'">
                  <el-input v-model="cert.build_no" />
                </el-form-item>
                <el-form-item label="价格区间" :prop="'takeCerts[' + certIndex + '].price_range'">
                  <el-col :span="10">
                    <el-input v-model="cert.min_price" placeholder="最低价" type="number">
                      <template #append>元/㎡</template>
                    </el-input>
                  </el-col>
                  <el-col :span="1" label="户型面积" :offset="1">
                    <span>-</span>
                  </el-col>
                  <el-col :span="10">
                    <el-input v-model="cert.max_price" placeholder="最高价" type="number">
                      <template #append>元/㎡</template>
                    </el-input>
                  </el-col>
                </el-form-item>
                <el-form-item label="套数" :prop="'takeCerts[' + certIndex + '].unit_count'">
                  <el-input v-model="cert.unit_count" type="number">
                    <template #append>套</template>
                  </el-input>
                </el-form-item>
                <el-form-item
                  label="户型建面区间"
                  :prop="'takeCerts[' + certIndex + '].unit_area_ranges'"
                >
                  <div
                    v-for="(range, rangeIndex) in cert.unit_area_ranges"
                    :key="'range-' + rangeIndex"
                    class="unit-area-range"
                  >
                    <el-col :span="2">
                      <el-input v-model="range.room" class="w-full" filterable></el-input>
                    </el-col>
                    <el-col :span="2" :offset="1">室</el-col>
                    <el-col :span="2">
                      <el-input v-model="range.ting" class="w-full" filterable></el-input>
                    </el-col>
                    <el-col :span="2" :offset="1">厅</el-col>
                    <el-col :span="2">
                      <el-input v-model="range.wei" class="w-full" filterable></el-input>
                    </el-col>
                    <el-col :span="2" :offset="1">卫</el-col>
                    <el-col :span="6">
                      <el-input v-model="range.areas" class="w-full" filterable>
                        <template #append>平米</template>
                      </el-input>
                    </el-col>
                    <el-button
                      @click="removeUnitAreaRange(certIndex, rangeIndex)"
                      type="danger"
                      size="mini"
                      >删除
                    </el-button>
                  </div>
                  <el-button @click="addUnitAreaRange(certIndex)" type="primary"
                    >新增户型建面区间</el-button
                  >
                </el-form-item>
                <el-form-item
                  label="预售证许可号"
                  :prop="'takeCerts[' + certIndex + '].presale_license'"
                >
                  <el-input v-model="cert.presale_license" />
                </el-form-item>
                <el-form-item
                  label="预计交房时间"
                  :prop="'takeCerts[' + certIndex + '].estimated_delivery'"
                >
                  <el-date-picker
                    v-model="cert.estimated_delivery"
                    type="date"
                    class="ya-date-value"
                    value-format="YYYY-MM-DD"
                  />
                </el-form-item>

                <el-form-item
                  label="销售许可证"
                  :prop="'takeCerts[' + certIndex + '].sales_license'"
                >
                  <ImgUploadsHouseNews
                    v-model="cert.sales_license"
                    upload-btn="上传图片"
                    file-type="image"
                    file-tip=""
                    :single-upload="true"
                    :isWatermark="is_watermark"
                    :source="source"
                  />
                </el-form-item>
                <el-form-item label="是否添加水印" prop="is_watermark">
                  <el-radio-group v-model="model.takeCerts.is_watermark" style="margin-left: 10px">
                    <el-radio label="否" :value="0" disabled></el-radio>
                    <el-radio label="是" :value="1"></el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-button
                  v-if="model.takeCerts.length > 1"
                  @click="removeTakeCert(certIndex)"
                  type="danger"
                  size="mini"
                  >删除取证
                </el-button>
              </el-card>
            </div>

            <br />

            <div v-for="(opening, index) in model.openings" :key="'opening-' + index">
              <el-card header="开盘" shadow="hover" class="nested-card">
                <el-form-item label="开盘日期" :prop="'openings[' + index + '].date'">
                  <el-date-picker
                    v-model="opening.date"
                    type="date"
                    class="ya-date-value"
                    value-format="YYYY-MM-DD"
                    :placeholder="'请选择日期'"
                    :clearable="true"
                  />
                </el-form-item>
                <el-form-item label="开盘去化率" :prop="'openings[' + index + '].absorption_rate'">
                  <el-input v-model="opening.absorption_rate" />
                </el-form-item>
                <el-form-item label="开盘套数" :prop="'openings[' + index + '].units'">
                  <el-input v-model="opening.units">
                    <template #append>套</template>
                  </el-input>
                </el-form-item>
                <el-form-item label="开盘楼栋" :prop="'openings[' + index + '].building'">
                  <el-input v-model="opening.building" />
                </el-form-item>
                <el-form-item label="开盘均价" :prop="'openings[' + index + '].avg_price'">
                  <el-input v-model="opening.avg_price">
                    <template #append>元/㎡</template>
                  </el-input>
                </el-form-item>
                <el-form-item label="价格区间" :prop="'openings[' + index + '].price_range'">
                  <el-col :span="10">
                    <el-input v-model="opening.min_price" placeholder="最低价" type="number">
                      <template #append>元/㎡</template>
                    </el-input>
                  </el-col>
                  <el-col :span="1" label="" :offset="1">
                    <span>-</span>
                  </el-col>
                  <el-col :span="10">
                    <el-input v-model="opening.max_price" placeholder="最高价" type="number">
                      <template #append>元/㎡</template>
                    </el-input>
                  </el-col>
                </el-form-item>
                <el-form-item label="开盘户型" :prop="'openings[' + index + '].unit_area_ranges'">
                  <div
                    v-for="(range, rangeIndex) in opening.unit_area_ranges"
                    :key="'range-' + rangeIndex"
                    class="unit-area-range"
                  >
                    <el-col :span="2">
                      <el-input v-model="range.room" class="w-full" filterable></el-input>
                    </el-col>
                    <el-col :span="2" :offset="1">室</el-col>
                    <el-col :span="2">
                      <el-input v-model="range.ting" class="w-full" filterable></el-input>
                    </el-col>
                    <el-col :span="2" :offset="1">厅</el-col>
                    <el-col :span="2">
                      <el-input v-model="range.wei" class="w-full" filterable></el-input>
                    </el-col>
                    <el-col :span="2" :offset="1">卫</el-col>
                    <el-col :span="6">
                      <el-input v-model="range.areas" class="w-full" filterable>
                        <template #append>平米</template>
                      </el-input>
                    </el-col>
                    <el-button
                      @click="removeOpeningUnitAreaRange(index, rangeIndex)"
                      type="danger"
                      size="mini"
                      >删除
                    </el-button>
                  </div>
                  <el-button @click="addOpeningUnitAreaRange(index)" type="primary"
                    >新增开盘户型</el-button
                  >
                </el-form-item>
                <el-form-item
                  label="预计交房时间"
                  :prop="'openings[' + index + '].estimated_delivery'"
                >
                  <el-date-picker
                    v-model="opening.estimated_delivery"
                    type="date"
                    class="ya-date-value"
                    value-format="YYYY-MM-DD"
                  />
                </el-form-item>
                <el-button
                  v-if="model.openings.length > 1"
                  @click="removeOpening(index)"
                  type="danger"
                  size="mini"
                >
                  删除开盘
                </el-button>
              </el-card>
            </div>
            <br />
            <el-button @click="addTakeCert" type="primary">新增取证</el-button>
            <el-button @click="addOpening" type="primary">新增开盘</el-button>
          </el-card>
        </el-col>
      </el-row>

      <div class="sub-btn">
        <el-button :loading="loading" @click="refresh()" style="margin-right: 10px">返回</el-button>
        <el-button :loading="loading" type="primary" @click="submit()" style="margin-right: 10px"
          >提交</el-button
        >
      </div>
    </el-form>
  </el-scrollbar>
</template>

<script>
import { reactive } from 'vue'
import { getHouseNewsInfo, saveHouseNews } from '@/api/project/houseNews.js'
import ImgUploadsHouseNews from '@/components/FileManage/ImgUploadsHouseNews.vue'
import { ElMessage } from 'element-plus'

export default {
  components: { ImgUploadsHouseNews },
  data() {
    return {
      loading: false,
      house_id: 0,
      model: reactive(this.getInitialModel()),
      rules: {
        'land_supply.address': [{ required: true, message: '供地地址为必填项', trigger: 'blur' }],
        'cache.address': [{ required: true, message: '取地地址为必填项', trigger: 'blur' }],
        'takeCerts.date': [{ required: true, message: '取证日期为必填项', trigger: 'change' }],
        'openings.date': [{ required: true, message: '开盘日期为必填项', trigger: 'change' }],
        // 添加新的验证规则
        'cache.land_use_cert_date': [
          { required: true, message: '用地许可日期为必填项', trigger: 'change' }
        ],
        'cache.land_use_cert': [
          { required: true, message: '用地许可证为必填项', trigger: 'change' }
        ],
        'cache.project_planning_permit_date': [
          { required: true, message: '工程规划许可日期为必填项', trigger: 'change' }
        ],
        'cache.project_planning_permit_img': [
          { required: true, message: '工程规划许可证为必填项', trigger: 'change' }
        ],
        'cache.project_work_date': [
          { required: true, message: '工程施工许可日期为必填项', trigger: 'change' }
        ],
        'cache.project_plant_cert_img': [
          { required: true, message: '工程施工许可证为必填项', trigger: 'change' }
        ]
      }
    }
  },
  mounted() {
    this.house_id = this.$route.query.id
    this.model.house_id = this.$route.query.id
    this.info()
    this.$nextTick(() => {
      this.$refs.houseBase.clearValidate()
    })
  },
  methods: {
    getInitialModel() {
      return {
        house_id: 0,
        land_supply: {
          address: '',
          direction: '',
          date: '',
          build_area: '',
          total_price: '',
          avg_price: '',
          auction_conds: [{ condition: '' }],
          remark: ''
        },
        cache: {
          address: '',
          build_area: '',
          total_price: '',
          avg_price: '',
          guide_price: '',
          markup_rate: '',
          take_land_companies: [{ name: '' }],
          date: '',
          land_cert_img: [],
          land_parcel_img: [],
          // 新增字段移动到这里
          land_use_cert_date: '',
          land_use_cert: [],
          project_planning_permit_date: '',
          project_planning_permit_img: [],
          project_work_date: '',
          project_plant_cert_img: [],
          is_watermark: 1,
          source: 4
        },
        takeCerts: [
          {
            date: '',
            build_no: '',
            min_price: '',
            max_price: '',
            unit_count: '',
            unit_area_ranges: [{ room: '', ting: '', wei: '', areas: '' }],
            presale_license: '',
            is_watermark: 1,
            source: 4,
            estimated_delivery: '',
            sales_license: []
            // 移除移动的字段
            // land_use_cert_date: '',
            // land_use_cert: [],
            // project_planning_permit_date: '',
            // project_planning_permit_img: [],
            // project_work_date: '',
            // project_plant_cert_img: [],
          }
        ],
        openings: [
          {
            date: '',
            absorption_rate: '',
            units: '',
            building: '',
            avg_price: '',
            price_range: '',
            unit_area_ranges: [{ room: '', ting: '', wei: '', areas: '' }],
            estimated_delivery: ''
          }
        ]
      }
    },
    addTakeLandCompany() {
      if (!Array.isArray(this.model.cache.take_land_companies)) {
        this.model.cache.take_land_companies = []
      }
      this.model.cache.take_land_companies.push({ name: '' })
    },
    removeTakeLandCompany(index) {
      if (this.model.cache.take_land_companies && this.model.cache.take_land_companies.length > 1) {
        this.model.cache.take_land_companies.splice(index, 1)
      }
    },
    addUnitAreaRange(certIndex) {
      if (this.model.takeCerts[certIndex]) {
        this.model.takeCerts[certIndex].unit_area_ranges.push({
          room: '',
          ting: '',
          wei: '',
          areas: ''
        })
      }
    },
    removeUnitAreaRange(certIndex, rangeIndex) {
      if (
        this.model.takeCerts[certIndex] &&
        this.model.takeCerts[certIndex].unit_area_ranges.length > 1
      ) {
        this.model.takeCerts[certIndex].unit_area_ranges.splice(rangeIndex, 1)
      }
    },
    // 其他方法保持不变
    addLandAuctionCond() {
      this.model.land_supply.auction_conds.push({ condition: '' })
    },
    removeLandAuctionCond(index) {
      this.model.land_supply.auction_conds.splice(index, 1)
    },
    addOpeningUnitAreaRange(openingIndex) {
      if (this.model.openings[openingIndex]) {
        this.model.openings[openingIndex].unit_area_ranges.push({
          room: '',
          ting: '',
          wei: '',
          areas: ''
        })
      }
    },
    removeOpeningUnitAreaRange(openingIndex, rangeIndex) {
      if (
        this.model.openings[openingIndex] &&
        this.model.openings[openingIndex].unit_area_ranges.length > 1
      ) {
        this.model.openings[openingIndex].unit_area_ranges.splice(rangeIndex, 1)
      }
    },
    addOpening() {
      this.model.openings.push({
        date: '',
        absorption_rate: '',
        units: '',
        building: '',
        avg_price: '',
        price_range: '',
        unit_area_ranges: [{ room: '', ting: '', wei: '', areas: '' }],
        estimated_delivery: ''
      })
    },
    removeOpening(index) {
      if (this.model.openings.length > 1) {
        this.model.openings.splice(index, 1)
      }
    },
    addTakeCert() {
      this.model.takeCerts.push({
        date: '',
        build_no: '',
        min_price: '',
        max_price: '',
        unit_count: '',
        unit_area_ranges: [{ room: '', ting: '', wei: '', areas: '' }],
        presale_license: '',
        estimated_delivery: '',
        sales_license: []
        // 移除移动的字段
        // project_work_date: '',
        // project_plant_cert_img: [],
        // land_use_cert_date: '',
        // land_use_cert: [],
        // project_planning_permit_date: '',
        // project_planning_permit_img: [],
      })
    },
    removeTakeCert(index) {
      if (this.model.takeCerts.length > 1) {
        this.model.takeCerts.splice(index, 1)
      }
    },
    async info() {
      try {
        const res = await getHouseNewsInfo({ house_id: this.house_id })
        if (res.data) {
          this.model = reactive(this.mergeModels(this.getInitialModel(), res.data))
          this.model.takeCerts.is_watermark = 1
        }
      } catch (error) {
        console.error('Error fetching house news info:', error)
      } finally {
        this.initializeModel()
      }
    },
    async submit() {
      this.loading = true
      try {
        await this.$refs.houseBase.validate()
        this.model.house_id = this.$route.query.id
        const response = await saveHouseNews(this.model)

        await this.info()
        this.dialog = false

        if (response && response.code === 200) {
          ElMessage.success(response.msg)
        } else {
          ElMessage.warning(response.msg || '操作未成功')
        }
      } catch (error) {
        if (error.message) {
          ElMessage.error(error.message)
        } else {
          console.error(error)
          ElMessage.error('表单验证失败，请检查必填项')
        }
      } finally {
        this.loading = false
      }
    },
    mergeModels(initialModel, fetchedData) {
      const mergedModel = JSON.parse(JSON.stringify(initialModel))
      if (fetchedData.takeCerts) {
        fetchedData.takeCerts.forEach((cert) => {
          if (cert.sales_license && !Array.isArray(cert.sales_license)) {
            cert.sales_license = [cert.sales_license]
          }
        })
      }
      for (const key in fetchedData) {
        if (
          fetchedData.hasOwnProperty(key) &&
          fetchedData[key] !== null &&
          fetchedData[key] !== undefined
        ) {
          if (key === 'cache') {
            mergedModel[key] = { ...mergedModel[key], ...fetchedData[key] }
            // 处理单个拿地公司到多个拿地公司的转换
            if (fetchedData[key].take_land_company) {
              mergedModel[key].take_land_companies = [{ name: fetchedData[key].take_land_company }]
            } else if (!Array.isArray(mergedModel[key].take_land_companies)) {
              mergedModel[key].take_land_companies = [{ name: '' }]
            }
          } else {
            mergedModel[key] = fetchedData[key]
          }
        }
      }
      return mergedModel
    },
    initializeModel() {
      this.model.takeCerts.forEach((cert) => {
        if (!Array.isArray(cert.sales_license)) {
          cert.sales_license = []
        }
      })
      // 确保所有必要的属性都存在
      if (!this.model.cache) {
        this.model.cache = this.getInitialModel().cache
      }
      if (
        !Array.isArray(this.model.cache.take_land_companies) ||
        this.model.cache.take_land_companies.length === 0
      ) {
        this.model.cache.take_land_companies = [{ name: '' }]
      }
      if (!Array.isArray(this.model.takeCerts) || this.model.takeCerts.length === 0) {
        this.model.takeCerts = [this.getInitialModel().takeCerts[0]]
      }
      if (!Array.isArray(this.model.openings) || this.model.openings.length === 0) {
        this.model.openings = [this.getInitialModel().openings[0]]
      }

      // 确保每个 takeCert 和 opening 都有必要的属性
      this.model.takeCerts.forEach((cert) => {
        if (!Array.isArray(cert.unit_area_ranges) || cert.unit_area_ranges.length === 0) {
          cert.unit_area_ranges = [{ room: '', ting: '', wei: '', areas: '' }]
        }
      })
      this.model.openings.forEach((opening) => {
        if (!Array.isArray(opening.unit_area_ranges) || opening.unit_area_ranges.length === 0) {
          opening.unit_area_ranges = [{ room: '', ting: '', wei: '', areas: '' }]
        }
      })
    },
    // 其他方法保持不变
    refresh() {
      // 实现返回逻辑
      this.$router.go(-1)
    }
  }
}
</script>

<style>
.wrapper {
  padding: 20px;
}

.card {
  margin-bottom: 20px;
}

.nested-card {
  margin-bottom: 10px;
}

.bold {
  font-weight: bold;
}

.sub-btn {
  text-align: center;
  margin-top: 20px;
}

.el-button {
  margin-top: 10px;
}

.unit-area-range {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.unit-area-range .el-col {
  margin-right: 5px;
}
</style>
