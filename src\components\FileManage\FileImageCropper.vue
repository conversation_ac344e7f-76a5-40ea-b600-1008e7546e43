<template>
  <div class="cropper-wraper">
    <div class="cropper">
      <vue-cropper ref="cropper" v-bind="option" @crop-moving="handleCropMoving" />
    </div>
    <div class="change-box">
      <el-upload auto-upload :before-upload="uploadBefore" accept=".jpg,.png,.jpeg">
        <div class="btn-box">
          <el-button type="primary">选择图片</el-button>
        </div>
      </el-upload>

      <div class="btn-box">
        <el-button type="primary" @click="handleRight">向右旋转</el-button>
      </div>
      <div class="btn-box">
        <el-button type="primary" @click="handleLeft">向左旋转</el-button>
      </div>
      <div class="from-item">
        <span>截取宽</span>
        <el-input type="number" v-model="option.autoCropWidth" placeholder="截取宽度" />
      </div>
      <div class="from-item">
        <span>截取高</span>
        <el-input type="number" v-model="option.autoCropHeight" placeholder="截取宽度" />
      </div>
      <!-- <div class="btn-box">
        <el-button type="primary" @click="handlePerview">预览图片</el-button>
      </div> -->
      <div class="btn-box submit">
        <el-button type="success" @click="handleSubmit" :loading="loading">上传图片</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { add } from '@/api/file/file'
import 'vue-cropper/dist/index.css'
import { VueCropper } from 'vue-cropper'
import { defineComponent } from 'vue'
import { ElMessage } from 'element-plus'
export default defineComponent({
  components: { VueCropper },
  name: '',
  props: {
    // img: {
    //   type: String,
    //   default: ''
    // },
    // 弹窗显示
    show: {
      type: Boolean,
      default: false
    },
    isWatermark: {
      type: Number,
      default: 1
    },
    source: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      option: {
        img: '',
        outputSize: 1,
        autoCrop: true,
        autoCropWidth: 400,
        autoCropHeight: 280
      },
      fileName: '',
      loading: false
    }
  },
  methods: {
    handleRight() {
      this.$refs.cropper.rotateRight()
    },
    handleLeft() {
      this.$refs.cropper.rotateLeft()
    },
    handlePerview() {
      // this.$refs.cropper.
    },
    handleCropMoving(data) {
      console.log(data, '截图框当前坐标')
    },
    handleSubmit() {
      this.loading = true
      this.$refs.cropper.getCropBlob((data) => {
        console.log(data)
        let file = new File([data], this.fileName || 'test.png', {
          type: 'image/png'
        })
        const formData = new FormData()
        console.log(this.isWatermark)
        console.log(this.source)
        formData.append('file', file)
        formData.append('isWatermark', this.isWatermark)
        formData.append('source', this.source)
        add(formData, 'url')
          .then((res) => {
            this.loading = false
            ElMessage.success('上传成功')
            this.$refs.cropper.refresh()
            this.option.img = ''
            this.$emit('success')
          })
          .catch(() => {
            this.loading = false
          })
      })
    },
    uploadBefore(e) {
      //   console.log(e)
      const file = e
      //   if (!/\.(gif|jpg|jpeg|png|bmp|GIF|JPG|PNG)$/.test(e.target.value)) {
      //     ElMessage.warning('图片类型必须是.gif,jpeg,jpg,png,bmp中的一种')
      //     return false
      //   }
      this.fileName = e.name

      const reader = new FileReader()
      reader.onload = (ev) => {
        let data
        if (typeof ev.target.result === 'object') {
          // 把Array Buffer转化为blob 如果是base64不需要
          data = window.URL.createObjectURL(new Blob([ev.target.result]))
        } else {
          data = ev.target.result
        }
        console.log(data)
        this.option.img = data
      }
      reader.readAsArrayBuffer(file)
      return false
    }
  }
})
</script>

<style lang="scss" scoped>
.cropper-wraper {
  width: 100%;
  height: 600px;
  //   border: 1px solid red;
  display: flex;
  align-items: flex-start;
  .cropper {
    width: 650px;
    height: 580px;
  }
  .change-box {
    width: 230px;
    display: flex;
    flex-direction: column;
    justify-items: flex-start;
    align-items: center;
  }
  .btn-box {
    width: 150px;
    margin-top: 20px;
    :deep(.el-button) {
      width: 100%;
    }
    &.submit {
      margin-top: 230px;
      :deep(.el-button) {
        height: 40px;
      }
    }
  }

  .from-item {
    margin-top: 18px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    :deep(.el-input) {
      width: 110px;
    }
  }
}
</style>
