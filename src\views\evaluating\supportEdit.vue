<template>
  <div class="page-wraaper">
    <h3>{{ routeQuery.projectName }} (ID:{{ routeQuery.id }})</h3>
    <div class="position-relative">
      <el-tabs
        v-model="cuurentTab"
        class="demo-tabs"
        @tab-change="handleTabClick"
        :before-leave="handleBeforeLeave"
      >
        <el-tab-pane
          v-for="(item, index) in tabs"
          :key="index"
          :label="item.title"
          :name="item.value"
        />
      </el-tabs>
      <div class="position-absolute top-[0] right-[0]" v-show="cuurentTab">
        <el-button type="primary" @click="handleAddSup">增加点位</el-button>
      </div>
    </div>
    <!-- <div v-if="cuurentTab === 0">
      <Coordinate :house="{ id: routeQuery.id }" />
    </div> -->
    <div class="table-box">
      <el-table
        :data="tableData.list"
        v-loading="tableData.loading"
        height="500"
        @sort-change="handleSortChange"
      >
        <el-table-column width="80" label="前台展示" align="center" fixed="left">
          <template #default="{ row }">
            <el-checkbox :key="row.id" :checked="!!row.is_manual_show" disabled />
          </template>
        </el-table-column>
        <el-table-column label="手动优先展示" width="120" align="center" fixed="left">
          <template #default="{ row }">
            <el-checkbox :key="row.id" v-model="row.isCheck" :true-value="1" :false-value="0" />
          </template>
        </el-table-column>
        <el-table-column
          prop="facility_name"
          label="名称"
          width="350"
          show-overflow-tooltip
          fixed="left"
        >
          <template #default="{ row }">
            <el-badge value="new" class="item" :offset="[10, 10]" :hidden="row.is_new === 0">
              {{ row.facility_name }}
            </el-badge>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="来源" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.is_manual ? '手动添加' : '腾讯API' }}
          </template>
        </el-table-column>
        <el-table-column label="经纬度" width="200" show-overflow-tooltip>
          <template #default="{ row }"> {{ row.latitude }},{{ row.longitude }} </template>
        </el-table-column>
        <el-table-column
          prop="line_distance_sort"
          label="直线距离"
          sortable="custom"
          width="120"
          show-overflow-tooltip
        >
          <template #default="{ row }"> {{ row.line_distance }}m</template>
        </el-table-column>
        <el-table-column
          prop="walk_distance_sort"
          label="步行距离"
          sortable="custom"
          width="120"
          show-overflow-tooltip
        >
          <template #default="{ row }"> {{ row.walk_distance }}m</template>
        </el-table-column>
        <el-table-column
          prop="feature_sort"
          label="特征信息"
          sortable="custom"
          show-overflow-tooltip
          width="180"
        >
          <template #default="{ row }">
            <template v-if="row.facility_type !== 3 && row.facility_type !== 6">
              <span> {{ formatFeature(row) }}</span>
            </template>
            <template v-else>
              <span v-if="row.feature?.star">
                <el-rate
                  :model-value="formaterStar(row.feature.star)"
                  show-score
                  disabled
                  allow-half
                />
              </span>
              <span v-else> -- </span>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <div class="flex">
              <el-link type="primary" v-if="row.is_manual" @click="handleClickEditItem(row)">
                编辑
              </el-link>
              <el-link type="primary" v-if="!row.is_manual" @click="handleClickMarkItem(row)">
                标注
              </el-link>
              <el-link class="ml-[10px]" v-if="row.is_manual" @click="handleClickDeleteItem(row)">
                删除
              </el-link>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="table-footer flex mt-[20px] flex-justify-between">
        <div class="flex flex-items-center">
          <div>
            已选中<span class="font-bold ml-[10px]"
              >{{ selections.length }}条数据 (最多100条数据)</span
            >
          </div>
          <div class="ml-[20px]">
            <el-button
              type="primary"
              :disabled="!selections.length"
              :loading="savaLoading"
              @click="handleSaveOrCalculate(0)"
              >保存</el-button
            >
            <el-button
              type="primary"
              :loading="savaLoading"
              :disabled="!selections.length"
              @click="handleSaveOrCalculate(1)"
              >保存并重新计算评分</el-button
            >

            <el-button
              type="primary"
              :loading="savaLoading"
              :disabled="!selections.length"
              @click="handlePushMessage"
              >保存并推送</el-button
            >
          </div>
        </div>
        <pagination
          v-show="tableData.total > 100"
          v-model:total="tableData.total"
          v-model:page="tableQuery.page"
          v-model:limit="tableQuery.limit"
          @pagination="featchList"
        />
      </div>
    </div>
    <SiteEditDialog
      v-bind="dialogConfig"
      :type="cuurentTab"
      v-model:visible="dialogConfig.visible"
      @close="handleClose"
      @update="hadnleUpdate"
    />
  </div>
</template>
<script setup>
import SiteEditDialog from './components/SiteEditDialog.vue'
import Coordinate from './components/Coordinate.vue'
import { getPageLimit } from '@/utils/settings'
import { isEmpty } from '@/utils/validate'
import {
  getSupportEvaluationList,
  supportEvaluationSave,
  supportEvaluationDeletePoint,
  removeNewLabel,
  sendMessage
} from '@/api/project/evaluating'
import { FACILTY_MAP, FACILTY_TYPES } from './config'
import { onBeforeRouteLeave } from 'vue-router'
const route = useRoute()

const routeQuery = computed(() => route.query)

const tabs = reactive([
  // { title: '位置信息', value: 0 },
  ...Object.keys(FACILTY_TYPES).map((key) => ({ title: FACILTY_TYPES[key], value: Number(key) }))
])

const formaterStar = (number) => {
  const str = String(parseFloat(number))
  if (str.length === 1) return Number(str)
  return Number(str[0]) + 0.5
}

const cuurentTab = ref(1)

const tableData = reactive({
  total: 0,
  list: [],
  loading: false
})

const tableQuery = reactive({
  page: 1,
  limit: 100
})

const sort = ref({})

const dialogConfig = reactive({
  id: '',
  model: '',
  visible: false
})

const savaLoading = ref(false)

// 特征信息
const formatFeature = (row) => {
  switch (cuurentTab.value) {
    case 1:
      return FACILTY_MAP[cuurentTab.value][row.facility_subtype] || '--'
    case 2:
      return isEmpty(row.feature)
        ? '--'
        : FACILTY_MAP[cuurentTab.value][row.feature.is_famous] || '--'
    case 4:
      return isEmpty(row.feature) ? '--' : FACILTY_MAP[cuurentTab.value][row.feature.level] || '--'
    case 5:
      return isEmpty(row.feature)
        ? '--'
        : FACILTY_MAP[cuurentTab.value].level[row.feature.level] +
            FACILTY_MAP[cuurentTab.value].type[row.feature.type]

    default:
      return '--'
  }
}

const selections = computed(() => {
  if (!tableData.list.length) return false
  return tableData.list.filter((i) => i.isCheck)
})

const featchList = async () => {
  tableData.loading = true

  const params = {
    ...tableQuery,
    ...sort.value,
    house_id: routeQuery.value.id,
    facility_type: cuurentTab.value
  }

  try {
    const { data } = await getSupportEvaluationList(params)
    tableData.total = data.count
    tableData.list = data.list.map((i) => ({ ...i, isCheck: i.is_manual_show }))
  } finally {
    tableData.loading = false
  }
}

const updateCurrentTypeIsNewStatus = async () => {
  try {
    await removeNewLabel({ house_id: routeQuery.value.id, facility_type: cuurentTab.value })
  } finally {
  }
}

const handleTabClick = (e) => {
  if (e.index !== '0') {
    featchList()
  }
}

const handleBeforeLeave = (active, oldActive) => {
  if (oldActive !== 0) {
    updateCurrentTypeIsNewStatus()
  }
}

onBeforeRouteLeave(() => {
  console.log('beforeRouteLeave', cuurentTab.value)
  if (cuurentTab.value !== 0) {
    updateCurrentTypeIsNewStatus()
  }
})

const handleSortChange = (e) => {
  let obj = {}
  if (e.order === 'ascending') {
    obj[e.prop] = 'asc'
  }
  if (e.order === 'descending') {
    obj[e.prop] = 'desc'
  }
  switch (e.order) {
    case 'ascending':
      obj[e.prop] = 'asc'
      break
    case 'descending':
      obj[e.prop] = 'desc'
      break
    default:
      obj = {}
      break
  }
  sort.value = obj
  featchList()
}

// 增加站点
const handleAddSup = () => {
  dialogConfig.id = ''
  dialogConfig.model = 'create'
  dialogConfig.visible = true
}

const handleClose = () => {
  dialogConfig.id = ''
}

const hadnleUpdate = () => {
  if (cuurentTab.value !== 0) {
    tableQuery.page = 1
    featchList()
  }
}

// 保存计算
const handleSaveOrCalculate = async (is_score) => {
  if (selections.value.length > 20) {
    ElMessage.warning('每次最多选择20条数据')
    return
  }
  const params = {
    house_id: routeQuery.value.id,
    is_score,
    id_list: selections.value.map((i) => i.id),
    facility_type: cuurentTab.value
  }
  savaLoading.value = true
  try {
    const { data } = await supportEvaluationSave(params)
    ElMessage.success('操作成功')
  } finally {
    savaLoading.value = false
  }
}

const handlePushMessage = async () => {
  if (selections.value.length > 20) {
    ElMessage.warning('每次最多选择20条数据')
    return
  }
  handleSaveOrCalculate(0)
  savaLoading.value = true
  try {
    const { data } = await sendMessage({ house_id: routeQuery.value.id })
    // ElMessage.success('操作成功')
  } finally {
    savaLoading.value = false
  }
  // sendMessage
}

// 删除
const handleClickDeleteItem = (row) => {
  // console.log(row)
  ElMessageBox({
    title: '提示',
    message: '确认删除吗？',
    showCancelButton: true,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        try {
          const res = await supportEvaluationDeletePoint({ id: row.id })
          ElMessage.success('操作成功')
          featchList()
        } finally {
          instance.confirmButtonLoading = false
          done()
        }
      } else {
        done()
      }
    }
  })
}

// 编辑
const handleClickEditItem = (row) => {
  dialogConfig.id = row.id
  dialogConfig.model = 'edit'
  dialogConfig.visible = true
}

// 标注
const handleClickMarkItem = (row) => {
  dialogConfig.id = row.id
  dialogConfig.model = 'mark'
  dialogConfig.visible = true
}

onMounted(featchList)
</script>
<style lang="scss" scoped>
.page-wraaper {
  padding: 0 20px;
}
</style>
