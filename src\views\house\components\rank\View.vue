<template>
  <div class="app-container">
    
    <!-- 搜索end -->
	<el-dialog
	  v-model="selectDialog"
	  :title="selectTitle"
	  :close-on-click-modal="false"
	  :close-on-press-escape="false"
	  top="20vh"
	>
	  <el-form ref="selectRef" label-width="120px">
	    
		<el-form-item label="楼盘名称">
		  <el-input v-model="rowInfo.name" type="text" autosize disabled />
		</el-form-item>
	    <el-form-item :label="name + 'ID'">
	      <el-input v-model="rowInfo.id" type="text" autosize disabled />
	    </el-form-item>
		<el-form-item label="历史热度">
		  <el-input v-model="rowInfo.real_view" type="text" autosize disabled />
		</el-form-item>
    <el-form-item label="近14天热度">
		  <el-input v-model="rowInfo.fourteen_day_view" type="text" autosize disabled />
		</el-form-item>
		<el-form-item label="热度加量">
		  <el-input v-model="rowInfo.view_num" type="number" autosize />
		</el-form-item>
		<el-form-item label="推荐流量">
		  <el-input v-model="rowInfo.view" type="text" autosize disabled />
		</el-form-item>
	  </el-form>
	  <template #footer>
	    <el-button :loading="loading" @click="selectCancel">取消</el-button>
	    <el-button :loading="loading" type="primary" @click="selectSubmit">提交</el-button>
	  </template>
	</el-dialog>
    <el-table
      ref="table"
      v-loading="loading"
      :data="data"
      height="600"
      @sort-change="sort"
      @selection-change="select"
    >
      <el-table-column type="selection" width="42" title="全选/反选" />
      <el-table-column :prop="idkey" label="ID" width="80" sortable="custom" />
      <el-table-column prop="name" label="楼盘名称" min-width="180" show-overflow-tooltip />
	  <el-table-column prop="is_land" label="类型" show-overflow-tooltip >
	  <template #default="scope">
	      <span v-text="scope.row.is_land == 0?'楼盘':'地块'"></span>
	    </template>
	  </el-table-column>
     
      <el-table-column prop="real_view" label="历史热度" min-width="160" show-overflow-tooltip >
	  <template #default="scope">
	      <span v-text="scope.row.real_view" v-if="scope.row.real_view != null"></span>
			
	      <span v-else v-text="''"></span>
	    </template>
	  </el-table-column>
    <el-table-column prop="fourteen_day_view" label="近14天热度" min-width="165" sortable="custom">
	  	<template #default="scope">
	  	  <span v-text="scope.row.fourteen_day_view?scope.row.fourteen_day_view:0"></span>
	  	</template>
	    </el-table-column>
	  <el-table-column prop="view_num" label="热度加量" min-width="165" sortable="custom">
	  	<template #default="scope">
	  	  <span v-text="scope.row.view_num?scope.row.view_num:0"></span>
	  	</template>
	    </el-table-column>
	  <el-table-column prop="view" label="展示热度" min-width="165" sortable="custom" >
	  <template #default="scope">
	      <span v-text="scope.row.view "></span>
	    </template>
	  </el-table-column>
      
		
		  
      <el-table-column label="操作" width="125" fixed="right">
        <template #default="scope">
         
          <el-link
            type="primary"
			class="mr-1"
            v-if="scope.row.release_status != 2"
            :underline="false"
            @click="selectOpen(scope.row)"
          >
            修改
          </el-link>
          
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="count > 0"
      v-model:total="count"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="houseList"
    />
  </div>
</template>
<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import checkPermission from '@/utils/permission'
import screenHeight from '@/utils/screen-height'
import Pagination from '@/components/Pagination/index.vue'
import { getPageLimit } from '@/utils/settings'
import { rankhouselist,editViewNum } from '@/api/project/houserank'
import { useFormDisabled } from 'element-plus'
import { arrayColumn } from '@/utils/index'
import request from '@/utils/request';
import router from '@/router'
import { defineProps } from 'vue';
const props = defineProps({
  queryType: String
});
const model = ref({})
const rowInfo = ref({})

const loading = ref(true)
const data = ref([])
const height = ref(680)
const query = ref({
  page: 1,
  limit: 15,
  ranktype: props.queryType
})
const count = ref(0)
const idkey = ref('id')
const name = ref('楼盘')
const dialog = ref(false)
const dialogTitle = ref('')
const selectDialog = ref(false)
const selectNames = ref('')
const selectTitle = ref('操作')
const realseOpt = ref('')
const sectorData = ref([])

function houseList() {
  loading.value = true
  rankhouselist(query.value)
    .then((res) => {
      data.value = res.data.list
      count.value = res.data.count
      loading.value = false
    })
    .catch((error) => {
      loading.value = false
    })
}

// 排序
function sort(sort) {
  query.value.sort_field = sort.prop
  query.value.sort_value = ''
  if (sort.order === 'ascending') {
    query.value.sort_value = 'asc'
    houseList()
  }
  if (sort.order === 'descending') {
    query.value.sort_value = 'desc'
    houseList()
  }
}

// 操作
function select(selection) {
  this.selection = selection
}
function selectGetIds(selection) {
  return arrayColumn(selection, this.idkey)
}
function cancel() {
  dialog.value = false
  reset()
}
//搜索
const queryRef = ref(null)
function resetQuery() {
  query.value.status = '-1'
  query.value.id = ''
  query.value.release_status = '-1'
  query.value.region_id = 0
  query.value.plate_id = 0
  query.value.date_field = ''
  query.value.date_value[0] = ''
  query.value.date_value[1] = ''
  queryRef.value.resetFields()
  handleQuery()
}
function handleQuery() {
  query.value.pageNum = 1
  houseList()
}
//搜索end
// 重置
function reset(row) {
  if (row) {
    model.value = row
  } else {
    model.value = {}
  }
}

onMounted(() => {
  height.value = screenHeight(310)
  // 获取楼盘列表
  houseList()
})
function selectAlert() {
  ElMessageBox.alert('请选择需要操作的' + this.name, '提示', {
    type: 'warning',
    callback: () => {}
  })
}
function selectOpen(selectRow = '') {
  if (selectRow.fourteen_day_view == null) {
    selectRow.fourteen_day_view = 0
  }
  rowInfo.value = selectRow
  selectNames.value = selectRow.name
  selectDialog.value = true
}
function selectCancel() {
  selectDialog.value = false
}
function selectSubmit() {
  
  release()
  
  selectDialog.value = false
}
// 发布时间
function release() {
	// console.log(rowInfo)
  editViewNum({
    id: rowInfo.value.eid,
    house_id: rowInfo.value.id,
    view_num: rowInfo.value.view_num
  })
    .then((res) => {
      houseList()
      ElMessage.success(res.msg)
    })
    .catch(() => {
      houseList()
    })
}

const handleKeyDown = (event) => {
  if (event.key === 'Enter') {
    handleQuery();
  }
};

onMounted(() => {
  window.addEventListener('keydown', handleKeyDown);
});

onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleKeyDown);
});
</script>
