import request from '@/utils/request';
// 板块列表
const url = '/admin/basicdata.Plate/';

/**
 * 价格评测列表
 * @param {array} params 请求参数
 */
export function list(params) {
  return request({
    url: url + 'list',
    method: 'get',
    params: params
  })
}

export function getRegionList(params) {
  return request({
    url: url + 'getRegionList',
    method: 'get',
    params: params
  })
}

/**
 * 板块修改
 * @param {array} data 请求数据
 */
export function edit(data) {
  return request({
    url: url + 'edit',
    method: 'post',
    data
  })
}


/**
 * 板块评测评分修改
 * @param {array} data 请求数据
 */
export function scoreedit(data) {
  return request({
    url: url + 'scoreedit',
    method: 'post',
    data
  })
}

/**
 * 板块添加
 * @param {array} data 请求数据
 */
export function add(data) {

  return request({
    url: url + 'add',
    method: 'post',
    data
  })
}

/**
 * 板块评分添加
 * @param {array} data 请求数据
 */
export function scoreadd(data) {

  return request({
    url: url + 'scoreadd',
    method: 'post',
    data
  })
}


/**
 * 板块信息
 * @param {array} params 请求参数
 */
export function info(params) {
  return request({
    url: url + 'info',
    method: 'get',
    params: params
  })
}

/**
 * 板块评分信息
 * @param {array} params 请求参数
 */
export function scoreinfo(params) {
  return request({
    url: url + 'scoreinfo',
    method: 'get',
    params: params
  })
}
/**
 * 获取所有板块
 * @param {array} params 请求参数
 */
export function getPlateList(params) {
  return request({
    url: url + 'getPlateList',
    method: 'get',
    params: params
  })
}

/**
* 板块发布/下架
* @param {array} data 请求数据
*/
export function changestage(data) {

  return request({
    url: url + 'changestage',
    method: 'post',
    data
  })
}

/**
* 板块评测内容导入
* @param {array} data 请求数据
*/
export function platescoreimports(data) {

  return request({
    url: url + 'importPlateScore',
    method: 'post',
    data
  })
}

/**
* 产业评测内容导入
* @param {array} data 请求数据
*/
export function importIndustryScore(data) {

  return request({
    url: url + 'importIndustryScore',
    method: 'post',
    data
  })
}


/**
* 获取中心点坐标
* @param {array} data 请求数据
*/
export function getCenterpoint(data) {
  return request({
    url: url + 'getCenterpoint',
    method: 'post',
    data
  })
}

/**
* 推送
* @param {array} data 请求数据
*/
export function sendPlateMessage(data) {
  return request({
    url: '/admin/basicdata.Plate/sendPlateMessage',
    method: 'post',
    data
  })
}


/**
 * 产业评测信息
 * @param {array} params 请求参数
 */
export function getIndustryInfo(params) {
  return request({
    url: url + 'getIndustryInfo',
    method: 'get',
    params: params
  })
}



/**
 * 产业评测评分修改
 * @param {array} data 请求数据
 */
export function industryScoryEdit(data) {
  return request({
    url: url + 'industryScoryEdit',
    method: 'post',
    data
  })
}

/**
 * 下载产业评测模版
 * @param {array} data 请求数据
 */
export function downloadIndustryTemplate(data) {
  return request({
    url: url + 'downloadIndustryTemplate',
    method: 'post',
    data
  })
}