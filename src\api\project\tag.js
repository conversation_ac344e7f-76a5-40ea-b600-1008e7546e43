import request from '@/utils/request';
// 板块列表
const url = '/admin/basicdata.Tag/';

/**
 * 价格评测列表
 * @param {array} params 请求参数
 */
export function list(params) {
    return request({
      url: url + 'list',
      method: 'get',
      params: params
    })
}

/**
 * 修改
 * @param {array} data 请求数据
 */
export function edit(data) {
    return request({
      url: url + 'edit',
      method: 'post',
      data
    })
}

/**
 * 信息
 * @param {array} params 请求参数
 */
export function info(params) {
  return request({
    url: url + 'info',
    method: 'get',
    params: params
  })
}

/**
 * 地区是否禁用
 * @param {array} data 请求数据
 */
export function disable(data) {
  return request({
    url: url + 'disable',
    method: 'post',
    data
  })
}

/**
 * 根据分组获取所有的标签
 * @param {array} data 请求数据
 */
export function tagsbygroup(data) {
  return request({
    url: url + 'tagsbygroup',
    method: 'post',
    data
  })
}

/**
 * 新增标签
 * @param {array} data 请求数据
 */
export function addTag(data) {
  return request({
    url: url + 'edit',
    method: 'post',
    data
  })
}