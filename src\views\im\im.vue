<template>
  <el-row style="margin-top: 20px;" >
    <el-card style="width: 100%">
      <el-card style="padding: 0px 20px;margin-left: 50px;width: 200px;display: inline-block;" class="text-center" shadow="never">
        <span>社群数量</span>
        <el-statistic :value="groupNum" />
      </el-card>
      <el-card style="padding: 0px 20px;margin-left: 50px;width: 200px;display: inline-block;" class="text-center" shadow="never">
        <span>发名片次数</span>
        <el-statistic :value="sendCardNum" />
      </el-card>
      <el-card style="padding: 0px 20px;margin-left: 50px;width: 200px;display: inline-block;" class="text-center" shadow="never">
        <span>发评测次数</span>
        <el-statistic :value="sendTestNum" />
      </el-card>
      <el-card style="padding: 0px 20px;margin-left: 50px;width: 200px;display: inline-block;" class="text-center" shadow="never">
        <span>发资讯次数</span>
        <el-statistic :value="sendNewsNum" />
      </el-card>
    </el-card>
    <el-card style="width: 100%">
      <template #header>
        <div class="card-header">
          <span>评测卡片</span>
          <div class="inline" style="float: right;">
            <el-date-picker
                v-model="dataValue1"
                type="daterange"
                :shortcuts="shortcuts"
                range-separator="To"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                @change="dataValue1Change"
            />
          </div>
        </div>
      </template>
      <div id="testCardNumEchart" style="height:400px;"></div>
    </el-card>
    <el-card style="width: 100%">
      <template #header>
        <div class="card-header">
          <span>总聊天量</span>
          <div class="inline" style="float: right;">
            <el-date-picker
                v-model="dataValue2"
                type="daterange"
                :shortcuts="shortcuts"
                range-separator="To"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                @change="dataValue2Change"
            />
          </div>
        </div>
      </template>
      <div id="imLogEchart" style="height:400px;"></div>
    </el-card>
    <el-card style="width: 100%">
      <template #header>
        <div class="card-header">
          <span>群组聊天量</span>
          <div class="inline" style="float: right;">
            <el-date-picker
                v-model="dataValue3"
                type="daterange"
                :shortcuts="shortcuts"
                range-separator="To"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                @change="dataValue3Change"
            />
          </div>
        </div>
      </template>
      <div id="groupImLogEchart" style="height:400px;"></div>
    </el-card>
  </el-row>
</template>

<script>
import screenHeight from '@/utils/screen-height'
import checkPermission from '@/utils/permission'
// 引入 echarts 核心模块，核心模块提供了 echarts 使用必须要的接口
import * as echarts from 'echarts'
import {index, testCard, imLog, imGroupLog} from '@/api/im/im'
export default {
  name: '首页',
  data() {
    return {
      name: '控制台',
      height: 680,
      groupNum:0,
      sendCardNum:0,
      sendTestNum:0,
      sendNewsNum:0,
      testCardData:[],
      imLogData:[],
      imGroupLogData:[],
      dataValue1:'',
      dataValue2:'',
      dataValue3:'',
      query:{
        start:'',
        end:'',
      },
      shortcuts:[
        {
          text: '今日',
          value: () => {
            const now = new Date();
            return [now, now]
          },
        },
        {
          text: '本周',
          value: () => {
            const now = new Date();
            const dayOfWeek = now.getDay();
            const daysToSubtract = dayOfWeek - 1; // 周一是1，我们需要将其调整为0
            let start = new Date(now.getTime() - (daysToSubtract * 24 * 60 * 60 * 1000));
            let end = new Date();
            return [start, end]
          },
        },
        {
          text: '本月',
          value: () => {
            const now = new Date();
            let start = new Date(now.getFullYear(), now.getMonth(), 1);
            return [start, now]
          },
        },
        {
          text: '全年',
          value: () => {
            const now = new Date();
            let start = new Date(now.getFullYear(), 0, 1);
            let end = new Date(now.getFullYear(), 11, 31);
            return [start, end]
          },
        },
      ]
    }
  },
  created() {
    this.index()

  },
  mounted() {
    this.height = screenHeight(130)
    this.testCard()
    this.imLog()
    this.imGroupLog()
  },
  methods: {
    dataValue1Change(val){
      let start = this.setDateFormat(val[0]);
      let end = this.setDateFormat(val[1]);
      this.query.start = start;
      this.query.end = end;
      this.testCard()
    },
    dataValue2Change(val){
      let start = this.setDateFormat(val[0]);
      let end = this.setDateFormat(val[1]);
      this.query.start = start;
      this.query.end = end;
      this.imLog()
    },
    dataValue3Change(val){
      let start = this.setDateFormat(val[0]);
      let end = this.setDateFormat(val[1]);
      this.query.start = start;
      this.query.end = end;
      this.imGroupLog()
    },
    index(){
      this.loading = true
      index(this.query)
          .then((res) => {
            this.groupNum = res.data.groupNum
            this.sendCardNum = res.data.sendCardNum
            this.sendTestNum = res.data.sendTestNum
            this.sendNewsNum = res.data.sendNewsNum
            this.loading = false
          })
          .catch(() => {
            this.loading = false
          })
    },
    testCard(){
      this.loading = true
      testCard(this.query)
          .then((res) => {
            this.getEchart(res.data, 'testCardNumEchart', '发送评测卡片')
          })
          .catch(() => {
            this.loading = false
          })
    },
    imLog(){
      this.loading = true
      imLog(this.query)
          .then((res) => {
            this.getEchart(res.data, 'imLogEchart', '总聊天量')
          })
          .catch(() => {
            this.loading = false
          })
    },
    imGroupLog(){
      this.loading = true
      imGroupLog(this.query)
          .then((res) => {
            this.getEchart(res.data, 'groupImLogEchart', '群组聊天量')
          })
          .catch(() => {
            this.loading = false
          })
    },
    getEchart(data, id, name){
      var myChart = echarts.init(document.getElementById(id))
      let option;
      option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: data.category,
            axisTick: {
              alignWithLabel: true
            }
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: name,
            type: 'bar',
            barWidth: '60%',
            data: data.value
          }
        ]
      };
      option && myChart.setOption(option);
    },
    setDateFormat(date){
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      return year+'-'+month+'-'+day;
    }

  }
}
</script>
