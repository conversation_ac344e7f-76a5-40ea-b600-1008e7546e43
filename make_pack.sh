#!/bin/bash

# 设定错误处理机制，任何命令出错都将使脚本退出
set -e

# 定义颜色常量，用于打印信息
YELLOW='\033[1;33m'
GREEN='\033[1;32m'
NC='\033[0m' # 无色

# 获取当前目录
CURRENT_DIR=$(pwd)

# 切换到 dev 分支
git checkout dev

# 打印当前分支
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
printf "${YELLOW}当前分支: %s${NC}\n" "$CURRENT_BRANCH"

# 拉代码
git pull origin dev
printf "拉取代码完毕\n"

# 安装依赖
printf "${YELLOW}正在安装依赖...${NC}\n"
npm install

# 构建项目，捕获警告信息，但继续执行后面的命令
printf "${YELLOW}正在构建项目...${NC}\n"
if ! npx vite build; then
    printf "${YELLOW}构建有警告，继续执行后续操作...${NC}\n"
fi

# 构建成功提示
printf "${GREEN}构建流程执行完毕!${NC}\n"

# 移动文件的过程
# 处理 adminbak 和 admin 的移动
cd /var/www/zmxfadmin/public/ && rm -rf adminbak && mv admin/ adminbak
cd /var/www/zmxfadminfront/ && mv admin /var/www/zmxfadmin/public/
cd /var/www/zmxfadmin/public/ && chown -R www:www admin/
printf "${GREEN}文件移动和权限设置完成!${NC}\n"