{"name": "yyla<PERSON><PERSON><PERSON><PERSON>", "version": "4.0.0", "description": "Minimalist background management system based on ThinkPHP and Vue", "keywords": ["yylAdmin", "yylAdminWeb", "php", "vue", "thinkphp", "element-plus"], "author": "https://github.com/skyselang/", "license": "Apache-2.0", "type": "module", "scripts": {"dev": "vite serve --mode development", "build:prod": "vite build --mode production", "build:stage": "vite build --mode staging", "preview:prod": "vite preview --mode production", "preview:stage": "vite preview --mode staging", "lint": "eslint --ext .js,.cjs,.vue ./src", "format": "prettier --write \"**/*.{js,cjs,json,css,less,scss,vue,html,md}\""}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tinymce/tinymce-vue": "4", "@vitejs/plugin-vue": "^4.6.2", "@vueuse/core": "^10.10.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "5.1.10", "@wangeditor/plugin-upload-attachment": "^1.1.0", "axios": "^1.7.2", "clipboardy": "^4.0.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "default-passive-events": "^2.0.0", "echarts": "^5.5.0", "element-plus": "^2.7.4", "js-cookie": "^3.0.5", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^6.2.2", "pinia": "^2.1.7", "terser": "^5.31.1", "tinymce": "5.9.1", "vue": "^3.4.27", "vue-cropper": "1.1.2", "vue-i18n": "^9.13.1", "vue-router": "^4.3.2", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.4", "eslint": "^8.57.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.26.0", "fast-glob": "^3.3.2", "prettier": "^2.8.8", "sass": "1.77.4", "unocss": "^0.58.9", "unplugin-auto-import": "^0.15.3", "unplugin-icons": "^0.16.6", "unplugin-vue-components": "^0.24.1", "vite": "^5.2.12", "vite-plugin-externals": "^0.6.2", "vite-plugin-svg-icons": "^2.0.1"}, "repository": {"type": "git", "url": "https://gitee.com/skyselang/yylAdminWeb.git"}, "bugs": {"url": "https://gitee.com/skyselang/yylAdminWeb/issues"}, "engines": {"node": ">=18.18.0", "npm": ">=8.19.0"}}