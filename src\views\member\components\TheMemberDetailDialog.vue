<template>
  <el-dialog
    v-model="visible"
    title="用户信息"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @open="handleOpen"
    @close="handleClose"
    width="900px"
    :z-index="1200"
    top="2vh"
  >
    <div>
      <el-descriptions title="基础信息" direction="horizontal" :column="1">
        <el-descriptions-item label="用户ID：">{{ userInfo.member_id }}</el-descriptions-item>
        <el-descriptions-item label="openid：">{{
          userInfo.wxopenid || '暂无'
        }}</el-descriptions-item>
        <el-descriptions-item label="unionid：">{{
          userInfo.wxunionid || '暂无'
        }}</el-descriptions-item>
        <el-descriptions-item label="用户昵称：">{{
          userInfo.nickname || '暂无'
        }}</el-descriptions-item>
        <el-descriptions-item label="手机号：">
          {{ currentPhone || '暂无' }}
          <template v-if="userInfo.phone">
            <span class="ml-[10px] cursor-[pointer]">
              <svg-icon icon-class="view" v-if="!isHidePhone" @click="handViewPhone" />
            </span>
          </template>
        </el-descriptions-item>
        <el-descriptions-item label="关注楼盘： ">{{
          userInfo.followHouse || '暂无'
        }}</el-descriptions-item>
      </el-descriptions>
      <div class="mt-[20px] font-bold font-size-[16px] color-[#303133]">浏览记录</div>
      <el-table
        ref="browsehistoryList"
        v-loading="historyTableData.loading"
        :data="historyTableData.list"
        height="350"
      >
        <el-table-column
          prop="createtime"
          label="浏览时间"
          width="160"
          :formatter="customformat('YYYY-MM-DD HH:mm')"
        />
        <el-table-column prop="id" label="ID" width="80" />

        <el-table-column prop="viewtype" label="类型" min-width="60" show-overflow-tooltip />
        <el-table-column label="手机设备及系统" min-width="160" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.devicemodel">{{ row.devicemodel }}</span>
            <br v-if="row.devicemodel" />
            <span v-if="row.devicesystem">{{ row.devicesystem }}</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="housename"
          label="楼盘"
          min-width="170"
          show-overflow-tooltip
          :formatter="defaultFormat()"
        />
        <el-table-column
          prop="relate_content"
          label="关联内容"
          min-width="112"
          :formatter="defaultFormat()"
          show-overflow-tooltip
        />
        <el-table-column
          prop="more_content"
          label="更多内容"
          :formatter="defaultFormat()"
          min-width="112"
          show-overflow-tooltip
        />
      </el-table>
      <pagination
        v-model:total="historyTableData.total"
        v-model:page="historyTableData.page"
        v-model:limit="historyTableData.limit"
        @pagination="getUserHistoryList"
      />
    </div>
  </el-dialog>
</template>

<script setup>
import { memberInfo, getMemberBrowseHistry, getMemberPhoneDtail } from '@/api/member/member'
import { computed, ref } from 'vue'
import { getPageLimit } from '@/utils/settings'
import { defaultFormat } from '@/utils'
import { customformat } from '@/utils/dateUtil'
const STATIC_COFIG = {
  page: 1,
  limit: getPageLimit(),
  loading: false,
  total: 0
}

const emits = defineEmits(['close'])

const visible = defineModel('visible', {
  type: Boolean,
  default: false
})

const props = defineProps({
  id: [String, Number]
})

const userInfo = ref({})

const allPhone = ref('')

const isHidePhone = ref(false)

const historyTableData = ref({ ...STATIC_COFIG, list: [] })

const currentPhone = computed(() => {
  return isHidePhone.value ? allPhone.value : userInfo.value?.phone
})

const getUserInfo = async () => {
  try {
    const { data } = await memberInfo({ member_id: props.id })
    console.log(data)
    userInfo.value = data
  } finally {
  }
}

const getUserHistoryList = async () => {
  historyTableData.loading = true
  try {
    const params = {
      member_id: props.id,
      page: historyTableData.value.page,
      limit: historyTableData.value.limit
    }
    const { data } = await getMemberBrowseHistry(params)
    console.log(data)
    historyTableData.value.list = data.list
    historyTableData.value.total = data.count
  } finally {
    historyTableData.loading = false
  }
}

const handleOpen = () => {
  getUserInfo()
  getUserHistoryList()
}

const handleClose = () => {
  emits('close')
  userInfo.value = {}
  allPhone.value = ''
  isHidePhone.value = false
  historyTableData.value = { ...STATIC_COFIG, list: [] }
}

const handViewPhone = async () => {
  if (allPhone.value) return
  const { data } = await getMemberPhoneDtail({ member_id: props.id })
  allPhone.value = data.phone
  isHidePhone.value = true
}

const handleViewHidePhone = () => (isHidePhone.value = false)
</script>

<style lang="scss" scoped></style>
