import request from '@/utils/request'
// 内容分类
const url = '/admin/im.Im/'
/**
 * 群聊分类列表
 * @param {array} params 请求参数
 */
export function index(params) {
    return request({
        url: url + 'index',
        method: 'get',
        params: params
    })
}
export function testCard(params) {
    return request({
        url: url + 'testCard',
        method: 'get',
        params: params
    })
}
export function imLog(params) {
    return request({
        url: url + 'imLog',
        method: 'get',
        params: params
    })
}
export function imGroupLog(params) {
    return request({
        url: url + 'imGroupLog',
        method: 'get',
        params: params
    })
}