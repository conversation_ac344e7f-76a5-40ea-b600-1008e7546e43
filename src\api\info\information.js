// @/api/info/content.js
import request from '@/utils/request'

// 获取列表
export function getList(params) {
    return request({
        url: '/admin/content/list',
        method: 'get',
        params
    })
}

// 获取详情
export function getDetail(id) {
    return request({
        url: '/admin/content/detail',
        method: 'get',
        params: { id }
    })
}

// 创建内容
export function create(data) {
    return request({
        url: '/admin/content/create',
        method: 'post',
        data
    })
}

// 更新内容
export function update(data) {
    return request({
        url: '/admin/content/update',
        method: 'post',
        data
    })
}

// 删除内容
export function deleteContent(id) {
    return request({
        url: '/admin/content/delete',
        method: 'post',
        data: { id }
    })
}

// 设置发布状态
export function setStatus(data) {
    return request({
        url: '/admin/content/setStatus',
        method: 'post',
        data
    })
}

// 上传图片
export function uploadImage(data) {
    return request({
        url: '/admin/upload/image',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}

// 搜索楼盘
export function searchHouses(params) {
    return request({
        url: '/admin/house/search',
        method: 'get',
        params
    })
}
