import request from '@/utils/request';
// 意见管理
const url = '/admin/suggests.Index/';

/**
 * 意见列表
 * @param {array} params 请求参数
 */
export function suggestsList(params) {
    return request({
      url: url + 'index',
      method: 'get',
      params: params
    })
}
/**
 * 修改意见信息
 * @param {array} data 请求数据
 */
export function editSuggest(data) {
  return request({
    url: url + 'edit',
    method: 'post',
    data
  })
}

/**
 * 修改意见信息
 * @param {array} data 请求数据
 */
export function info(params) {
  return request({
    url: url + 'info',
    method: 'get',
    params: params
  })
}
