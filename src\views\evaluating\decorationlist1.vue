<template>
  <div class="app-container">
    <!-- 查询 -->
    <el-form ref="queryRef" :model="query" :inline="true" label-width="90px">
      <el-form-item label="评测名称：">
        <el-input v-model="query.name" class="ya-search-value" placeholder="户型名称" clearable />
      </el-form-item>
      <el-form-item label="所属楼盘：">
        <el-select
          v-model="query.house_id"
          class="w-full ya-search-value"
          clearable
          filterable
          remote
          :remote-method="houselist"
          placeholder="请选择所属楼盘"
          :loading="loading"
        >
          <el-option
            v-for="item in search_houselist"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发布状态：">
        <el-select v-model="query.release_status" style="width: 120px" clearable placeholder="全部">
          <el-option key="1" label="已发布" value="1" />
          <el-option key="0" label="待发布" value="0" />
        </el-select>
      </el-form-item>
    </el-form>
    <el-row>
      <el-col class="mb-2">
        <el-select v-model="query.date_field" class="ya-date-field" placeholder="时间类型">
          <el-option value="create_time" label="创建时间" />
          <el-option value="update_time" label="修改时间" />
        </el-select>
        <el-date-picker
          v-model="query.date_value"
          type="datetimerange"
          class="ya-date-value"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="[new Date(2024, 1, 1, 0, 0, 0), new Date(2024, 1, 1, 23, 59, 59)]"
        />
        &nbsp;&nbsp;
        <el-button type="primary" @click="search()">查询</el-button>
        <el-button title="重置" @click="refresh()">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" @click="add()">添加</el-button>
      </el-col>
    </el-row>
    <!-- 操作 -->
    <el-row>
      
    </el-row>
    
    <!-- 列表 -->
    <el-table
      ref="table"
      v-loading="loading"
      :data="data"
      :height="height"
      @sort-change="sort"
      @selection-change="select"
    >
      <el-table-column type="selection" width="42" title="全选/反选" />
      <el-table-column :prop="idkey" label="ID" width="80" sortable="custom" />
      <el-table-column prop="name" label="评测名称" min-width="105" show-overflow-tooltip />
      <el-table-column prop="house_name" label="所属楼盘" min-width="105" show-overflow-tooltip>
       
      </el-table-column>
      <el-table-column
        prop="member_score"
        label="评测评分"
        min-width="85"
        show-overflow-tooltip
        sortable="custom"
      />

      <el-table-column prop="update_time" label="更新时间" width="165" sortable="custom" />
      <el-table-column
        prop="professional_score"
        label="用户评分"
        min-width="85"
        show-overflow-tooltip
        sortable="custom"
      />
      <el-table-column
        prop="professional_score"
        label="专家评分"
        min-width="85"
        show-overflow-tooltip
        sortable="custom"
      />
      
      <el-table-column prop="release_status" label="发布状态" min-width="85">
        <template #default="scope">
          <span v-text="search_params['releaseStatus'][scope.row.release_status]"></span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="165">
        <template #default="scope">
          <el-link type="primary" class="mr-1" :underline="false" @click="edit(scope.row)">编辑</el-link>
          |
          <el-link type="primary" class="mr-1" :underline="false" @click="selectOpen('release', scope.row, 'up')" v-if="scope.row.release_status == 2">发布</el-link>
          <el-link type="primary" class="mr-1" :underline="false" @click="selectOpen('release', scope.row, 'down')" v-else >下架</el-link>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="count > 0"
      v-model:total="count"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="list"
    />
    <el-dialog
      v-model="selectDialog"
      :title="selectTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      top="20vh"
    >
      <el-form ref="selectRef" label-width="120px">
       
        <el-form-item label="评测名称">
          <el-input v-model="selectNames" type="textarea" autosize disabled />
        </el-form-item>
        
        <el-form-item :label="name + 'ID'">
          <el-input v-model="selectIds" type="textarea" autosize disabled />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button :loading="loading" @click="selectCancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="selectSubmit(selectIds)">提交</el-button>
      </template>
    </el-dialog>
    <!-- 添加修改 -->
    <el-dialog
      v-model="dialog"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="cancel"
      destroy-on-close
      width="1400px"
      top="5vh"
    >
      <el-form ref="ref" :rules="rules" :model="model" label-width="100px">
        <el-tabs>
          <el-tab-pane label="基础信息">
            <el-scrollbar native :height="height - 80">
              <el-row>
                <el-col :span="11">
                  <el-divider>
                    <h3 class="bold">基础信息</h3>
                  </el-divider>
                  <el-form-item label="所属楼盘" prop="house_id" placeholder="请选择所属楼盘">
                    <el-select
                      v-model="model.house_id"
                      class="w-full"
                      clearable
                      filterable
                      remote
                      placeholder="请选择所属楼盘"
                      :remote-method="houselist"
                      :loading="loading"
                      @change="houseChange"
                    >
                      <el-option
                        v-for="item in search_houselist"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                  
                  <el-form-item label="评测名称" prop="name">
                    <el-input v-model="model.name" placeholder="输入评测名称" clearable> </el-input>
                  </el-form-item>
                  <el-form-item label="样板间视频" prop="videos">
                    <ImgUpload
                      v-model:file-url="model.videos['sample']"
                      file-type="video"
                      :height="100"
                      :width="160"
                      upload
                    />
                    <span>只能上传50M以内的视频</span>
                  </el-form-item>
                  <el-form-item label="交付实景视频" prop="video">
                    <ImgUpload
                      v-model:file-url="model.videos['charge']"
                      file-type="video"
                      :height="100"
                      :width="160"
                      upload
                    />
                    <span>只能上传50M以内的视频</span>
                  </el-form-item>
                  <el-form-item label="户型图" prop="pictures">
                    <ImgUploadsHouseType
                      v-model="model.pictures"
                      upload-btn="上传图片"
                      file-type="image"
                      file-tip=""
                      :source="source"
                    />
                  </el-form-item>
                  
                  
                </el-col>
                <el-col :offset="1" :span="11">
                  <el-divider>
                    <h3 class="bold">评测信息</h3>
                  </el-divider>
                  
                  <el-form-item label="级别(依据装修标准计算级别为：中端)" prop="level"  label-position="top">
                    <el-radio-group v-model="model.level" style="margin-left: 10px">
                      <el-radio label="经济" :value="1"></el-radio>
                      <el-radio label="中端" :value="2"></el-radio>
                      <el-radio label="高端" :value="3"></el-radio>
                      <el-radio label="豪华" :value="4"></el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item
                        label="优缺点描述"  label-position="top"
                        v-for="(item2, indx) in model.features"
                        prop="features"
                      >
                        <el-col :span="4">
                          <el-select v-model="item2.type" class="w-full" filterable>
                            <el-option key="优点" label="优点" :value="1" />
                            <el-option key="缺点" label="缺点" :value="2" />
                          </el-select>
                        </el-col>
                        <el-col :span="10">
                          <el-input v-model="item2.content" placeholder="输入描述（15个汉字以内）" />
                        </el-col>

                        <el-col :span="5" :offset="1">
                          <el-button @click="addPoints(idx)" type="primary">+</el-button>
                          <el-button @click="cancelPoints(idx, indx)" v-if="indx > 0">-</el-button>
                        </el-col>
                      </el-form-item>
                      <el-form-item label="评分描述" prop="basic_rate_desc" label-position="top">
                    <el-input
                      type="textarea"
                      :rows="20"
                      placeholder="输入基础评分描述"
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-scrollbar>
          </el-tab-pane>
          <el-divider><h3 class="bold">交付标准</h3></el-divider>
        </el-tabs>
      </el-form>
      <template #footer>
        <el-button :loading="loading" @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submit">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import screenHeight from '@/utils/screen-height'
import Pagination from '@/components/Pagination/index.vue'
import { arrayColumn } from '@/utils/index'
import { getPageLimit } from '@/utils/settings'
import {

  add,
  edit,
  dele,
  disable
} from '@/api/project/housetype'

import {
  list,
  info,
  release
  
} from '@/api/project/decoration'

import { houseList } from '@/api/project/house'
import { ElMessage } from 'element-plus'

const tagGroupId = 7
export default {
  name: 'decorationlist',
  components: { Pagination },

  data() {
    return {
      name: '装修评测',
      height: 680,
      loading: false,
      idkey: 'id',
      search_params: {},
      search_houselist: [],
      exps: [{ exp: 'like', name: '包含' }],
      realseOpt:"",
      query: {
        page: 1,
        release_status: null,
        name: null,
        house_id: null,
        limit: getPageLimit(),
        date_field: 'create_time'
      },
      data: [],
      count: 0,
      dialog: false,
      dialogTitle: '',
      
      model: {
        id: 0,
        name: '',
        house_id: null,
        videos: [
          {'sample':[]},
          {'charge':[]},
        ],
        level: 0,
        features: [
          {'k':0, 'id':0,'type':1,'content':''}
        ],
        pictures: [],
        
      },
      selection: [],
      selectIds: '',
      selectTitle: '操作',
      selectDialog: false,
      selectType: '',
     
     
    }
  },
  created() {
    this.height = screenHeight()
    if (this.$route.query.house_id) {
      this.query.house_id = parseInt(this.$route.query.house_id)
    }
    this.list()
    if (this.query.house_id) {
      this.houselistbyid(this.query.house_id)
    }
  },
  methods: {
    // 列表
    list() {
      this.loading = true
      list(this.query)
        .then((res) => {
          this.data = res.data.list
          console.log(this.data)
          this.count = res.data.count
          this.exps = res.data.exps
          this.loading = false
          this.search_params = res.data.search_params
        })
        .catch(() => {
          this.loading = false
        })
    },

    // 根据楼盘名称获取楼盘列表
    houselist(keywords) {
      houseList({ name: keywords })
        .then((res) => {
          this.search_houselist = res.data.list
        })
        .catch((error) => {})
    },
    // 根据楼盘名称获取楼盘列表
    houselistbyid(id) {
      houseList({ id: id })
        .then((res) => {
          this.search_houselist = res.data.list
        })
        .catch((error) => {})
    },
    
    // 添加修改
    add() {
      this.dialog = true
      this.dialogTitle = this.name + '添加'
      this.reset()
    },
    edit(row) {
      this.dialog = true
      this.dialogTitle = this.name + '修改：' + row[this.idkey]
      var id = {}
      id[this.idkey] = row[this.idkey]
      info(id)
        .then((res) => {
          
          this.reset(res.data)
          
          this.houselistbyid(res.data.house_id)
        })
        .catch(() => {})
    },
    cancel() {
      this.dialog = false
      this.reset()
    },
    submit() {
      
      this.$refs['ref'].validate((valid) => {
        if (valid) {
          if (this.model[this.idkey]) {
            edit(this.model)
              .then((res) => {
                this.list()
                this.dialog = false
                ElMessage.success(res.msg)
              })
              .catch(() => {})
          } else {
            add(this.model)
              .then((res) => {
                this.list()
                this.dialog = false
                ElMessage.success(res.msg)
              })
              .catch(() => {})
          }
        } else {
          // const errors = this.$refs.myForm.errors;
          // Object.values(errors)[0];
          // console.log(errors)
          ElMessage.error('请完善必填项（带红色星号*）')
        }
      })
    },
    // 重置
    reset(row) {
      if (row) {
        this.model = row
        console.log("===========================================")
        console.log(this.model)
      } else {
        this.model = this.$options.data().model
      }
      
      if (this.$refs['ref'] !== undefined) {
        try {
          this.$refs['ref'].resetFields()
          this.$refs['ref'].clearValidate()
        } catch (error) {
          console.log(error)
        }
      }
    },
    // 查询
    search() {
      this.query.page = 1
      this.list()
    },
    // 刷新
    refresh() {
      const limit = this.query.limit
      this.query = this.$options.data().query
      this.$refs['table'].clearSort()
      this.query.limit = limit
      this.list()
    },
    // 排序
    sort(sort) {
      this.query.sort_field = sort.prop
      this.query.sort_value = ''
      if (sort.order === 'ascending') {
        this.query.sort_value = 'asc'
        this.list()
      }
      if (sort.order === 'descending') {
        this.query.sort_value = 'desc'
        this.list()
      }
    },
    // 操作
    select(selection) {
      this.selection = selection
      this.selectIds = this.selectGetIds(selection).toString()
    },
    selectGetIds(selection) {
      return arrayColumn(selection, this.idkey)
    },
    selectAlert() {
      ElMessageBox.alert('请选择需要操作的' + this.name, '提示', {
        type: 'warning',
        callback: () => {}
      })
    },
    
    selectSubmit(val) {
      
      this.release(val, true)
        
      this.selectDialog = false
      
    },

    // 是否禁用
    disable(row, select = false) {
      if (!row.length) {
        this.selectAlert()
      } else {
        this.loading = true
        var is_disable = row[0].is_disable
        if (select) {
          is_disable = this.is_disable
        }
        disable({
          ids: this.selectGetIds(row),
          is_disable: is_disable
        })
          .then((res) => {
            this.list()
            ElMessage.success(res.msg)
          })
          .catch(() => {
            this.list()
          })
      }
    },
    
    selectOpen(sectType, selectRow = '', opt = '') {
      var optName = opt == 'up' ? '发布' : '下架'
      this.realseOpt = opt
      if (sectType === 'release') {
        this.selectTitle = optName
      }
      this.selectIds = selectRow.id
      this.selectNames = selectRow.name
      this.selectDialog = true
      this.selectType = sectType
    },

    selectCancel() {
      this.selectDialog = false
    },
    addPoints(index) {
      const prosCons = { type: 1, content: '' } // 优点
      this.model.features.push(prosCons)
    },
    cancelPoints(index, index2) {
      this.model.features.splice(index2, 1)
    },
    // 发布时间
    release(row) {
      if (!row) {
        this.selectAlert()
      } else {
        this.loading = true
        release({
          id: row,
          release_status: this.realseOpt == 'up' ? 1 : 2
        })
          .then((res) => {
            this.list()
            ElMessage.success(res.msg)
          })
          .catch(() => {
            this.list()
          })
      }
    },
   
    // 删除
    dele(row) {
      if (!row.length) {
        this.selectAlert()
      } else {
        this.loading = true
        dele({
          ids: this.selectGetIds(row)
        })
          .then((res) => {
            this.list()
            ElMessage.success(res.msg)
          })
          .catch(() => {
            this.loading = false
          })
      }
    }
  }
}
</script>
<style>
.bordered-box {
  border: 1px solid #d3dce6; /* 设置边框样式 */
  border-radius: 4px; /* 可选：设置圆角 */
  padding: 10px; /* 内边距 */
  box-sizing: border-box; /* 确保边框不影响内部内容的宽度 */
  margin-top: 10px;
}
</style>
