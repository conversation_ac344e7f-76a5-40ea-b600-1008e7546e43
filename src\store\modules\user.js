import { defineStore } from 'pinia'
import { resetRouter } from '@/router'
import { store } from '@/store'
import { useStorage } from '@vueuse/core'
import { useSettingsStore } from '@/store/modules/settings'
import { login as loginApi, logout as logout<PERSON><PERSON> } from '@/api/system/login'
import { info as userInfo<PERSON>pi } from '@/api/system/user-center'

export const useUserStore = defineStore('user', () => {
  const token = useStorage('AdminToken', '')
  const user = reactive({
    username: '',
    nickname: '',
    avatar_url: '',
    roles: [],
    forcechangepwd: 0,
    menus: []
  })

  // 登录
  function login(data) {
    return new Promise((resolve, reject) => {
      loginApi(data)
        .then((res) => {
          const data = res.data
          const settingsStore = useSettingsStore()
          const tokenName = settingsStore.tokenName
          token.value = data[tokenName]
          resolve(data)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  // 用户信息
  function userInfo() {
    return new Promise((resolve, reject) => {
      userInfoApi()
        .then(({ data }) => {
          if (!data) {
            reject('Verification failed, please Login again.')
            return
          }
          if (!data.roles || data.roles.length <= 0) {
            reject('userInfo: roles must be a non-null array!')
            return
          }
          user.nickname = data.nickname
          user.username = data.username
          user.avatar_url = data.avatar_url
          user.roles = data.roles
          user.menus = data.menus
          user.forcechangepwd = data.forcechangepwd
          resolve(data)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  // 退出
  function logout() {
    return new Promise((resolve, reject) => {
      logoutApi()
        .then(() => {
          token.value = ''
          location.reload() // 清空路由
          resolve()
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  // 移除token
  function resetToken() {
    return new Promise((resolve) => {
      token.value = ''
      resetRouter()
      resolve()
    })
  }

  return {
    token,
    user,
    login,
    userInfo,
    logout,
    resetToken
  }
})

// 非setup
export function useUserStoreHook() {
  return useUserStore(store)
}
