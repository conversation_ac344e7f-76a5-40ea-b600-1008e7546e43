import request from '@/utils/request'
// 内容分类
const url = '/admin/im.Speechcraft/'
/**
 * emoji列表
 * @param {array} params 请求参数
 */
export function list(params) {
    return request({
        url: url + 'list',
        method: 'get',
        params: params
    })
}
/**
 * emoji详情
 * @param {array} params 请求参数
 */
export function info(params) {
    return request({
        url: url + 'info',
        method: 'get',
        params: params
    })
}
/**
 * emoji添加
 * @param {array} data 请求数据
 */
export function add(data) {
    return request({
        url: url + 'add',
        method: 'post',
        data
    })
}
/**
 * emoji修改
 * @param {array} data 请求数据
 */
export function edit(data) {
    return request({
        url: url + 'edit',
        method: 'post',
        data
    })
}
/**
 * emoji删除
 * @param {array} data 请求数据
 */
export function dele(data) {
    return request({
        url: url + 'dele',
        method: 'post',
        data
    })
}

