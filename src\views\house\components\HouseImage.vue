<template>
  <div class="app-container">
    <!-- 查询 -->
    <el-row>
      <el-col class="mb-2">
        <el-form-item label="楼盘名称"> 楼盘名称 </el-form-item>
        <el-button type="primary" title="添加文件" @click="add()">添加</el-button>
        <el-divider></el-divider>
      </el-col>
    </el-row>
    <!-- 操作 -->
    <el-row>
      <el-col>
        <!-- 无内容 -->
      </el-col>
    </el-row>
    <el-dialog
      v-model="selectDialog"
      :title="selectTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      top="20vh"
      append-to-body
    >
      <el-form label-width="120px">
        <el-form-item v-if="selectType === 'dele'">
          <span class="ya-margin-left c-red">
            确定要删除选中的{{ name }}吗？删除会对已使用该文件的业务造成影响！
          </span>
        </el-form-item>

        <el-form-item :label="name + 'ID'">
          <el-input v-model="selectIds" type="textarea" autosize disabled />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="selectCancel">取消</el-button>
        <el-button type="primary" @click="selectSubmit">提交</el-button>
      </template>
    </el-dialog>

    <!-- 列表筛选 -->
    <el-row :gutter="3">
      <!-- 筛选 -->
      <el-col :span="2">
        <el-scrollbar native :height="height">
          <!-- 类型筛选 -->
          <el-row>
            <el-col>
              <el-text size="default">类型：</el-text>
            </el-col>
            <el-col class="ya-padding-left">
              <el-link
                :type="query.pictype === '' ? 'primary' : 'default'"
                :underline="false"
                @click="typeSelect('')"
              >
                全部
              </el-link>
            </el-col>
            <el-col v-for="(item, index) in pictypes" :key="index" class="ya-padding-left">
              <el-link
                :type="query.pictype === index ? 'primary' : 'default'"
                :underline="false"
                @click="typeSelect(index)"
              >
                {{ item }}
              </el-link>
            </el-col>
          </el-row>
        </el-scrollbar>
      </el-col>
      <!-- 卡片展示 -->
      <template v-if="showMode == 'card'">
        <el-checkbox-group
          v-model="selection"
          style="display: contents; line-height: 1.5"
          @change="select"
        >
          <el-col v-if="count > 0" :span="22">
            <el-scrollbar native :height="height">
              <el-row v-loading="loading" :gutter="3" class="mt-[6px] !mr-0 !ml-0">
                <el-col
                  v-for="(item, index) in data"
                  :key="index"
                  :span="4"
                  style="margin-bottom: 6px; text-align: center"
                >
                  <el-card
                    :body-style="{
                      minWidth: '16.0%',
                      height: (height - height * 0.1) / 3 + 'px',
                      minHeight: '180px',
                      padding: '0 6px'
                    }"
                  >
                    <div style="text-align: left">
                      <el-checkbox :key="item[idkey]" :value="item[idkey]" :label="item[idkey]">
                        {{ pictypes[item['pictype']] }} ({{ item[idkey] }})
                      </el-checkbox>
                    </div>
                    <div
                      :style="{
                        width: '100%',
                        height:
                          (height - height * 0.1) / 3 - ((height - height * 0.1) / 3) * 0.5 + 'px',
                        minHeight: '62px'
                      }"
                    >
                      <el-image
                        style="height: 100%"
                        :src="item.path"
                        :preview-src-list="fileImgPre"
                        :initial-index="imagePreIndex(item.path)"
                        fit="contain"
                        title="点击看大图"
                        lazy
                      />
                    </div>
                    <div :style="{ paddingTop: '5px', minHeight: '50px' }">
                      <p>
                        <el-text :title="item.name + '.' + item.file_ext" size="default" truncated>
                          {{ item.name }}
                        </el-text>
                      </p>
                      <div>
                        <el-link
                          type="primary"
                          class="mr-1"
                          :underline="false"
                          @click="fileDownload(item)"
                        >
                          下载
                        </el-link>
                        <el-link
                          type="primary"
                          class="mr-1"
                          :underline="false"
                          @click="edit(item)"
                          :disabled="item.pictype == 2"
                        >
                          修改
                        </el-link>
                        <el-link
                          :disabled="item.pictype == 2"
                          type="primary"
                          :underline="false"
                          @click="selectOpen('dele', [item.id])"
                        >
                          删除
                        </el-link>
                      </div>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </el-scrollbar>
          </el-col>
          <el-col v-else :span="22">
            <el-empty :description="'暂无' + name" />
          </el-col>
        </el-checkbox-group>
      </template>
    </el-row>
    <!-- 分页 -->
    <pagination
      v-show="count > 0"
      v-model:total="count"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="list"
    />
    <!-- 添加修改 -->
    <el-dialog
      v-model="dialog"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="cancel"
      top="5vh"
      append-to-body
    >
      <el-scrollbar native :height="height - 30">
        <el-form ref="ref" :rules="rules" :model="model" label-width="100px">
          <el-form-item label="图片类型" prop="pictype">
            <el-select v-model="model.pictype" :disabled="model.id != 0">
              <el-option
                v-for="(item, index) in pictypes"
                :key="index"
                :value="index"
                :label="item"
                :disabled="index == 2"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="图片名称" prop="name">
            <el-input v-model="model.name" placeholder="图片名称" clearable />
          </el-form-item>

          <!-- <el-form-item label="封面图" prop="is_cover" v-if="model.id > 0">
		    <el-radio-group
		      v-model="model.is_cover"
		      style="margin-left: 10px"
		    >
		      <el-radio label="否" :value="0"></el-radio>
		      <el-radio label="是" :value="1"></el-radio>
		    </el-radio-group>
		  </el-form-item> -->
          <el-form-item label="多图" prop="is_set" v-if="model.id == 0">
            <el-radio-group v-model="is_set" style="margin-left: 10px" @change="picSetChange">
              <el-radio label="否" :value="0"></el-radio>
              <el-radio label="是" :value="1"></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否添加水印" prop="is_watermark" v-if="model.id == 0">
            <el-radio-group v-model="is_watermark" style="margin-left: 10px">
              <el-radio label="否" :value="0" disabled></el-radio>
              <el-radio label="是" :value="1"></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="model.remark" placeholder="请输入备注" clearable />
          </el-form-item>
          <el-form-item label="图片" prop="file_id" v-if="is_set == 0">
            <FileImage
              v-model="model.file_id"
              :file-url="model.path"
              :height="150"
              upload
              :isWatermark="is_watermark"
              :source="source"
            />
          </el-form-item>

          <el-form-item label="图片列表" prop="images" v-if="is_set == 1">
            <FileUploads
              v-model="model.images"
              upload-btn="上传图片"
              file-type="image"
              file-tip="图片文件"
              :isWatermark="is_watermark"
              :source="source"
            />
          </el-form-item>

          <el-form-item v-if="model.id" label="添加时间" prop="create_time">
            <el-input v-model="model.create_time" disabled />
          </el-form-item>
          <el-form-item v-if="model.id" label="修改时间" prop="update_time">
            <el-input v-model="model.update_time" disabled />
          </el-form-item>
          <el-form-item v-if="model.delete_time" label="删除时间" prop="delete_time">
            <el-input v-model="model.delete_time" disabled />
          </el-form-item>
        </el-form>
      </el-scrollbar>
      <template #footer>
        <el-button :loading="loading" @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submit">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import screenHeight from '@/utils/screen-height'
import Pagination from '@/components/Pagination/index.vue'
import clip from '@/utils/clipboard'
import { useSettingsStoreHook } from '@/store/modules/settings'
import { useUserStoreHook } from '@/store/modules/user'
import { arrayColumn } from '@/utils/index'
import { list, info, add, edit, dele, editSet, setlist } from '@/api/project/houseImge'

export default {
  name: '',
  components: { Pagination },
  props: {
    isRecycle: { type: Number, default: 0 },
    pictype: { type: String, default: '' }
  },
  props: {
    houseId: {
      type: String,
      default: '0'
    }
  },
  data() {
    return {
      name: '文件',
      recycle: 0, // 是否回收站
      height: 680,
      loading: false,
      is_tao: 0, // 是否是套图
      idkey: 'id',
      exps: [],
      picsets: [], // 套图列表
      query: {
        page: 1,
        limit: 18,
        storage: '',
        pictype: '',
        is_disable: '',
        search_field: 'name',
        search_exp: 'like',
        date_field: 'create_time',
        sort_field: 'id',
        sort_value: '',
        house_id: 0
      },
      data: [],
      count: 0,
      showMode: 'card',
      dialog: false,
      dialogTitle: '',
      dialogSet: false,
      dialogTitleSet: '新增套图',
      model: {
        id: 0,
        file_id: '',
        name: '',
        pictype: '',
        path: '',
        remark: '',
        sort: 250,
        is_disable: 0,
        is_delete: 0,
        is_cover: 0,
        house_id: 0,
        images: []
      },

      rules: {
        pictype: [{ required: true, message: '请选择图片类型', trigger: 'blur' }],
        file_url: [{ required: true, message: '请输入文件链接', trigger: 'blur' }]
      },
      fileIds: [],
      storages: [],
      pictypes: [],
      fileImgPre: [],
      selectAll: false,
      selectAllInd: false,
      selection: [],
      selectIds: '',
      selectTitle: '操作',
      selectDialog: false,
      selectType: '',
      group_id: 0,
      tag_ids: [],
      domain: '',
      pictype: '',
      is_disable: 0,
      uploadAction: add(),
      uploadHeaders: {},
      uploadData: {},
      uploadLimit: 9,
      uploadAccept: '',
      uploadFilelist: [],
      uploadCount: 0,
      uploadNumber: 0,
      is_set: 0, // 0 上传单图 1 上传多图
      is_watermark: 1,
      source: 1
    }
  },
  watch: {
    isRecycle: {
      handler(value) {
        this.recycle = value
        this.list()
      }
    },
    pictype: {
      handler(value) {
        this.recycle = this.isRecycle
        this.query.pictype = value
        this.query.page = 1

        this.list()
      }
    }
  },

  created() {
    this.height = screenHeight() - 10
    this.recycle = this.isRecycle
    if (this.pictype) {
      this.query.is_disable = 0
      this.query.pictype = this.pictype
      if (this.pictype == 'all') {
        this.query.pictype = ''
      }
      this.height = this.height - 145
    }
    const settingsStore = useSettingsStoreHook()
    const userStore = useUserStoreHook()
    const tokenType = settingsStore.tokenType
    const tokenName = settingsStore.tokenName
    const tokenValue = userStore.token
    if (tokenType === 'header') {
      const uploadHeaders = {}
      uploadHeaders[tokenName] = tokenValue
      this.uploadHeaders = uploadHeaders
    } else {
      const uploadData = { group_id: 0 }
      uploadData[tokenName] = tokenValue
      this.uploadData = uploadData
    }
    this.query.house_id = this.houseId
    this.query.limit = 18
    this.list()
  },
  methods: {
    checkPermission,
    // 列表
    list() {
      this.loading = true
      if (this.recycle) {
        recycle(this.query)
          .then((res) => {
            this.listData(res.data)
          })
          .catch(() => {
            this.loading = false
          })
      } else {
        list(this.query)
          .then((res) => {
            this.listData(res.data)
          })
          .catch(() => {
            this.loading = false
          })
      }
    },
    // 套图列表
    getSetList() {
      var postdata = {}
      postdata['house_id'] = this.houseId
      if (this.model.pictype != 2) {
        ElMessage({
          message: '只有户型图才可以创建图集',
          type: 'info'
        })
        return
      }
      postdata['pictype'] = this.model.pictype
      this.loading = true
      setlist(postdata)
        .then((res) => {
          this.picsets = res.data
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    listData(data) {
      this.data = data.list
      this.count = data.count
      this.fileIds = data.ids
      this.exps = data.exps
      this.pictypes = data.setting.pictypes
      this.imagePreview(data.list)
      this.loading = false
    },
    // 切换套图/图片
    picSetChange(value) {
      if (value == 0) {
        this.showPicList = false
        this.showOnePic = true
      } else {
        this.showPicList = true
        this.showOnePic = false
        var imgobj = {}
        imgobj['img_id'] = this.model.id
        imgobj['file_url'] = this.model.path
        imgobj['file_id'] = this.model.file_id
        imgobj['file_type'] = 'image'
        imgobj['file_type_name'] = '图片'
        imgobj['file_ext'] = 'jpg'
        imgobj['file_name'] = this.model.name
        if (this.model.hasOwnProperty('images')) {
          // 属性存在的逻辑处理
          this.model.images = []
        } else {
          // 属性不存在的逻辑处理
          this.model.images = []
        }
        // this.model.images.push(imgobj)
        this.model.images = []
      }
    },
    // 切换套图/图片
    picTypeChange(value) {
      if (value == 2) {
        this.picSetChange(1)
      } else {
        this.is_set = 0
        this.picSetChange(0)
      }
    },
    // 添加修改
    add() {
      this.dialog = true
      this.dialogTitle = this.name + '添加'
      this.reset()
      this.model.type = 'url'
    },
    // 添加修改
    addNewSet() {
      this.dialogSet = true
      // this.dialogTitle = this.name + '添加'
      // this.reset()
      // this.model.type = 'url'
    },
    edit(row) {
      this.dialog = true
      this.dialogTitle = this.name + '修改：' + row.id
      info({
        id: row.id
      })
        .then((res) => {
          this.reset(res.data)
        })
        .catch(() => {})
    },
    cancel() {
      this.dialog = false
      this.reset()
    },

    submit() {
      this.model.house_id = this.houseId

      this.$refs['ref'].validate((valid) => {
        if (valid) {
          this.loading = true
          if (this.model.id) {
            edit(this.model)
              .then((res) => {
                this.list()
                this.reset()
                this.dialog = false
                ElMessage.success(res.msg)
              })
              .catch(() => {
                this.loading = false
              })
          } else {
            edit(this.model)
              .then((res) => {
                this.list()
                this.reset()
                this.dialog = false
                ElMessage.success(res.msg)
              })
              .catch(() => {
                this.loading = false
              })
          }
          this.is_set = 0
        }
      })
    },

    // 查询
    search() {
      this.query.page = 1
      this.list()
    },
    // 刷新
    refresh() {
      const limit = this.query.limit
      this.query = this.$options.data().query
      this.query.limit = limit
      this.reset()
      this.list()
    },
    // 排序
    sort(sort) {
      if (this.showMode === 'card') {
        if (this.query.sort_value && this.query.sort_value) {
          this.list()
        }
      }
    },
    // 重置
    reset(row) {
      if (row) {
        this.model = row
      } else {
        this.model = this.$options.data().model
      }
      this.selection = []
      this.selectIds = ''
      this.selectAll = false
      this.selectAllInd = false
      if (this.$refs['ref'] !== undefined) {
        this.$refs['ref'].resetFields()
      }
      if (this.$refs['table'] !== undefined) {
        try {
          this.$refs['table'].clearSelection()
          this.$refs['table'].clearSort()
        } catch (error) {}
      }
    },

    // 选择操作
    select(selection) {
      this.selection = selection
      this.selectIds = this.selectGetIds().toString()
      this.selectAll = selection.length === this.fileIds.length
      this.selectAllInd = selection.length > 0 && selection.length < this.fileIds.length
    },
    selectAlls(value) {
      if (value) {
        this.selection = this.fileIds
        if (this.$refs['table'] !== undefined) {
          this.$refs['table'].toggleAllSelection()
        }
      } else {
        this.selection = []
        if (this.$refs['table'] !== undefined) {
          try {
            this.$refs['table'].clearSelection()
          } catch (error) {}
        }
      }
      this.select(this.selection)
    },
    selectGetIds() {
      if (this.showMode === 'card') {
        return this.selection
      } else {
        return arrayColumn(this.selection, this.idkey)
      }
    },
    selectAlert() {
      ElMessageBox.alert('请选择需要操作的' + this.name, '提示', {
        type: 'warning',
        callback: () => {}
      })
    },
    selectOpen(selectType, selectRow = '') {
      if (selectRow) {
        if (this.showMode === 'card') {
          this.selection = []
        } else {
          if (this.$refs['table'] !== undefined) {
            try {
              this.$refs['table'].clearSelection()
            } catch (error) {}
          }
        }
        this.select(selectRow)
      }
      if (!this.selection.length) {
        this.selectAlert()
      } else {
        this.selectTitle = '操作'
        if (selectType === 'dele') {
          this.selectTitle = this.name + '删除'
        }
        this.selectDialog = true
        this.selectType = selectType
      }
    },
    selectCancel() {
      this.selectDialog = false
    },
    selectSubmit() {
      if (!this.selection.length) {
        this.selectAlert()
      } else {
        const selectType = this.selectType
        if (selectType === 'dele') {
          this.dele()
        }
        this.selectDialog = false
      }
    },

    // 删除
    dele() {
      if (!this.selection.length) {
        this.selectAlert()
      } else {
        this.loading = true
        if (this.recycle) {
          recycleDele({
            ids: this.selectGetIds()
          })
            .then((res) => {
              this.list()
              this.reset()
              ElMessage.success(res.msg)
            })
            .catch(() => {
              this.loading = false
            })
        } else {
          dele({
            ids: this.selectGetIds()
          })
            .then((res) => {
              this.list()
              this.reset()
              ElMessage.success(res.msg)
            })
            .catch(() => {
              this.loading = false
            })
        }
      }
    },
    // 图片预览
    imagePreview(list) {
      console.log('图片预览')
      var preview = []
      const length = list.length
      for (let index = 0; index < length; index++) {
        preview.push(list[index]['path'])
      }
      this.fileImgPre = preview
    },
    imagePreIndex(fileUrl) {
      return this.fileImgPre.indexOf(fileUrl)
    },
    // 文件下载
    fileDownload(file) {
      clip(file.name, '文件名复制成功')
      setTimeout(() => {
        window.open(file.path, '_blank')
      }, 500)
    },

    // 类型筛选
    typeSelect(pictype = '') {
      this.query.pictype = pictype
      this.list()
    },

    // 复制
    copy(text) {
      clip(text)
    },
    // 单元格双击复制
    cellDbclick(row, column) {
      this.copy(row[column.property])
    }
  }
}
</script>
