# 品牌下拉选中文字显示修复说明

## 问题描述

品牌下拉选中后，文字没有显示出来。

## 问题原因

### 1. 数据结构不匹配
```javascript
// 问题：v-model 绑定的是对象数组
v-model="currentItem.brand_list"  // [{id: 1}, {id: 2}]

// 但 el-option 的 value 是 ID
:value="item.id"  // 1, 2, 3...
```

### 2. 自定义标签模板覆盖
使用了 `#tag` 模板后，Element Plus 的默认标签显示被覆盖，但数据结构不匹配导致无法正确显示。

### 3. 移除逻辑错误
`handleRemoveBrand` 函数中过滤的是对象数组，但实际需要过滤的是 ID 数组。

## 修复方案

### 1. 添加计算属性处理数据转换

```javascript
// 计算属性：选中的品牌ID列表
const selectedBrandIds = computed({
  get() {
    if (!currentItem.value.brand_list) return []
    // 如果 brand_list 是对象数组，提取 ID
    if (Array.isArray(currentItem.value.brand_list) && currentItem.value.brand_list.length > 0) {
      if (typeof currentItem.value.brand_list[0] === 'object') {
        return currentItem.value.brand_list.map(item => item.id)
      }
      // 如果已经是 ID 数组，直接返回
      return currentItem.value.brand_list
    }
    return []
  },
  set(newIds) {
    // 将 ID 数组转换为对象数组
    currentItem.value.brand_list = newIds.map(id => ({ id }))
  }
})
```

### 2. 修改 v-model 绑定

```vue
<!-- 修复前 -->
<el-select v-model="currentItem.brand_list">

<!-- 修复后 -->
<el-select v-model="selectedBrandIds" @change="handleBrandChange">
```

### 3. 修复自定义标签模板

```vue
<!-- 修复前 -->
<template #tag>
  <el-tag v-for="item in (currentItem.brand_list || [])" :key="item.id">
    {{ getUnionBrand(item.id) }}
  </el-tag>
</template>

<!-- 修复后 -->
<template #tag>
  <el-tag v-for="brandId in selectedBrandIds" :key="brandId">
    {{ getUnionBrand(brandId) }}
  </el-tag>
</template>
```

### 4. 简化移除品牌逻辑

```javascript
// 修复前
const handleRemoveBrand = (brandId) => {
  currentItem.value.brand_list = currentItem.value.brand_list.filter(item => item && item.id !== brandId)
}

// 修复后
const handleRemoveBrand = (brandId) => {
  const newIds = selectedBrandIds.value.filter(id => id !== brandId)
  selectedBrandIds.value = newIds
}
```

## 数据流程

### 1. 选择品牌时
```
用户选择品牌 → selectedBrandIds.value = [1, 2, 3] → setter 触发 → currentItem.brand_list = [{id:1}, {id:2}, {id:3}]
```

### 2. 显示品牌时
```
currentItem.brand_list = [{id:1}, {id:2}] → getter 触发 → selectedBrandIds.value = [1, 2] → 模板显示品牌名称
```

### 3. 移除品牌时
```
点击关闭按钮 → handleRemoveBrand(1) → selectedBrandIds.value = [2] → setter 触发 → currentItem.brand_list = [{id:2}]
```

## 完整的修复代码

### 模板部分
```vue
<el-select 
  v-model="selectedBrandIds" 
  multiple
  placeholder="请选择品牌"
  @change="handleBrandChange"
>
  <el-option
    v-for="item in brandOptions"
    :key="item.id"
    :label="item.union_brand"
    :value="item.id"
  />

  <template #tag>
    <el-tag
      v-for="brandId in selectedBrandIds"
      :key="brandId"
      type="primary"
      closable
      @close="handleRemoveBrand(brandId)"
      class="brand-tag"
    >
      {{ getUnionBrand(brandId) }}
    </el-tag>
  </template>
</el-select>
```

### 脚本部分
```javascript
// 计算属性
const selectedBrandIds = computed({
  get() {
    if (!currentItem.value.brand_list) return []
    if (Array.isArray(currentItem.value.brand_list) && currentItem.value.brand_list.length > 0) {
      if (typeof currentItem.value.brand_list[0] === 'object') {
        return currentItem.value.brand_list.map(item => item.id)
      }
      return currentItem.value.brand_list
    }
    return []
  },
  set(newIds) {
    currentItem.value.brand_list = newIds.map(id => ({ id }))
  }
})

// 处理函数
const handleBrandChange = (newIds) => {
  console.log('品牌选择变化:', newIds)
}

const handleRemoveBrand = (brandId) => {
  const newIds = selectedBrandIds.value.filter(id => id !== brandId)
  selectedBrandIds.value = newIds
  console.log('移除品牌:', getUnionBrand(brandId))
}
```

## 测试验证

### 1. 选择品牌测试
1. 打开品牌下拉列表
2. 选择多个品牌
3. 确认选中的品牌正确显示为标签

### 2. 移除品牌测试
1. 选择多个品牌
2. 点击任意品牌标签的关闭按钮
3. 确认该品牌被移除，其他品牌保持选中

### 3. 数据一致性测试
1. 选择品牌后查看 `currentItem.brand_list`
2. 确认数据格式为 `[{id: 1}, {id: 2}]`
3. 提交表单时数据格式正确

### 4. 编辑回显测试
1. 编辑已有记录
2. 确认品牌正确回显
3. 可以正常添加和移除品牌

## 关键改进点

1. **数据转换**：通过计算属性自动处理 ID 数组和对象数组的转换
2. **显示正确**：修复了选中品牌不显示的问题
3. **操作简化**：移除品牌的逻辑更加简洁
4. **兼容性好**：支持不同的数据格式输入
5. **调试友好**：添加了详细的日志输出

## 验收标准

✅ 选择品牌后正确显示品牌名称标签
✅ 点击标签关闭按钮可以移除单个品牌
✅ 数据格式与后端接口保持一致
✅ 编辑时品牌正确回显
✅ 支持多选、清空等原有功能
✅ 控制台有清晰的调试信息
