<template>
  <div class="test-container">
    <h2>位置类型联动测试</h2>
    
    <el-card style="margin-bottom: 20px;">
      <template #header>
        <span>测试控制面板</span>
      </template>
      
      <div style="margin-bottom: 20px;">
        <el-button type="primary" @click="testEdit1">测试编辑1（类型存在）</el-button>
        <el-button type="primary" @click="testEdit2">测试编辑2（类型不存在）</el-button>
        <el-button @click="testCreate">测试新建</el-button>
        <el-button @click="clearForm">清空表单</el-button>
      </div>
      
      <div>
        <strong>当前状态：</strong>
        <span>模式: {{ isCreateMode ? '新建' : '编辑' }}</span> | 
        <span>位置ID: {{ form.position_id }}</span> | 
        <span>类型ID: {{ form.category_id }}</span>
      </div>
    </el-card>

    <el-card>
      <template #header>
        <span>表单测试</span>
      </template>
      
      <el-form :model="form" label-width="120px">
        <el-form-item label="位置">
          <el-select 
            v-model="form.position_id" 
            placeholder="请选择位置"
            @change="handlePositionChange"
            style="width: 300px;"
          >
            <el-option
              v-for="item in positions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="类型">
          <el-select 
            v-model="form.category_id" 
            placeholder="请选择类型"
            :disabled="!form.position_id"
            :loading="categoriesLoading"
            style="width: 300px;"
          >
            <el-option
              v-for="item in categories"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <div style="margin-top: 20px;">
        <h4>调试信息：</h4>
        <div style="background: #f5f5f5; padding: 10px; border-radius: 4px;">
          <div><strong>位置列表：</strong> {{ JSON.stringify(positions) }}</div>
          <div><strong>类型列表：</strong> {{ JSON.stringify(categories) }}</div>
          <div><strong>表单数据：</strong> {{ JSON.stringify(form) }}</div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'PositionCategoryTest'
})

// 模拟数据
const mockPositions = [
  { id: 1, name: '客厅' },
  { id: 2, name: '卧室' },
  { id: 3, name: '厨房' },
  { id: 4, name: '卫生间' }
]

const mockCategories = {
  1: [ // 客厅
    { id: 1, name: '地板' },
    { id: 2, name: '墙面' },
    { id: 3, name: '吊顶' },
    { id: 4, name: '门窗' }
  ],
  2: [ // 卧室
    { id: 1, name: '地板' },
    { id: 2, name: '墙面' },
    { id: 4, name: '门窗' },
    { id: 5, name: '衣柜' }
  ],
  3: [ // 厨房
    { id: 2, name: '墙面' },
    { id: 6, name: '橱柜' },
    { id: 7, name: '台面' },
    { id: 8, name: '电器' }
  ],
  4: [ // 卫生间
    { id: 2, name: '墙面' },
    { id: 9, name: '洁具' },
    { id: 10, name: '防水' }
  ]
}

// 响应式数据
const form = reactive({
  position_id: '',
  category_id: ''
})

const positions = ref(mockPositions)
const categories = ref([])
const categoriesLoading = ref(false)
const isCreateMode = ref(true)

// 模拟加载类型数据
const loadCategories = async (positionId) => {
  categoriesLoading.value = true
  
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500))
  
  categories.value = mockCategories[positionId] || []
  categoriesLoading.value = false
  
  console.log(`加载位置${positionId}的类型数据:`, categories.value)
}

// 位置改变处理
const handlePositionChange = async (positionId) => {
  console.log('=== 位置改变 ===')
  console.log('新位置ID:', positionId)
  console.log('当前模式:', isCreateMode.value ? '新建' : '编辑')
  console.log('当前选中的类型ID:', form.category_id)
  
  // 保存当前选中的类型ID
  const currentCategoryId = form.category_id
  
  if (positionId) {
    // 加载新位置对应的类型数据
    await loadCategories(positionId)
    
    // 检查新的类型列表是否包含当前选中的类型ID
    if (currentCategoryId) {
      const categoryExists = categories.value.find(item => item.id == currentCategoryId)
      console.log('当前类型是否存在于新列表中:', categoryExists)
      
      if (categoryExists) {
        // 如果新列表包含当前类型，保持选中状态
        form.category_id = currentCategoryId
        console.log('✅ 保持类型选择:', currentCategoryId)
        ElMessage.success(`保持类型选择: ${categoryExists.name}`)
      } else {
        // 如果新列表不包含当前类型，清空选择
        form.category_id = ''
        console.log('❌ 清空类型选择，因为新列表中不存在')
        ElMessage.warning('当前类型在新位置中不存在，已清空选择')
      }
    } else {
      // 如果当前没有选中类型，在新建模式下保持清空状态
      if (isCreateMode.value) {
        form.category_id = ''
      }
    }
  } else {
    // 如果没有选择位置，清空类型列表和选择
    categories.value = []
    form.category_id = ''
    console.log('清空位置，同时清空类型')
  }
  
  console.log('位置改变处理完成，最终类型ID:', form.category_id)
  console.log('=== 处理结束 ===')
}

// 测试函数
const testEdit1 = () => {
  console.log('\n🧪 测试编辑1：类型存在于新列表')
  isCreateMode.value = false
  form.position_id = 1 // 客厅
  form.category_id = 1 // 地板（在客厅和卧室都存在）
  loadCategories(1)
  ElMessage.info('设置为编辑模式：客厅-地板，现在切换到卧室试试')
}

const testEdit2 = () => {
  console.log('\n🧪 测试编辑2：类型不存在于新列表')
  isCreateMode.value = false
  form.position_id = 1 // 客厅
  form.category_id = 3 // 吊顶（只在客厅存在）
  loadCategories(1)
  ElMessage.info('设置为编辑模式：客厅-吊顶，现在切换到卧室试试')
}

const testCreate = () => {
  console.log('\n🧪 测试新建模式')
  isCreateMode.value = true
  form.position_id = ''
  form.category_id = ''
  categories.value = []
  ElMessage.info('设置为新建模式，选择位置和类型后再切换位置试试')
}

const clearForm = () => {
  form.position_id = ''
  form.category_id = ''
  categories.value = []
  ElMessage.info('表单已清空')
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

h2 {
  color: #303133;
  margin-bottom: 20px;
}

h4 {
  margin: 10px 0;
  color: #606266;
}
</style>
