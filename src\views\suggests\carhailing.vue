<template>
      <div class="app-container">
    <el-table
    ref="table"
    v-loading="loading"
    :data="data"
    :height="height"
    @sort-change="sort"
    @selection-change="select"
  >
    <el-table-column type="selection" width="42" title="全选/反选" />
    <el-table-column :prop="idkey" label="ID" width="80" sortable="custom" />
    <el-table-column prop="name" label="用户姓名" min-width="100" />
    <el-table-column prop="phone" label="用户手机号" min-width="150" show-overflow-tooltip />

   
    <el-table-column prop="projectname" label="项目名称" min-width="160" show-overflow-tooltip />
    <el-table-column prop="status" label="约看状态" min-width="110" show-overflow-tooltip >
      <template #default="scope"> 
          <el-text v-if="scope.row.status == 0" type="success">待处理</el-text>
          <el-text v-else-if="scope.row.status == 1" type="warning">已约看</el-text>
          <el-text v-else-if="scope.row.status == 2" type="warning">已取消</el-text>
          <el-text v-else>-</el-text>
      </template>
    </el-table-column>
    <el-table-column prop="appointdate" label="预约日期" :formatter='customformat("YYYY-MM-DD")' min-width="110" show-overflow-tooltip />
   
    <el-table-column prop="createtime" label="提交时间" min-width="165" sortable="custom" />
    <el-table-column label="操作" width="95">
        <template #default="scope">
        <el-link type="primary" class="mr-1" :underline="false" @click="infoCarhailing(scope.row)">
          编辑
        </el-link>
        
      </template>
    </el-table-column>
  </el-table>
  <!-- 分页 -->
  <pagination
    v-show="count > 0"
    v-model:total="count"
    v-model:page="query.page"
    v-model:limit="query.limit"
    @pagination="carhailingList"
  />
  
<!-- 添加修改 -->
<el-dialog
      v-model="dialog"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="cancel"
      top="5vh"
    >
      <el-form ref="editform"  :model="model" label-width="100px">
        <el-tabs>
          <el-tab-pane label="信息">
            <el-scrollbar native :height="height - 80">
              
              <el-form-item label="姓名" prop="name">
                <el-input v-model="model.name" placeholder="请输入名称" disabled />
              </el-form-item>
              <el-form-item label="手机号" prop="phone">
                <el-input v-model="model.phone" placeholder="请输入手机号" disabled />
              </el-form-item>
             
              <el-form-item  label="约看状态">
          
                <el-select v-model="model.status" style="width: 120px">
                  <el-option v-for="dict in statusOption" :key="dict.value" :label="dict.label" :value="dict.value" :disabled="dict.disabled" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="备注" prop="remark">
                <el-input v-model="model.remark" placeholder="备注" clearable type="textarea" />
              </el-form-item>
            </el-scrollbar>
          </el-tab-pane>
         
          
        </el-tabs>
      </el-form>
      <template #footer>
        <el-button :loading="loading" @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="editCarhailing">提交</el-button>
      </template>
    </el-dialog>
</div>
</template>
<script setup>
import {ref, onMounted} from "vue"
import checkPermission from '@/utils/permission';
import screenHeight from '@/utils/screen-height'
import Pagination from '@/components/Pagination/index.vue'
import { getPageLimit } from '@/utils/settings'
import * as Carhailing from '@/api/suggests/carhailing'
import { customformat } from '@/utils/dateUtil'
import { defineOptions } from 'vue'
defineOptions({
name: 'Carhailing'
})
const model = ref({});
const rowInfo = ref({});
const editform = ref(null);
const loading = ref(true);
const data = ref([])
const height = ref(680)


const statusOption = [
{
    value:0,
    label:'待处理',
    disabled: true
  },
  {
    value:1,
    label:'已约看'
  },
  {
    value:2,
    label:'已取消'
  }
];

const query = ref({
        page: 1,
        limit: getPageLimit(),
        sort_value: 'desc',
        sort_field:'id'
      });
const count = ref(0);
const idkey = ref('id');

const dialog = ref(false);
const dialogTitle = ref('');
function carhailingList(){
   
    loading.value = true;
    Carhailing.carhailingList(query.value).then((res) => {
       
          data.value = res.data.list
          count.value = res.data.count
          loading.value = false
        })
        .catch(error => {
          console.log(error);
          loading.value = false
        })

}
function infoCarhailing(row) {

  rowInfo.value = row
  loading.value = true;
  dialog.value = true
  dialogTitle.value = row.name + '预约详情：' + row[idkey.value]
  var id = {}
  id[idkey.value] = row[idkey.value]
  Carhailing.info(id)
    .then((res) => {
      reset(res.data)
    })
    .catch(() => {

    })
  loading.value = false

}
function editCarhailing() {
  loading.value = true;
  // 验证
  let dataEdit = {}
  // 备注
  if (rowInfo.value.remark != model.value.remark) {
    dataEdit['remark'] = model.value.remark
  }

  // 是否已经回访
  if (rowInfo.value.status != model.value.status) {
    dataEdit['status'] = model.value.status
  }
  if (Object.keys(dataEdit).length === 0) {
    ElMessage.error("未做任何修改");
    dialog.value = true
  } else {
    dataEdit[idkey.value] = model.value[idkey.value]
    
    Carhailing.editCarhailing(dataEdit).then((res) => {
      carhailingList()
      dialog.value = false
    })
    .catch(() => {
      loading.value = false
    })
  }
  
  loading.value = false
}
// 排序
function sort(sort) {
      query.value.sort_field = sort.prop
      query.value.sort_value = ''
      if (sort.order === 'ascending') {
        query.value.sort_value = 'asc'
        carhailingList()
      }
      if (sort.order === 'descending') {
        query.value.sort_value = 'desc'
        carhailingList()
      }
}
// 操作
function select(selection) {
      this.selection = selection
      this.selectIds = this.selectGetIds(selection).toString()
    }

function cancel() {
  dialog.value = false
  reset()    
}
// 重置
function reset(row) {
  if (row) {
    model.value = row
  } else {
    model.value = {}
  }
}
onMounted(()=>{
    height.value = screenHeight(310)
    carhailingList()
})

</script>