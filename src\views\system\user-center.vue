<template>
  <div class="app-container">
    <div class="bg-[#ffffff] p-[10px] border-rd-10px mb-[10px]">
      <el-tabs>
        <el-tab-pane v-if="checkPermission(['admin/system.UserCenter/info'])" label="我的信息" lazy>
          <UserCenterInfo />
        </el-tab-pane>
        <el-tab-pane v-if="checkPermission(['admin/system.UserCenter/edit'])" label="修改信息" lazy>
          <UserCenterEdit />
        </el-tab-pane>
        <el-tab-pane v-if="checkPermission(['admin/system.UserCenter/pwd'])" label="修改密码" lazy>
          <UserCenterPwd />
        </el-tab-pane>
        <el-tab-pane v-if="checkPermission(['admin/system.UserCenter/log'])" label="我的日志" lazy>
          <UserCenterLog />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import UserCenterInfo from './components/UserCenterInfo.vue'
import UserCenterEdit from './components/UserCenterEdit.vue'
import UserCenterPwd from './components/UserCenterPwd.vue'
import UserCenterLog from './components/UserCenterLog.vue'
import { useUserStore } from '@/store/modules/user'
import { ElMessage } from 'element-plus'
const userStore = useUserStore()
if (userStore.user.forcechangepwd == 1) {
  ElMessage({
    message: '您的密码已到期,请修改密码后重新登录!!',
    type: 'error',
    duration: 0
  })
}
export default {
  name: 'SystemUserCenter',
  components: { UserCenterInfo, UserCenterEdit, UserCenterPwd, UserCenterLog },
  data() {
    return {
      name: '个人中心'
    }
  },
  methods: {
    checkPermission
  }
}
</script>
