import { ref, toRefs } from 'vue'
//import { houseProject } from '@/services/index'
//import useDictStore from '@/store/modules/dict'

/** 数据范围选项 */
// 项目状态
export const projectStatusList = ref([
  { value: 0, label: '待售' },
  { value: 1, label: '在售' },
  { value: 2, label: '售馨' }
])
// 产权类型
export const propertyTypeList = [
  { value: 0, label: '商品住宅', checked: false },
  { value: 1, label: '共有产权房', checked: false },
  { value: 2, label: '安置住房', checked: false },
  { value: 3, label: '公寓', checked: false },
  { value: 4, label: '商业', checked: false },
  { value: 5, label: '配售型保障住房', checked: false }
]
// 户型类型
export const buildTypeList = ref([
  { value: 0, label: '高层' },
  { value: 1, label: '小高层' },
  { value: 2, label: '洋房' },
  { value: 3, label: '叠拼' },
  { value: 4, label: '别墅' }
])
// 发布状态
export const releaseStatusList = ref([
  { value: 0, label: '未完善' },
  { value: 1, label: '待发布' },
  { value: 2, label: '已发布' },
  { value: 3, label: '已下架' }
])

// 环线
export const loopLineList = [
  { value: 0, label: '二环以内', checked: false },
  { value: 1, label: '二至三环', checked: false },
  { value: 2, label: '三至四环', checked: false },
  { value: 3, label: '四至五环', checked: false },
  { value: 4, label: '五至六环', checked: false },
  { value: 5, label: '六环以外', checked: false }
]

// 户型
export const unitTypeList = [
  { value: 0, label: '1居', checked: false },
  { value: 1, label: '2居', checked: false },
  { value: 2, label: '3居', checked: false },
  { value: 3, label: '4居', checked: false },
  { value: 4, label: '5居', checked: false },
  { value: 5, label: '5居+', checked: false }
]

// 学校
export const schoolList = [
  { value: 0, label: '优质公立小学', checked: false },
  { value: 1, label: '优质公立中学', checked: false },
  { value: 2, label: '国际学校', checked: false },
  { value: 3, label: '优质民办学校', checked: false }
]

// 商场
export const shopList = [
  { value: 0, label: '距离商场500米', checked: false },
  { value: 1, label: '距离商场1000米', checked: false },
  { value: 2, label: '综合性商场', checked: false }
]

// 特色
export const featureList = [
  { value: 0, label: '大阳台', checked: false },
  { value: 1, label: '大宽厅', checked: false },
  { value: 2, label: '现房', checked: false },
  { value: 3, label: '颜值高', checked: false },
  { value: 4, label: '买贵包赔', checked: false },
  { value: 5, label: '大平层', checked: false },
  { value: 6, label: '低密居所', checked: false },
  { value: 7, label: '景观居所', checked: false },
  { value: 8, label: '公园洋房', checked: false },
  { value: 9, label: '大湖游园', checked: false },
  { value: 10, label: '高得房率', checked: false },
  { value: 11, label: '超高得房率', checked: false },
  { value: 12, label: '配套齐全', checked: false }
]

// 交通
export const trafficList = [
  { value: 0, label: '地铁500米', checked: false },
  { value: 1, label: '地铁1000米', checked: false },
  { value: 2, label: '双地铁', checked: false },
  { value: 3, label: '三地铁', checked: false }
]

// 价格评测
export const priceFlagList = [
  { value: 0, label: '未导入', checked: false },
  { value: 1, label: '已导入', checked: false }
]
//板块评测搜索  评测展示
export const evaluatingShowList = [
  { value: 0, label: '全部展示', checked: false },
  { value: 1, label: '均不展示', checked: false },
  { value: 2, label: '板块评测', checked: false },
  { value: 3, label: '产业评测', checked: false }
]
//板块评测发布状态
export const evaluatingStateList = [
  { value: -1, label: '全部', checked: false },
  { value: 0, label: '待发布', checked: false },
  { value: 1, label: '已发布', checked: false },
  { value: 2, label: '已下架', checked: false }
]
//评测项
export const evaluatingList = [
  { value: 'price_state', label: '价格评测', checked: false }
]

//用户身份
// 交通
export const memberIdentityList = [
  { value: 1, label: '普通用户', checked: false },
  { value: 2, label: '置业顾问', checked: false },
  { value: 3, label: '选房师', checked: false }
]
/**
 * 获取字典数据
 */
/* export function useDictSelf(...args) {
  const res = ref<any>({})
  return (() => {
    args.forEach(dictType => {
      res.value[dictType] = []
      const dicts = useDictStore().getDict(dictType)
      if (dicts) {
        res.value[dictType] = dicts
      } else {
        houseProject.getOption(dictType).then(resp => {
          if (resp.data && resp.data[0] !== null) {
            res.value[dictType] = resp.data.map((p) => ({
              label: p,
              value: p
            }))
            useDictStore().setDict(dictType, res.value[dictType])
          }
        })
      }
    })
    return toRefs(res.value)
  })()
} */

// 处理数组
export function stringToArray(str, type) {
  // 如果字符串为空，返回空rpx
  if (!str) return []
  // 在字符串前加上逗号，然后分割字符串
  if (type) {
    return str.split(',').map(s => s.trim())
  }

  return str.split(',').map(s => Number(s.trim()))
}
