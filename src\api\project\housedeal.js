import request from '@/utils/request'
// 成交数据
const url = '/admin/house.HouseDeal/'
/**
 * 成交数据列表
 * @param {array} params 请求参数
 */
export function getList(params) {
  return request({
    url: url + 'index',
    method: 'post',
    params: params
  })
}
/**
 * 成交数据详情
 * @param {array} params 请求参数
 */
export function info(params) {
  return request({
    url: url + 'info',
    method: 'get',
    params: params
  })
}
/**
 * 成交数据添加
 * @param {array} data 请求数据
 */
export function addItem(data) {
  return request({
    url: url + 'create',
    method: 'post',
    data
  })
}
/**
 * 成交数据修改
 * @param {array} data 请求数据
 */
export function updateItem(data) {
  return request({
    url: url + 'edit',
    method: 'post',
    data
  })
}
/**
 * 成交数据删除
 * @param {array} data 请求数据
 */
export function deleteItems(data) {
  return request({
    url: url + 'del',
    method: 'post',
    data
  })
}
/**
 * 成交数据删除
 * @param {array} data 请求数据
 */
export function changeState(data) {
  return request({
    url: url + 'changeState',
    method: 'post',
    data
  })
}
/**
* 板块评测内容导入
* @param {array} data 请求数据
*/
export function onlineSigningImport(data) {

  return request({
    url: url + 'onlineSigningImport',
    method: 'post',
    data
  })
}
export function delHouseDealapi(data) {

  return request({
    url: url + 'delHouseDeal',
    method: 'post',
    data
  })
}
// /**
//  * 内容标签是否禁用
//  * @param {array} data 请求数据
//  */
// export function disable(data) {
//   return request({
//     url: url + 'disable',
//     method: 'post',
//     data
//   })
// }
// /**
//  * 内容标签内容
//  * @param {array} params 请求参数
//  */
// export function content(params) {
//   return request({
//     url: url + 'content',
//     method: 'get',
//     params: params
//   })
// }
// /**
//  * 内容标签内容解除
//  * @param {array} data 请求数据
//  */
// export function contentRemove(data) {
//   return request({
//     url: url + 'contentRemove',
//     method: 'post',
//     data
//   })
// }
