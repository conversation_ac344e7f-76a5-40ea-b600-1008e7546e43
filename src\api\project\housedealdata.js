import request from '@/utils/request'
// 成交数据
const url = '/admin/house.HouseDealData/'
/**
 * 成交数据列表
 * @param {array} params 请求参数
 */
export function getList(params) {
  return request({
    url: url + 'regionindex',
    method: 'get',
    params: params
  })
}
/**
 * 获取城区列表
 * @param {array} params 请求参数
 */
export function getRegionList(params) {
  return request({
    url: url + 'regions',
    method: 'get',
    params: params
  })
}
/**
 * 月度成交数据导入
 * @param {array} params 请求参数
 */
export function monthDataExport(params) {
  return request({
    url: url + 'housedealdataexport',
    method: 'post',
    params: params
  })
}
/**
 * 成交数据楼盘列表
 * @param {array} params 请求参数
 */
export function getProjectList(params) {
  return request({
    url: url + 'regionlist',
    method: 'get',
    params: params
  })
}
/**
 * 成交数据删除
 * @param {array} data 请求数据
 */
export function deleteItems(data) {
  return request({
    url: url + 'del',
    method: 'post',
    data
  })
}
/**
 * 成交数据删除
 * @param {array} data 请求数据
 */
export function changeState(data) {
  return request({
    url: url + 'changeState',
    method: 'post',
    data
  })
}

/**
 * 修改成交数据
 * @param {array} data 请求数据
 */
export function editDealData(data) {
  return request({
    url: url + 'editProjectDealData',
    method: 'post',
    data
  })
}



