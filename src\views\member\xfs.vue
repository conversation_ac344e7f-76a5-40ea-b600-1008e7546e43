<template>
  <div class="page-contaniner">
    <TheZygwFilter @on-search="handleSearch"/>

    <div class="table-box">
      <!-- 待审核置业顾问数量 -->
      <!-- <div @click="actionsearch(tableData.auditnum)">待审核数量{{ tableData.auditnum }}</div> -->
      <el-table :data="tableData.list" v-loading="tableData.loading"  @sort-change="sort">
        <el-table-column prop="member_id" label="用户ID" />
        
        <el-table-column prop="wxunionid" label="unionid" :formatter="defaultFormat()" />
        
        <el-table-column prop="service_name" label="用户姓名" :formatter="defaultFormat()" />
        <el-table-column prop="phone" label="手机号码" :formatter="defaultFormat()" />
        <el-table-column prop="identity_type" label="用户角色"  >
          <template #default="scope">
          <el-text v-if="scope.row.identity_type == 1 && scope.row.phone != ''" >普通用户</el-text>
          <el-text v-else-if="scope.row.identity_type == 2" >置业顾问<span v-if="scope.row.review_state == 1">(待审核)</span><span v-else-if="scope.row.review_state == 3">(审核不通过)</span></el-text>
          <el-text v-else-if="scope.row.identity_type == 3" >选房师</el-text>
          <el-text v-else>-</el-text>
        </template>
        </el-table-column>
        <el-table-column prop="activation" label="活跃度" :formatter="defaultFormat()" sortable="custom"/>
        <el-table-column prop="available_score" label="可用积分" :formatter="defaultFormat()" sortable="custom" />
        <el-table-column
          prop="create_time"
          label="创建时间"
          :formatter="customformat('YYYY-MM-DD HH:mm')"
        />
        <el-table-column
          prop="real_user_time"
          label="转正时间"
         :formatter="customformat('YYYY-MM-DD HH:mm')"
        >
          <template #default="scope">
            <el-text v-if="scope.row.phone == '' " >-</el-text>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="140">
          <template #default="{ row }">
            <div class="flex">
              <!-- @click="handleView(row.member_id)" -->
              <el-link type="primary" @click="scoreDetail(row.member_id,row.identity_type)" >积分</el-link>  &nbsp;&nbsp;
              <el-link type="primary" v-if=" (row.phone != '' && row.identity_type == 1) || (row.phone != '' && row.identity_type == 3) || (row.phone != '' && row.identity_type == 2 && row.review_state == 2 ) " @click="handleViewSup(row.member_id,row.identity_type)" >编辑</el-link>
              <el-link type="primary" v-if="row.phone == '' || row.review_state == 3 || row.review_state == 4" @click="handleViewSup(row.member_id,row.identity_type)" >查看</el-link>
              <el-link type="primary" v-if="row.identity_type == 2 && row.review_state == 1" @click="handleViewSup(row.member_id,row.identity_type)" >审核</el-link>
             
              <div class="ml-[10px]" @click="handleSetDisable(row)">
                <el-link type="primary" v-if="!row.is_disable">禁用</el-link>
                <el-link type="primary" v-else>解禁</el-link>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="tableData.total > 0"
        v-model:total="tableData.total"
        v-model:page="query.page"
        v-model:limit="query.limit"
        @pagination="featchList"
      />
    </div>
    <TheMemberDetailDialog v-model:visible="visible" :id="currentId" @close="handleClose" />
  </div>
</template>

<script setup>
import TheZygwFilter from './components/TheZygwFilter.vue'
import TheMemberDetailDialog from './components/TheMemberDetailDialog.vue'
import { reactive, ref, onMounted,defineOptions } from 'vue'
import { getPageLimit } from '@/utils/settings'
import { list, listzygw, disable } from '@/api/member/member'
import { customformat } from '@/utils/dateUtil'
import { defaultFormat } from '@/utils'
import { useRouter } from 'vue-router'
import { memberIdentityList } from '@/mapping/selectOption'

defineOptions({
name: 'Member'
})
const router = useRouter()
const query = ref({
  page: 1,
  role: 3,
  limit: getPageLimit()
})

const visible = ref(false)

const tableData = reactive({
  total: 0,
  auditnum: 0,
  list: [],
  loading: false
})
const scoreDetail = (id,identity_type) => {
  router.push({
    path: '/member/zygwjf',
    query: { id,identity_type }
  })
}
const currentId = ref(null)

const featchList = async () => {
  tableData.loading = true
  const { data } = await listzygw(query.value)
  tableData.total = data.count
  tableData.list = data.list
  tableData.auditnum = data.auditnum
  try {
  } finally {
    tableData.loading = false
  }
}

const handleSearch = (params) => {
	console.log(params)
  query.value = { ...query.value, ...params }
  featchList()
}
const actionsearch = (num)=>{
  if(num > 0){
    query.value = { ...query.value,'role':4 }
    featchList()
  }
}
const handleClose = () => {
  currentId.value = null
}

const handleView = (id) => {
  currentId.value = id
  visible.value = true
}

const handleSetDisable = ({ is_disable, member_id }) => {
  console.log(is_disable)
  const message = is_disable === 0 ? '是否确认禁用' : '是否确认解禁'
  ElMessageBox({
    title: '提示',
    message,
    showCancelButton: true,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        try {
          const res = await disable({
            ids: [member_id],
            is_disable: is_disable ? 0 : 1
          })
          ElMessage.success('操作成功')
          featchList()
        } finally {
          instance.confirmButtonLoading = false
          done()
        }
      } else {
        done()
      }
    }
  })
}
const handleViewSup = (id,identity_type) => {
  router.push({
    path: '/member/member/memberinfo',
    query: { id,identity_type }
  })
}
// 排序
const sort = (sort)=> {
  query.value.sort_field = sort.prop
  query.value.sort_value = ''
  if (sort.order === 'ascending') {
    query.value.sort_value = 'asc'
    featchList()
  }
  if (sort.order === 'descending') {
    query.value.sort_value = 'desc'
    featchList()
  }
}
//onMounted()
onMounted(() => {
  
  featchList()
})
</script>
<style lang="scss" scoped>
.page-contaniner {
  min-width: 1200px;
  .table-box {
    padding: 10px;
    margin: 0 20px;
  }
}
</style>
