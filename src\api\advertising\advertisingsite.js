import request from '@/utils/request'
// 广告管理
const url = '/admin/advertise.AdvertisingSite/'
/**
 * 广告位列表
 * @param {array} params 请求参数
 */
export function list(params) {
  return request({
    url: url + 'index',
    method: 'get',
    params: params
  })
}

/**
 * 广告位具体信息
 * @param {array} params 请求参数
 */
export function show(data) {
    return request({
      url: url + 'show',
      method: 'post',
      data
    })
  }

/**
 * 广告位添加
 * @param {array} data 请求数据
 */
export function add(data) {
    return request({
      url: url + 'add',
      method: 'post',
      data
    })
}


/**
 * 广告位编辑
 * @param {array} data 请求数据
 */
export function edit(data) {
    return request({
      url: url + 'edit',
      method: 'post',
      data
    })
}