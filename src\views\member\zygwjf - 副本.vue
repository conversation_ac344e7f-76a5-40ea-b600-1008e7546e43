<template>
  <div class="app-container">
	  <!-- 用户名称，上次登录时间 -->
	  <el-row>
	    <el-col class="mb-2">
			<el-form-item label="用户名">
			  <span>更新时间</span>
			</el-form-item>
	      
	    </el-col>
	  </el-row>
	  <el-row v-loading="loading" :gutter="10">
	  		<el-col :xs="24" :span="6">
	  		  <el-card :body-style="{ padding: '10px 0px' }">
	  		    <template #header>
	  		      <div class="text-center">
	  		        <span>用户可用分</span>
	  		      </div>
	  		    </template>
	  		    <div class="text-center" title="用户可用分">
	  		      <el-statistic :value="memberScore.available_score" />
	  		    </div>
	  		  </el-card>
	  		</el-col>  
	  		
	  		<el-col :xs="24" :span="6">
	  		  <el-card :body-style="{ padding: '10px 0px' }">
	  		    <template #header>
	  		      <div class="text-center">
	  		        <span>用户冻结积分</span>
	  		      </div>
	  		    </template>
	  		    <div class="text-center" title="用户冻结积分">
	  		      <el-statistic :value="memberScore.freeze_score" />
	  		    </div>
	  		  </el-card>
	  		</el-col>  
	  		
	  		<el-col :xs="24" :span="6">
	  		  <el-card :body-style="{ padding: '10px 0px' }">
	  		    <template #header>
	  		      <div class="text-center">
	  		        <span>累计发放积分</span>
	  		      </div>
	  		    </template>
	  		    <div class="text-center" title="累计发放积分">
	  		      <el-statistic :value="memberScore.total_get_score" />
	  		    </div>
	  		  </el-card>
	  		</el-col> 
	  		 
	  		<el-col :xs="24" :span="6">
	  		   <el-card :body-style="{ padding: '10px 0px' }">
	  		     <template #header>
	  		       <div class="text-center">
	  		         <span>累计消耗积分</span>
	  		       </div>
	  		     </template>
	  		     <div class="text-center" title="累计消耗积分">
	  		       <el-statistic :value="memberScore.total_consume_score" />
	  		     </div>
	  		   </el-card>
	  		 </el-col>
	    
	  </el-row>
	  <br/>
    <!-- 查询 -->
    <el-row>
      <el-col class="mb-2">
        
        
      </el-col>
    </el-row>
    <!-- 操作 -->
    <el-row>
		<el-row>
		<el-col class="mb-2">
			<el-form-item label="积分管理">
			  <el-col>
				<el-button title="积分下发" @click="selectOpen('give')">积分下发</el-button>
				<el-button title="积分消耗" @click="selectOpen('consume')">积分消耗</el-button>
				<el-button title="积分冻结" @click="selectOpen('frozen')">积分冻结</el-button>
			  </el-col>
			</el-form-item>
		</el-col>
		</el-row>
      
    </el-row>

    <!-- 列表 -->
    <el-table
      ref="table"
      v-loading="loading"
      :data="data"
      :height="height"
      @sort-change="sort"
      @selection-change="select"
    >
      <el-table-column type="selection" width="42" title="全选/反选" />
      <el-table-column :prop="idkey" label="ID" width="80" sortable="custom" />
	  <el-table-column prop="create_time" label="操作时间" width="165" sortable="custom" />
	  <el-table-column prop="create_time" label="分值添加时间" width="165" sortable="custom" />
      <el-table-column prop="op_type" label="积分类型" min-width="175" show-overflow-tooltip >
	  <template #default="scope">
	  	<span v-text="publicparams['idetifyScoreType'][scope.row.op_type.toString()]"></span>
	    </template>
	  </el-table-column>
      <el-table-column prop="detail_name" label="类型明细" min-width="105" show-overflow-tooltip />
	  <el-table-column prop="action_type" label="操作类型" min-width="85" sortable="custom">
	    <template #default="scope">
			<span v-text="scope.row.action_type==1 || scope.row.action_type=='1'?'手动':'自动'"></span>
	    </template>
	  </el-table-column>
    
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="count > 0"
      v-model:total="count"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="list"
    />
    <!-- 添加修改 -->
    <el-dialog
      v-model="dialog"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="cancel"
      top="5vh"
    >
      <el-form ref="ref" :rules="rules" :model="model" label-width="100px">
        <el-tabs>
          <el-tab-pane :label="selectTitle">
            <el-scrollbar native :height="height - 80">
			<el-form-item label="选择时间" prop="create_time">
			  <el-date-picker
				v-model="model.create_time"
				type="datetime"
				value-format="YYYY-MM-DD HH:mm:ss"
				:default-time="new Date(2024, 1, 1, 0, 0, 0)"
				placeholder="选择时间"
			  />
			</el-form-item>
			
			<div v-if="selectType=='give'">
				<el-form-item  label="任务归属" prop="getscore_reason" v-for="(val,key) in taskList">
					<el-col :span="5" >
						<el-select v-model="val.getscore_reason" class="w-full" filterable>
						  <el-option
							v-for="(item, index) in publicparams['idetifyScoreGiveReason']"
							:key="index"
							:label="item"
							:value="index"
						  />
						</el-select>
						</el-col>
						<el-col offset="2" :span="3">
						  <el-input v-model="val.number" type="number" placeholder="输入积分"/>
						</el-col>
						<el-col :span="5" :offset="1">
						  <el-button @click="addPoints(key)" type="primary">+</el-button>
						  <el-button @click="cancelPoints(key)">-</el-button>
						</el-col>
				</el-form-item>
				
			</div>
			<div v-else-if="selectType=='consume'">
				<el-form-item  label="兑换商品" prop="getscore_reason">
					<el-col :span="6" >
						<el-input v-model="model.goods_name" placeholder="请输入商品名称" clearable />
					</el-col>
					<el-col offset="2" :span="4">
					  <el-input v-model="model.number" type="number" placeholder="输入积分"/>
					</el-col>
					<el-col :span="5" :offset="1">
					  <el-button @click="addPoints" type="primary">+</el-button>
					  <el-button @click="cancelPoints">-</el-button>
					</el-col>
				</el-form-item>
			</div>
			<div v-else>
				<el-form-item  label="冻结理由" prop="freeze_reason">
					<el-col :span="6" >
						<el-select v-model="model.freeze_reason" class="w-full" filterable>
						  <el-option
							v-for="(item, index) in publicparams['idetifyScoreFrozenReason']"
							:key="index"
							:label="item"
							:value="index"
						  />
						</el-select>
					</el-col>
					<el-col offset="2" :span="4">
					  <el-input v-model="model.number" type="number" placeholder="输入积分"/>
					</el-col>
				</el-form-item>
				
			</div>
            <el-form-item label="材料证明" prop="prove_material">
                <FileImage
                  v-model="model.prove_material"
                  :file-url="model.image_url"
                  :height="100"
                  upload
                />
            </el-form-item>
            </el-scrollbar>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <el-button :loading="loading" @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submit">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import screenHeight from '@/utils/screen-height'
import Pagination from '@/components/Pagination/index.vue'
import { arrayColumn } from '@/utils/index'
import { getPageLimit } from '@/utils/settings'
import {
  getScore,getScoreDetailList,addScoreDetail
} from '@/api/member/memberscore'
import {
  info,
  add,
  edit,
  dele,
  istop,
  ishot,
  isrec,
  disable,
  release
} from '@/api/content/content'
export default {
  name: 'Zygwjf',
  components: { Pagination },

  data() {
    return {
      name: '置业顾问积分',
      height: 680,
      loading: false,
      idkey: 'id',
	  member_id:0,
	  identify_id:0,
      exps: [{ exp: 'like', name: '包含' }],
      query: {
        page: 1,
        limit: getPageLimit(),
		member_id:0
      },
	  // 用户积分详情
	  memberScore:{
		"available_score":0,
		"freeze_score":0,
		"total_consume_score":0,
		"total_get_score":0,
		"update_time":0,
	  },
	  // 用户积分详情
	  memberDetailModel:{
		"available_score":0,
		"freeze_score":0,
		"total_consume_score":0,
		"total_get_score":0,
		"update_time":0,
	  },
	  // 公共参数
	  publicparams:{},
      data: [],
      count: 0,
      dialog: false,
      dialogTitle: '',
	  taskList:[], // 任务操作列表
      model: {
        member_id: '',
        identify_id: '',
        goods_name: '',
        freeze_reason: 0,
        getscore_reason: 0,
        number: 0,
        prove_material: '',
        task_id: 0,
        task_sub_id: 0,
        detail_name: '',
        action_type: 1,  
        op_type: 0,  
		create_time:0
      },
      rules: {
        // name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        // release_time: [{ required: true, message: '请选择发布时间', trigger: 'blur' }]
      },
      
      selection: [],
      selectIds: '',
      selectTitle: '操作',
      selectDialog: false,
      selectType: ''
    }
  },
  created() {
    this.height = screenHeight()
  },
  mounted() {
    this.member_id = this.$route.query.id; // John
	this.query.member_id = this.member_id
	this.model.member_id = this.member_id
	
	this.getScore()
	this.list()
  },
  methods: {
	getScore(){
		var queryData = {"member_id":this.member_id}
		getScore(queryData).then((res)=>{
			this.memberScore = res.data
		})
	},
    // 列表
    list() {
      this.loading = true
	  this.query.member_id = this.member_id
      getScoreDetailList(this.query)
        .then((res) => {
          this.data = res.data.list
          this.count = res.data.count
		  this.publicparams = res.data.publicparams
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 添加修改
    add() {
      this.dialog = true
      this.dialogTitle = this.name + '添加'
      this.reset()
    },
    
    cancel() {
      this.dialog = false
      this.reset()
    },
    submit() {
		this.model.member_id = this.member_id
		this.model.identify_id = this.memberScore.identify_id
		if(this.selectType=="frozen"){
			this.model.op_type = 3
		}else if(this.selectType=="give"){
			this.model.op_type = 1
		}else{
			this.model.op_type = 2
		}
      this.$refs['ref'].validate((valid) => {
        if (valid) {
          if (this.model[this.idkey]) {
            edit(this.model)
              .then((res) => {
                this.list()
                this.dialog = false
                ElMessage.success(res.msg)
              })
              .catch(() => {})
          } else {
            addScoreDetail(this.model)
              .then((res) => {
                this.list()
				this.getScore()
                this.dialog = false
                ElMessage.success(res.msg)
              })
              .catch(() => {})
          }
        } else {
          ElMessage.error('请完善必填项（带红色星号*）')
        }
      })
    },
    // 重置
    reset(row) {
      if (row) {
        this.model = row
		console.log(this.model.tag_ids)
      } else {
        this.model = this.$options.data().model
		
      }
      if (this.$refs['ref'] !== undefined) {
        try {
          this.$refs['ref'].resetFields()
          this.$refs['ref'].clearValidate()
        } catch (error) {}
      }
    },
    
    // 刷新
    refresh() {
      const limit = this.query.limit
      this.query = this.$options.data().query
      this.$refs['table'].clearSort()
      this.query.limit = limit
      this.list()
    },
    // 排序
    sort(sort) {
      this.query.sort_field = sort.prop
      this.query.sort_value = ''
      if (sort.order === 'ascending') {
        this.query.sort_value = 'asc'
        this.list()
      }
      if (sort.order === 'descending') {
        this.query.sort_value = 'desc'
        this.list()
      }
    },
    // 操作
    select(selection) {
      this.selection = selection
      this.selectIds = this.selectGetIds(selection).toString()
    },
    selectGetIds(selection) {
      return arrayColumn(selection, this.idkey)
    },
    selectAlert() {
      ElMessageBox.alert('请选择需要操作的' + this.name, '提示', {
        type: 'warning',
        callback: () => {}
      })
    },
    selectOpen(selectType) {
		this.selectTitle = '操作'
		if (selectType === 'give') {
		  this.selectTitle = '积分下发'
		} else if (selectType === 'consume') {
		  this.selectTitle = '积分消耗'
		} else if (selectType === 'frozen') {
		  this.selectTitle = '积分冻结'
		}
		this.selectType = selectType
		this.add()
    }
  }
}
</script>
