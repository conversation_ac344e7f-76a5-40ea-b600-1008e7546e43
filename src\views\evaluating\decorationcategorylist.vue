<template>
  <div class="app-container">
    <div class="mb-4">
      <el-button type="primary" @click="handleCreate">新建</el-button>
    </div>
    <!-- 操作按钮 -->
    

    <!-- 表单 -->
    <el-dialog 
  :title="isCreateMode ? '新建分类' : '编辑分类'"
  v-model="editDialogVisible"
  width="50%"
>
      <el-form :model="form" label-width="120px">
        <el-form-item label="位置">
          <!-- 位置选择器 -->
          <div class="form-item-container">
            <el-select 
              v-model="form.position_id" 
              placeholder="请选择位置"
              class="form-select"
              @change="(val) => {
                // 只有在新建模式下才清空 category_id
                if (isCreateMode && !isEditingData) {
                  form.category_id = ''
                }
                if (val) getCategories(val)
              }"
            >
              <el-option
                v-for="item in positions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <el-button type="primary" @click="showPositionDialog">添加</el-button>
          </div>
        </el-form-item>

        <el-form-item label="类型">
          <!-- 类型选择器 -->
          <div class="form-item-container">
            <el-select
              v-model="form.category_id"
              placeholder="请选择类型"
              class="form-select"
              :disabled="!form.position_id"
              :loading="categoriesLoading"
            >
              <el-option
                v-for="item in categories"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <el-button type="primary" @click="showCategoryDialog">添加</el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
<!-- 点击取消按钮时，关闭编辑对话框 -->
<el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </template>
    </el-dialog>

    <!-- 类型列表 -->
    <el-table v-loading="loading" :data="tableData" style="width: 100%; margin-top: 20px">
      <el-table-column prop="id" label="类型ID" width="100" />
      <el-table-column prop="position_name" label="位置" />
      <el-table-column prop="name" label="类型" />
      <el-table-column prop="update_time" label="更新时间" />
      <el-table-column prop="status" label="发布状态">
        <template #default="scope">
          <el-tag :type="scope.row.release_status == 2 ? 'success' : 'danger'">
            {{ scope.row.release_status == 2 ? '已发布' : '未发布' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180">
        <template #default="scope">
          <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button 
            size="small" 
            :type="scope.row.release_status == 2 ? 'danger' : 'success'"
            @click="handleRelease(scope.row)"
          >
            {{ scope.row.release_status == 2 ? '下架' : '发布' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>


    <!-- 位置添加弹框 -->
    <el-dialog v-model="positionDialogVisible" title="添加位置">
      <el-form :model="positionForm" label-width="120px">
        <el-form-item label="位置名称">
          <el-input v-model="positionForm.name" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="positionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addPosition">确定</el-button>
      </template>
    </el-dialog>

    <!-- 类型添加弹框 -->
    <el-dialog v-model="categoryDialogVisible" title="添加类型">
      <el-form :model="categoryForm" label-width="120px">
        <el-form-item label="位置">
          <el-input v-model="selectedPositionName" disabled />
        </el-form-item>
        <el-form-item label="类型名称">
          <el-input v-model="categoryForm.name" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="categoryDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addCategory">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getList, add, edit, del, updateReleaseStatus } from '@/api/project/decorationcategory'

const form = ref({
  position_id: '',
  category_id: ''
})

const getCategories = async (positionId) => {
  categoriesLoading.value = true
  try {
    const res = await getCategoryList({ position_id: positionId })
    categories.value = res.data || []
    return res.data
  } catch (error) {
    console.error('获取类型列表失败:', error)
    categories.value = []
    throw error
  } finally {
    categoriesLoading.value = false
  }
}
const positions = ref([])
const categories = ref([])
const categoriesLoading = ref(false)
const positionDialogVisible = ref(false)
const categoryDialogVisible = ref(false)
const formDialogVisible = ref(false)
const editDialogVisible = ref(false)
const isCreateMode = ref(false)
const isEditingData = ref(false) // 标记是否正在编辑数据
const positionForm = ref({
  name: ''
})
const categoryForm = ref({
  name: ''
})
const selectedPositionName = ref('')
const editingRowData = ref(null) // 保存正在编辑的行数据

const tableData = ref([])
const loading = ref(true)
const pagination = ref({
  page: 1,
  limit: 10,
  total: 0
})

const showPositionDialog = () => {
  positionDialogVisible.value = true
  positionForm.value.name = ''
}

const showCategoryDialog = () => {
  if (!form.value.position_id) {
    ElMessage.warning('请先选择位置')
    return
  }
  const selectedPosition = positions.value.find(item => item.id === form.value.position_id)
  selectedPositionName.value = selectedPosition ? selectedPosition.name : ''
  categoryDialogVisible.value = true
  categoryForm.value.name = ''
}

const addPosition = () => {
  positions.value.push({
    id: Date.now(),
    name: positionForm.value.name
  })
  positionDialogVisible.value = false
  ElMessage.success('位置添加成功')
}

const addCategory = () => {
  categories.value.push({
    id: Date.now(),
    name: categoryForm.value.name
  })
  categoryDialogVisible.value = false
  ElMessage.success('类型添加成功')
}

import { getPositionList, getCategoryList } from '@/api/project/decorationcategory'

const getPositions = async () => {
  try {
    const res = await getPositionList()
    positions.value = res.data || []
    return res.data
  } catch (error) {
    console.error('获取位置列表失败:', error)
    positions.value = []
    throw error
  }
}

const handleCreate = () => {
  form.value = { position_id: '', category_id: '' }
  categories.value = []
  isCreateMode.value = true
  isEditingData.value = false
  editingRowData.value = null
  editDialogVisible.value = true
  getPositions()
}

const handleCancel = () => {
  editDialogVisible.value = false
  editingRowData.value = null
  isEditingData.value = false
}

async function handleEdit(row) {
  console.log('开始编辑，原始数据:', row);
  loading.value = true
  isEditingData.value = true; // 设置编辑标志
  editingRowData.value = row; // 保存编辑的行数据

  try {
    // 先设置基本表单数据，但暂时不设置 category_id
    form.value = {
      position_id: row.position_id,
      category_id: '' // 先清空，等类型数据加载完成后再设置
    };
    isCreateMode.value = false;
    editDialogVisible.value = true;

    console.log('设置表单数据，position_id:', row.position_id);

    // 确保位置数据已加载
    if (!positions.value.length) {
      console.log('加载位置数据...');
      await getPositions();
    }
    console.log('位置数据:', positions.value);

    // 如果有位置ID，加载对应的类型数据
    if (row.position_id) {
      console.log('加载类型数据，position_id:', row.position_id);
      await getCategories(row.position_id);
      console.log('类型数据加载完成:', categories.value);

      // 等待下一个 tick 确保 DOM 更新完成，然后设置类型值
      await nextTick();
      console.log('设置 category_id:', row.id);
      form.value.category_id = row.id;

      // 再次等待确保设置生效
      await nextTick();
      console.log('最终表单数据:', form.value);
    }
  } catch (error) {
    console.error('编辑数据加载失败:', error);
    ElMessage.error('数据加载失败');
  } finally {
    loading.value = false;
    isEditingData.value = false; // 清除编辑标志
  }
}




const handleDelete = (row) => {
  ElMessageBox.confirm('确定删除该分类吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    loading.value = true
    del({ id: row.id }).then(() => {
      ElMessage.success('删除成功')
      getListData()
      loading.value = false
    }).catch(() => {
      loading.value = false
    })
  }).catch(() => {})
}

const handleRelease = (row) => {
  ElMessageBox.confirm(`确定${row.release_status == 2 ? '下架' : '发布'}该分类吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    loading.value = true
    // 这里需要添加发布/下架的API调用
    // 假设接口名为 updateReleaseStatus
    updateReleaseStatus({ 
      id: row.id,
      release_status: row.release_status == 2 ? 1 : 2 
    }).then(() => {
      ElMessage.success(`${row.release_status == 2 ? '下架' : '发布'}成功`)
      getListData()
      loading.value = false
    }).catch(() => {
      loading.value = false
    })
  }).catch(() => {})
}

const getListData = async () => {
  try {
    loading.value = true
    const res = await getList({
      page: pagination.value.page,
      limit: pagination.value.limit
    })
    tableData.value = res.data.list
    pagination.value.total = res.data.total
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 监听类型数据变化，在编辑模式下自动设置正确的值
watch(categories, (newCategories) => {
  if (editingRowData.value && newCategories.length > 0 && !isCreateMode.value) {
    console.log('类型数据变化，设置 category_id:', editingRowData.value.id);
    nextTick(() => {
      form.value.category_id = editingRowData.value.id;
      console.log('通过 watch 设置后的表单数据:', form.value);
    });
  }
}, { immediate: false })

// 组件挂载时获取列表数据
onMounted(() => {
  getListData()
})

const handleSubmit = () => {
  if (!form.value.position_id || !form.value.category_id) {
    ElMessage.warning('请选择位置和分类')
    return
  }
  loading.value = true
  
  const apiCall = isCreateMode.value ? add : edit
  
  const requiredData = { position_id: form.value.position_id, category_id: form.value.category_id };
  apiCall(requiredData).then(() => {
    ElMessage.success(isCreateMode.value ? '添加成功' : '编辑成功')
    getListData()
    loading.value = false
    editDialogVisible.value = false
    editingRowData.value = null
    isEditingData.value = false
  }).catch(() => {
    loading.value = false
  })
}
</script>

<style scoped>
/* 表单容器样式 */
.app-container {
  padding: 20px;
}

/* 表单项容器 */
.form-item-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* 表单选择器 */
.form-select {
  flex: 1;
  min-width: 300px;
  margin-right: 10px;
}
</style>