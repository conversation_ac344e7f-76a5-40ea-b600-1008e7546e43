<template>
  <div class="app-container">
    <div class="mb-4">
      <el-button type="primary" @click="handleCreate">新建</el-button>
    </div>
    <!-- 操作按钮 -->
    

    <!-- 表单 -->
    <el-dialog 
  :title="isCreateMode ? '新建分类' : '编辑分类'"
  v-model="editDialogVisible"
  width="50%"
>
      <el-form :model="form" label-width="120px">
        <el-form-item label="位置">
          <!-- 位置选择器 -->
          <div class="form-item-container">
            <el-select 
              v-model="form.position_id" 
              placeholder="请选择位置"
              class="form-select"
              @change="(val) => {
                form.category_id = ''
                if (val) getCategories(val)
              }"
            >
              <el-option
                v-for="item in positions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <el-button type="primary" @click="showPositionDialog">添加</el-button>
          </div>
        </el-form-item>

        <el-form-item label="类型">
          <!-- 类型选择器 -->
          <div class="form-item-container">
            <el-select 
              v-model="form.category_id" 
              placeholder="请选择类型"
              class="form-select"
              :disabled="!form.position_id"
            >
              <el-option
                v-for="item in categories"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <el-button type="primary" @click="showCategoryDialog">添加</el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
<!-- 点击取消按钮时，关闭编辑对话框 -->
<el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </template>
    </el-dialog>

    <!-- 类型列表 -->
    <el-table v-loading="loading" :data="tableData" style="width: 100%; margin-top: 20px">
      <el-table-column prop="id" label="类型ID" width="100" />
      <el-table-column prop="position_name" label="位置" />
      <el-table-column prop="name" label="类型" />
      <el-table-column prop="update_time" label="更新时间" />
      <el-table-column prop="status" label="发布状态">
        <template #default="scope">
          <el-tag :type="scope.row.release_status == 2 ? 'success' : 'danger'">
            {{ scope.row.release_status == 2 ? '已发布' : '未发布' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180">
        <template #default="scope">
          <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button 
            size="small" 
            :type="scope.row.release_status == 2 ? 'danger' : 'success'"
            @click="handleRelease(scope.row)"
          >
            {{ scope.row.release_status == 2 ? '下架' : '发布' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>


    <!-- 位置添加弹框 -->
    <el-dialog v-model="positionDialogVisible" title="添加位置">
      <el-form :model="positionForm" label-width="120px">
        <el-form-item label="位置名称">
          <el-input v-model="positionForm.name" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="positionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addPosition">确定</el-button>
      </template>
    </el-dialog>

    <!-- 类型添加弹框 -->
    <el-dialog v-model="categoryDialogVisible" title="添加类型">
      <el-form :model="categoryForm" label-width="120px">
        <el-form-item label="位置">
          <el-input v-model="selectedPositionName" disabled />
        </el-form-item>
        <el-form-item label="类型名称">
          <el-input v-model="categoryForm.name" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="categoryDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addCategory">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getList, add, edit, del, updateReleaseStatus } from '@/api/project/decorationcategory'

const form = ref({
  position_id: '',
  category_id: ''
})

const getCategories = async (positionId) => {
  try {
    const res = await getCategoryList({ position_id: positionId })
    categories.value = res.data
  } catch (error) {
    console.error(error)
  }
}
const positions = ref([])
const categories = ref([])
const positionDialogVisible = ref(false)
const categoryDialogVisible = ref(false)
const formDialogVisible = ref(false)
const editDialogVisible = ref(false)
const isCreateMode = ref(false)
const positionForm = ref({
  name: ''
})
const categoryForm = ref({
  name: ''
})
const selectedPositionName = ref('')

const tableData = ref([])
const loading = ref(true)
const pagination = ref({
  page: 1,
  limit: 10,
  total: 0
})

const showPositionDialog = () => {
  positionDialogVisible.value = true
  positionForm.value.name = ''
}

const showCategoryDialog = () => {
  if (!form.value.position_id) {
    ElMessage.warning('请先选择位置')
    return
  }
  const selectedPosition = positions.value.find(item => item.id === form.value.position_id)
  selectedPositionName.value = selectedPosition ? selectedPosition.name : ''
  categoryDialogVisible.value = true
  categoryForm.value.name = ''
}

const addPosition = () => {
  positions.value.push({
    id: Date.now(),
    name: positionForm.value.name
  })
  positionDialogVisible.value = false
  ElMessage.success('位置添加成功')
}

const addCategory = () => {
  categories.value.push({
    id: Date.now(),
    name: categoryForm.value.name
  })
  categoryDialogVisible.value = false
  ElMessage.success('类型添加成功')
}

import { getPositionList, getCategoryList } from '@/api/project/decorationcategory'

const getPositions = async () => {
  try {
    const res = await getPositionList()
    positions.value = res.data
  } catch (error) {
    console.error(error)
  }
}

const handleCreate = () => {
  form.value = { position: '', category: '' }
  categories.value = []
  isCreateMode.value = true
  editDialogVisible.value = true
  getPositions()
}

async function handleEdit(row) {
  loading.value = true
  try {
    form.value = { ...row };
    isCreateMode.value = false;
    editDialogVisible.value = true;
    // 仅请求一次位置接口
    if (!positions.value.length) {
      getPositions().then(() => {
        if (row.position_id) {
          getCategories(row.position_id).then(() => {
            // 确保类型选择器正确回显
            form.value.category_id = row.category_id;
            // 强制更新选择器
            nextTick(() => {
              // 这里可根据 Element Plus 组件文档添加强制更新代码
            });
          });
        }
      });
    } else {
      if (row.position_id) {
        getCategories(row.position_id).then(() => {
          // 确保类型选择器正确回显
          form.value.category_id = row.category_id;
        });
      }
    }
  } finally {
    loading.value = false
  }
}




const handleDelete = (row) => {
  ElMessageBox.confirm('确定删除该分类吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    loading.value = true
    del({ id: row.id }).then(() => {
      ElMessage.success('删除成功')
      getListData()
      loading.value = false
    }).catch(() => {
      loading.value = false
    })
  }).catch(() => {})
}

const handleRelease = (row) => {
  ElMessageBox.confirm(`确定${row.release_status == 2 ? '下架' : '发布'}该分类吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    loading.value = true
    // 这里需要添加发布/下架的API调用
    // 假设接口名为 updateReleaseStatus
    updateReleaseStatus({ 
      id: row.id,
      release_status: row.release_status == 2 ? 1 : 2 
    }).then(() => {
      ElMessage.success(`${row.release_status == 2 ? '下架' : '发布'}成功`)
      getListData()
      loading.value = false
    }).catch(() => {
      loading.value = false
    })
  }).catch(() => {})
}

const getListData = async () => {
  try {
    loading.value = true
    const res = await getList({
      page: pagination.value.page,
      limit: pagination.value.limit
    })
    tableData.value = res.data.list
    pagination.value.total = res.data.total
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取列表数据
onMounted(() => {
  getListData()
})

const handleSubmit = () => {
  if (!form.value.position_id || !form.value.category_id) {
    ElMessage.warning('请选择位置和分类')
    return
  }
  loading.value = true
  
  const apiCall = isCreateMode.value ? add : edit
  
  const requiredData = { position_id: form.value.position_id, category_id: form.value.category_id };
  apiCall(requiredData).then(() => {
    ElMessage.success(isCreateMode.value ? '添加成功' : '编辑成功')
    getListData()
    loading.value = false
    editDialogVisible.value = false
  }).catch(() => {
    loading.value = false
  })
}
</script>

<style scoped>
/* 表单容器样式 */
.app-container {
  padding: 20px;
}

/* 表单项容器 */
.form-item-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* 表单选择器 */
.form-select {
  flex: 1;
  min-width: 300px;
  margin-right: 10px;
}
</style>