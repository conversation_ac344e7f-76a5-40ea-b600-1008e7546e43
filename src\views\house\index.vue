<template>
  <div class="app-container">
    <el-form ref="queryRef" :model="query" :inline="true" label-width="90px">
      <el-form-item label="楼盘ID：" prop="id">
        <el-input
          v-model="query.id"
          maxlength="20"
          placeholder="请输入楼盘ID"
          style="width: 140px"
        />
      </el-form-item>

      <el-form-item label="楼盘名称：" prop="name">
        <el-input
          v-model="query.name"
          maxlength="20"
          placeholder="请输入楼盘名称"
          style="width: 140px"
        />
      </el-form-item>

      <el-form-item label="销售状态：" prop="status">
        <el-select v-model="query.status" style="width: 120px">
          <el-option value="-1" label="不限" />
          <el-option
            v-for="(item, index) in search_params['houseSalestate']"
            :key="index"
            :label="item"
            :value="index"
          />
        </el-select>
      </el-form-item>

      <!-- 时间 -->
      <el-form-item>
        <el-select v-model="query.date_field" class="ya-date-field" placeholder="时间类型">
          <el-option value="" label="请选择" />
          <el-option value="create_time" label="添加时间" />
          <el-option value="update_time" label="修改时间" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="query.date_value"
          type="datetimerange"
          class="ya-date-value"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="[new Date(2024, 1, 1, 0, 0, 0), new Date(2024, 1, 1, 23, 59, 59)]"
        />
      </el-form-item>
      <br />

      <el-form-item label="区域板块" prop="sector_name">
        <el-cascader
          v-model="query.sector_name"
          :options="sectorData"
          :props="sectorProps"
          ref="mycascader"
          @change="changeCasc"
          class="w-full"
          placeholder="请选择板块"
          clearable
          filterable
          style="width: 140px"
        />
      </el-form-item>
      <el-form-item label="发布状态：" prop="release_status">
        <el-select v-model="query.release_status" style="width: 120px">
          <el-option value="-1" label="不限" />
          <el-option
            v-for="(item, index) in search_params['releaseStatus']"
            :key="index"
            :label="item"
            :value="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" title="搜索" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
        <el-button type="primary">
          <router-link to="./info">添加</router-link>
        </el-button>
        <el-button
          ><a
            href="https://img.betterhousing.cn/storage/file/20240725/362e58aba89ec512b963400472f96d65186b4063.xlsx"
            >下载导入模板</a
          >
        </el-button>
        <excel-import
          v-if="checkPermission(['admin/house.Index/info'])"
          title="批量导入"
          @on-import="imports"
        />
        <!-- 
        <el-button @click="getMaidoHouse('all')">同步所有楼盘</el-button>
        <el-button @click="getMaidoHouse('newest')">同步新增楼盘</el-button> 
        -->
      </el-form-item>
    </el-form>
    <!-- 搜索end -->
    <el-dialog
      v-model="selectDialog"
      :title="selectTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      top="20vh"
    >
      <el-form ref="selectRef" label-width="120px">
        <el-form-item v-if="selectType === 'istop'" label="是否置顶">
          <el-switch v-model="is_top" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item v-else-if="selectType === 'ishot'" label="是否热门">
          <el-switch v-model="is_hot" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item v-else-if="selectType === 'isrec'" label="是否推荐">
          <el-switch v-model="is_rec" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item v-else-if="selectType === 'disable'" label="是否禁用">
          <el-switch v-model="is_disable" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item v-else-if="selectType === 'release'" label="楼盘名称">
          <el-input v-model="selectNames" type="textarea" autosize disabled />
        </el-form-item>
        <el-form-item v-else-if="selectType === 'dele'">
          <span class="c-red">确定要删除选中的{{ name }}吗？</span>
        </el-form-item>
        <el-form-item :label="name + 'ID'">
          <el-input v-model="selectIds" type="textarea" autosize disabled />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button :loading="loading" @click="selectCancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="selectSubmit">提交</el-button>
      </template>
    </el-dialog>
    <el-table
      ref="table"
      v-loading="loading"
      :data="data"
      height="600"
      @sort-change="sort"
      @selection-change="select"
    >
      <el-table-column type="selection" width="42" title="全选/反选" />
      <el-table-column :prop="idkey" label="ID" width="80" sortable="custom" />
      <el-table-column prop="name" label="楼盘名称" min-width="180" show-overflow-tooltip />
      <el-table-column prop="is_land" label="类型" show-overflow-tooltip>
        <template #default="scope">
          <span v-text="scope.row.is_land == 0 ? '楼盘' : '地块'"></span>
        </template>
      </el-table-column>
      <el-table-column
        prop="develop_brand"
        label="开发品牌"
        min-width="150"
        show-overflow-tooltip
      />

      <el-table-column prop="project_status" label="销售状态" min-width="110" show-overflow-tooltip>
        <template #default="scope">
          <span
            v-text="
              scope.row.project_status != null
                ? search_params['houseSalestate'][parseInt(scope.row.project_status)]
                : '-'
            "
          ></span>
        </template>
      </el-table-column>
      <el-table-column prop="region_name" label="区域" min-width="130" />
      <el-table-column prop="sector_name" label="板块" min-width="130" />
      <el-table-column prop="totalprice" label="总价" min-width="160" show-overflow-tooltip>
        <template #default="scope">
          <span
            v-text="scope.row.min_total_price + '-'"
            v-if="scope.row.min_total_price != null"
          ></span>

          <span
            v-text="scope.row.max_total_price == null ? '' : scope.row.max_total_price + '万/套'"
          ></span>
        </template>
      </el-table-column>

      <el-table-column prop="update_time" label="更新时间" min-width="165" sortable="custom">
        <template #default="scope">
          <span v-text="scope.row.update_time" v-if="scope.row.update_time != null"></span>

          <span v-else v-text="scope.row.create_time"></span>
        </template>
      </el-table-column>
      <el-table-column prop="release_status" label="发布状态" min-width="165" sortable="custom">
        <template #default="scope">
          <span v-text="search_params['releaseStatus'][parseInt(scope.row.release_status)]"></span>
        </template>
      </el-table-column>

      <el-table-column prop="release_status" label="浏览量" min-width="165" sortable="custom">
        <template #default="scope">
          <span v-text="scope.row.real_view"></span>
        </template>
      </el-table-column>
      <el-table-column prop="release_status" label="全浏览量" min-width="165" sortable="custom">
        <template #default="scope">
          <span v-text="scope.row.click_num"></span>
        </template>
      </el-table-column>
      <el-table-column prop="release_status" label="关注量" min-width="165" sortable="custom">
        <template #default="scope">
          <span v-text="scope.row.follow_num"></span>
        </template>
      </el-table-column>

      <el-table-column prop="sale_count" label="销售套数" min-width="165" sortable="custom">
        <template #default="scope">
          <span v-text="scope.row.sale_count"></span>
        </template>
      </el-table-column>
      <el-table-column prop="sale_count" label="销售金额" min-width="165" sortable="custom">
        <template #default="scope">
          <span v-text="scope.row.sale_amount + ' 万'"></span>
        </template>
      </el-table-column>
      <el-table-column prop="add_time" label="上新时间" min-width="165" sortable="custom">
        <template #default="scope">
          <span v-text="scope.row.add_time"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="125" fixed="right">
        <template #default="scope">
          <el-link
            type="primary"
            class="mr-1"
            :underline="false"
            :href="
              scope.row.is_land == 0
                ? '#/house/info?id=' + scope.row.id
                : '#/house/land?id=' + scope.row.id
            "
          >
            编辑
          </el-link>
          <!-- <router-link type="primary" class="mr-1" to="/house/info">添加</router-link> -->
          <el-link
            type="primary"
            class="mr-1"
            v-if="scope.row.release_status != 2"
            :underline="false"
            @click="selectOpen('release', scope.row, 'up')"
          >
            发布
          </el-link>
          <el-link
            type="primary"
            class="mr-1"
            v-if="scope.row.release_status == 2"
            :underline="false"
            @click="selectOpen('release', scope.row, 'down')"
          >
            下架
          </el-link>

          <!-- <el-link
            type="primary"
          
            :underline="false"
            @click="getMaidoHouse('newst', scope.row.id)"
          >
            同步
          </el-link> -->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="count > 0"
      v-model:total="count"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="houseList"
    />
  </div>
</template>
<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import checkPermission from '@/utils/permission'
import screenHeight from '@/utils/screen-height'
import Pagination from '@/components/Pagination/index.vue'
import { getPageLimit } from '@/utils/settings'
import * as Project from '@/api/project/house'
import { useFormDisabled } from 'element-plus'
import { arrayColumn } from '@/utils/index'
import request from '@/utils/request'
import router from '@/router'

const model = ref({})
const rowInfo = ref({})

const loading = ref(true)
const data = ref([])
const search_params = ref([])
const height = ref(680)
const query = ref({
  page: 1,
  limit: 15,
  region_id: 0,
  plate_id: 0,
  search_field: 'name',
  search_exp: 'like',
  date_field: 'create_time',
  date_value: [null, null]
})
const count = ref(0)
const idkey = ref('id')
const name = ref('楼盘')
const dialog = ref(false)
const dialogTitle = ref('')
const selectDialog = ref(false)
const selectType = ref('')
const selectIds = ref('')
const selectNames = ref('')
const selectTitle = ref('操作')
const realseOpt = ref('')
const sectorData = ref([])
const sectorProps = ref({
  expandTrigger: 'hover',
  checkStrictly: true,
  value: 'id',
  label: 'name'
  // emitPath: false
})

function houseList() {
  loading.value = true
  Project.houseList(query.value)
    .then((res) => {
      data.value = res.data.list
      count.value = res.data.count
      loading.value = false
    })
    .catch((error) => {
      loading.value = false
    })
}

function searchparams() {
  loading.value = true
  Project.searchparams()
    .then((res) => {
      console.log(res)
      search_params.value = res.data
      // sectorData.value = res.data.sectors
      sectorData.value = res.data.regionPlates
      // 获取楼盘列表
      houseList()
      loading.value = false
    })
    .catch((error) => {
      console.log(error)
      loading.value = false
    })
}

// 选择 城区板块
function changeCasc(selectValuesArr) {
  if (selectValuesArr == undefined) {
    query.value.region_id = 0
    query.value.plate_id = 0
    return
  } else {
    console.log(selectValuesArr)
    if (selectValuesArr.length == 1) {
      query.value.region_id = selectValuesArr[0]
    }

    if (selectValuesArr.length == 2) {
      query.value.region_id = selectValuesArr[0]
      query.value.plate_id = selectValuesArr[1]
    }
  }
}

// 排序
function sort(sort) {
  query.value.sort_field = sort.prop
  query.value.sort_value = ''
  console.log('query.value:', query.value)
  if (sort.order === 'ascending') {
    query.value.sort_value = 'asc'
    houseList()
  }
  if (sort.order === 'descending') {
    query.value.sort_value = 'desc'
    houseList()
  }
}

// 导入，results数据，header表头
function imports({ results, header }) {
  loading.value = true
  Project.houseexport({
    import: results
  })
    .then((res) => {
      houseList()
      ElMessage.success(res.msg)
    })
    .catch(() => {
      loading.value = false
    })
}

// 操作
function select(selection) {
  this.selection = selection
  this.selectIds = selectGetIds(selection).toString()
}

function selectGetIds(selection) {
  return arrayColumn(selection, this.idkey)
}

function cancel() {
  dialog.value = false
  reset()
}

//搜索
const queryRef = ref(null)

function resetQuery() {
  query.value.status = '-1'
  query.value.id = ''
  query.value.release_status = '-1'
  query.value.region_id = 0
  query.value.plate_id = 0
  query.value.date_field = ''
  query.value.date_value[0] = ''
  query.value.date_value[1] = ''
  queryRef.value.resetFields()
  handleQuery()
}

function handleQuery() {
  query.value.pageNum = 1
  houseList()
}

//搜索end
// 重置
function reset(row) {
  if (row) {
    model.value = row
  } else {
    model.value = {}
  }
}

function getMaidoHouse(syncType = 'newst', house_id = 0) {
  var params = {
    page: 1,
    pagesize: 20,
    isupdate: 1
  }
  if (syncType == 'all') {
    params['pagesize'] = 1000
  }
  if (house_id != 0) {
    params['houseid'] = house_id
  }
  loading.value = true
  request({
    url: '/migrate/maido.house/house',
    method: 'post',
    data: params
  })
    .then((res) => {
      ElMessage.success('同步成功')
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}

onMounted(() => {
  height.value = screenHeight(310)
  // 加载参数列表
  searchparams()
})

function selectAlert() {
  ElMessageBox.alert('请选择需要操作的' + this.name, '提示', {
    type: 'warning',
    callback: () => {}
  })
}

function selectOpen(sectType, selectRow = '', opt = '') {
  var optName = opt == 'up' ? '发布' : '下架'
  realseOpt.value = opt
  if (sectType === 'editcate') {
    selectTitle.value = name.value + '修改分类'
  } else if (sectType === 'edittag') {
    selectTitle.value = name.value + '修改标签'
  } else if (sectType === 'istop') {
    selectTitle.value = name.value + '是否置顶'
  } else if (sectType === 'ishot') {
    selectTitle.value = name.value + '是否热门'
  } else if (sectType === 'isrec') {
    selectTitle.value = name.value + '是否推荐'
  } else if (sectType === 'disable') {
    selectTitle.value = name.value + '是否禁用'
  } else if (sectType === 'release') {
    selectTitle.value = name.value + optName
  } else if (sectType === 'dele') {
    selectTitle.value = name.value + '删除'
  }
  selectIds.value = selectRow.id
  selectNames.value = selectRow.name
  selectDialog.value = true
  selectType.value = sectType
}

function selectCancel() {
  selectDialog.value = false
}

function selectSubmit() {
  if (selectType.value === 'editcate') {
    // this.editcate(this.selection)
  } else if (selectType.value === 'edittag') {
    // this.edittag(this.selection)
  } else if (selectType.value === 'istop') {
    // this.istop(this.selection, true)
  } else if (selectType.value === 'ishot') {
    // this.ishot(this.selection, true)
  } else if (selectType.value === 'isrec') {
    // this.isrec(this.selection, true)
  } else if (selectType.value === 'disable') {
    // this.disable(this.selection, true)
  } else if (selectType.value === 'release') {
    release()
  } else if (selectType.value === 'dele') {
    // this.dele(this.selection)
  }
  selectDialog.value = false
}

// 发布时间
function release() {
  Project.releaseHouse({
    ids: selectIds.value,
    release: realseOpt.value == 'up' ? 2 : 3
  })
    .then((res) => {
      houseList()
      ElMessage.success(res.msg)
    })
    .catch(() => {
      houseList()
    })
}

const handleKeyDown = (event) => {
  if (event.key === 'Enter') {
    handleQuery()
  }
}

onMounted(() => {
  window.addEventListener('keydown', handleKeyDown)
})

onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleKeyDown)
})
</script>
