<template>
  <div class="app-container">
    <el-form :model="form" ref="formRef" label-position="top" :rules="rules">
      <!-- 基础信息 -->
      <div class="filter-section">
        <div class="section-title">编辑装饰品牌</div>
        <el-form-item label="品牌中文名称" prop="cn_name">
          <el-input v-model="form.cn_name" placeholder="输入品牌中文名称" />
        </el-form-item>
        <el-form-item label="品牌英文名称" prop="en_name">
          <el-input v-model="form.en_name" placeholder="输入品牌英文名称" />
        </el-form-item>
        <el-form-item label="名称展示选择" prop="show">
          <el-radio-group v-model="form.show">
            <el-radio label="1">中文</el-radio>
            <el-radio label="2">英文</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="品牌logo" prop="pic">
          <ImgUploadsHouseType
            v-model="form.pic"
            upload-btn="上传图片"
            file-type="image"
            file-tip=""
            :isWatermark="form.is_watermark"
            :source="0"
          />
        </el-form-item>
        <el-form-item label="级别" prop="level">
          <el-radio-group v-model="form.level">
            <el-radio label="1">经济</el-radio>
            <el-radio label="2">中档</el-radio>
            <el-radio label="3">高档</el-radio>
            <el-radio label="4">豪华</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
    </el-form>
    <div class="footer">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import ImgUploadsHouseType from '@/components/FileManage/ImgUploadsHouseType.vue'
import request from '@/utils/request'
import { useRouter, useRoute } from 'vue-router'

// 状态变量
const router = useRouter()
const route = useRoute()
const formRef = ref(null)
const loading = ref(false)

// 表单数据
const form = reactive({
  id: null,
  cn_name: '',
  en_name: '',
  show: '1', // 默认值
  pic: [], // 初始化为数组，兼容 ImgUploadsHouseType
  level: '1', // 默认值
  is_watermark: 0
})

// 表单验证规则
const rules = {
  cn_name: [{ required: true, message: '请输入品牌中文名称', trigger: 'blur' }],
  en_name: [{ required: true, message: '请输入品牌英文名称', trigger: 'blur' }],
  show: [{ required: true, message: '请选择名称展示方式', trigger: 'change' }],
  pic: [{ required: true, message: '请上传品牌logo', trigger: 'change' }],
  level: [{ required: true, message: '请选择级别', trigger: 'change' }]
};

// 取消
const cancel = () => {
  router.push('/evaluating/decorationBrand') // 返回列表页面
}

// 提交
const submit = async () => {
  if (!formRef.value) return;
  try {
    loading.value = true;
    await formRef.value.validate();
    // 确保 pic 是数组或单个 URL，视接口要求调整
    const submitData = {
      id: form.id,
      cn_name: form.cn_name,
      en_name: form.en_name,
      show: form.show,
      pic: Array.isArray(form.pic) ? form.pic[0] : form.pic, // 取第一个 URL 或直接使用
      level: form.level,
      is_watermark: form.is_watermark
    };
    const res = await request.post('/admin/furnish.DecorationBrand/update', submitData);
    if (res.code === 200) {
      ElMessage.success('编辑成功');
      router.push('/evaluating/decorationBrand'); // 编辑成功后返回
    }
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error('编辑失败');
  } finally {
    loading.value = false;
  }
};

// 获取详情数据
const fetchDetail = async (id) => {
  try {
    loading.value = true;
    const res = await request.get('/admin/furnish.DecorationBrand/detail', { params: { id } });
    if (res.code === 200) {
      const data = res.data;
      // 映射 show 值（假设 0 表示默认中文，需根据业务确认）
      const mappedShow = data.show === 0 ? '1' : data.show.toString();
      // 提取 pic URL
      const picUrl = data.pic ? data.pic.file_url : '';
      // 转换为字符串 level
      const mappedLevel = data.level.toString();
      // 更新表单数据
      Object.assign(form, {
        id: data.id,
        cn_name: data.cn_name,
        en_name: data.en_name,
        show: mappedShow,
        pic: picUrl ? [picUrl] : [], // 转换为数组，兼容 ImgUploadsHouseType
        level: mappedLevel,
        is_watermark: data.release_status === 2 ? 1 : 0 // 假设 release_status 2 表示水印
      });
    } else {
      ElMessage.error('获取详情失败');
    }
  } catch (error) {
    console.error('获取详情失败:', error);
    ElMessage.error('获取详情失败');
  } finally {
    loading.value = false;
  }
};

// 页面加载，初始化数据
onMounted(() => {
  nextTick(() => {
    console.log('Component mounted, formRef:', formRef.value);
    const { id } = route.query;
    if (id) {
      fetchDetail(id); // 根据 id 获取详情
    }
  });
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-section {
  background-color: #ffffff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #303133;
}

:deep(.el-form-item__label) {
  font-weight: bold;
  color: #606266;
}

.footer {
  margin-top: 20px;
  text-align: right;
}
</style>