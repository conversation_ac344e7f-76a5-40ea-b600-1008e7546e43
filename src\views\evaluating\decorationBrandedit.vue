<template>
  <div class="app-container">
    <el-form :model="form" ref="formRef" label-position="top" :rules="rules">
      <!-- 基础信息 -->
      <div class="filter-section">
        <div class="section-title">编辑装饰品牌</div>
        <el-form-item label="品牌中文名称" prop="cn_name">
          <el-input v-model="form.cn_name" placeholder="输入品牌中文名称" />
        </el-form-item>
        <el-form-item label="品牌英文名称" prop="en_name">
          <el-input v-model="form.en_name" placeholder="输入品牌英文名称" />
        </el-form-item>
        <el-form-item label="名称展示选择" prop="show">
          <el-radio-group v-model="form.show">
            <el-radio :label="1" :disabled="!form.cn_name" :key="1">
              <span>
                中文
                <el-tooltip
                  :disabled="form.show === '1' && !form.cn_name"
                  content="暂无中文"
                  placement="top"
                />

              </span>
            </el-radio>
            <el-radio :label="2" :disabled="!form.en_name" :key="2">
              <span>
                英文
                <el-tooltip
                  :disabled="form.show === '2' && !form.en_name"
                  content="暂无英文"
                  placement="top"
                />
              </span>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="品牌logo" prop="pic_id">
          <ImgUpload
            :isWatermark="form.is_watermark"
            :source="0"
            v-model="form.pic_id"
            v-model:file-url="form.pic_url"
            file-type="image"
            :height="100"
            upload
          />
        </el-form-item>
        <el-form-item label="级别" prop="level">
          <el-radio-group v-model="form.level">
            <el-radio label="1">经济</el-radio>
            <el-radio label="2">中档</el-radio>
            <el-radio label="3">高档</el-radio>
            <el-radio label="4">豪华</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
    </el-form>
    <div class="footer">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import ImgUpload from '@/components/FileManage/ImgUpload.vue'
import request from '@/utils/request'
import { useRouter, useRoute } from 'vue-router'

// 状态变量
const router = useRouter()
const route = useRoute()
const formRef = ref(null)
const loading = ref(false)

// 表单数据
const form = reactive({
  id: null,
  cn_name: '',
  en_name: '',
  show: '', // 初始为空，依赖回显
  pic_url: '',
  pic_id: 0,
  level: '1', // 默认值
  is_watermark: 0
})

// 表单验证规则
const rules = {
  cn_name: [
    {
      validator: (rule, value, callback) => {
        if (!value && !form.en_name) {
          callback(new Error('品牌中文名称和英文名称至少填写一个'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  en_name: [
    {
      validator: (rule, value, callback) => {
        if (!value && !form.cn_name) {
          callback(new Error('品牌中文名称和英文名称至少填写一个'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  show: [{ required: true, message: '请选择名称展示方式', trigger: 'change' }],
  pic_id: [], // 非必填
  level: [{ required: true, message: '请选择级别', trigger: 'change' }]
}

// 取消
const cancel = () => {
  router.push('/evaluating/decorationBrand') // 返回列表页面
}

// 提交
const submit = async () => {
  if (!formRef.value) return
  try {
    loading.value = true
    await formRef.value.validate()
    const submitData = {
      id: form.id,
      cn_name: form.cn_name,
      en_name: form.en_name,
      show: form.show,
      pic_id: form.pic_id,
      level: form.level,
      is_watermark: form.is_watermark
    }
    const res = await request.post('/admin/furnish.DecorationBrand/update', submitData)
    if (res.code === 200) {
      ElMessage.success('编辑成功')
      router.push('/evaluating/decorationBrand') // 编辑成功后返回
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('编辑失败')
  } finally {
    loading.value = false
  }
}

// 获取详情数据
const fetchDetail = async (id) => {
  try {
    loading.value = true
    const res = await request.get('/admin/furnish.DecorationBrand/detail', { params: { id } })
    if (res.code === 200) {
      const data = res.data
      // 映射 show 值，假设 0 表示中文，其他为英文或默认中文
      // const mappedShow = data.show === 0 || data.show === '0' || data.show === 1 || data.show === '1' ? '1' : (data.show === 2 || data.show === '2' ? '2' : '1')
      // 填充表单数据
      Object.assign(form, {
        id: data.id,
        cn_name: data.cn_name || '',
        en_name: data.en_name || '',
        show: data.show, // 回显并默认选中
        pic_url: data.pic_url || '',
        pic_id: data.pic_id || 0,
        level: data.level.toString() || '1',
      })
      // 确保 DOM 更新
      await nextTick()
      console.log('form.show after fetchDetail:', form.show) // 调试用
    } else {
      ElMessage.error('获取详情失败')
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 页面加载，初始化数据
onMounted(() => {
  nextTick(() => {
    console.log('Component mounted, formRef:', formRef.value)
    const { id } = route.query
    if (id) {
      fetchDetail(id) // 根据 id 获取详情
    } else {
      form.show = '1' // 无 id 时默认选中中文
    }
  })
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-section {
  background-color: #ffffff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #303133;
}

:deep(.el-form-item__label) {
  font-weight: bold;
  color: #606266;
}

.footer {
  margin-top: 20px;
  text-align: right;
}
</style>