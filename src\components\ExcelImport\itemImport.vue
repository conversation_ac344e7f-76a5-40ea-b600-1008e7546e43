<!-- 来自当前文件目录下的index.vue audited by <PERSON><PERSON><PERSON><PERSON><PERSON>@zmxf 2024-11-26 -->
<template>
  <div class="cp-wrap">
    <input
      ref="excel-upload-input"
      class="excel-upload-input"
      type="file"
      accept=".xlsx, .xls, .csv"
      @change="handleClick"
    />
    <el-button :loading="loading" @click="handleUpload">{{ title }}</el-button>
    <el-dialog
      v-model="dialogShow"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      top="5vh"
      width="88%"
    >
      <el-table v-loading="loading" :data="excelData.results" :height="height" style="width: 100%">
        <el-table-column
          v-for="item in excelData.header"
          :key="item"
          :prop="item"
          :label="item"
          sortable
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row[item] }}
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <el-button :loading="loading" @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submit">导入</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import * as XLSX from 'xlsx'
import screenHeight from '@/utils/screen-height'
import dayjs from 'dayjs'
// 表格导入
export default {
  props: {
    limitSize: { type: Number, default: 1 },
    title: { type: String, default: '导入' },
    itemid: { type: Number, default: 0 }
  },
  data() {
    return {
      loading: false,
      height: 580,
      dialogTitle: '导入预览',
      dialogShow: false,
      excelData: {
        filename: null,
        header: null,
        results: null
      }
    }
  },
  created() {
    this.height = screenHeight(340)
  },
  methods: {
    formatTime(date) {
      return dayjs(date).add(1, 'day').format('YYYY-MM-DD')
    },
    cancel() {
      this.dialogShow = false
    },
    submit() {
      this.dialogShow = false
      this.$emit('on-import', this.excelData)
    },
    generateData({ header, results,filename }) {
      this.excelData.header = header
      this.excelData.results = results
      this.excelData.filename = filename
      this.dialogShow = true
    },
    handleDrop(e) {
      e.stopPropagation()
      e.preventDefault()
      if (this.loading) return
      const files = e.dataTransfer.files
      if (files.length !== 1) {
        ElMessage.error('只能上传一个文件')
        return
      }
      const rawFile = files[0]

      if (!this.isExcel(rawFile)) {
        ElMessage.error('文件类型仅支持 xlsx、xls、csv')
        return false
      }
      this.upload(rawFile)
      e.stopPropagation()
      e.preventDefault()
    },
    handleUpload() {
      this.$refs['excel-upload-input'].click()
    },
    handleClick(e) {
      const files = e.target.files
      const rawFile = files[0]
      if (!rawFile) return
      this.upload(rawFile)
    },
    upload(rawFile) {
      this.$refs['excel-upload-input'].value = null

      if (!this.beforeUpload) {
        this.readerData(rawFile)
        return
      }
      const before = this.beforeUpload(rawFile)
      if (before) {
        this.readerData(rawFile)
      }
    },
    beforeUpload(file) {
      const limitSize = this.limitSize
      const fileSize = file.size / 1024 / 1024

      if (fileSize > limitSize) {
        ElMessage.error(`文件大小不能大于 ${limitSize} m`)
        return false
      }

      return true
    },
    readerData(rawFile) {
		const filename = rawFile.name
      this.loading = true
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = (e) => {
          const data = e.target.result
          const workbook = XLSX.read(data, { type: 'array', cellDates: true })
          const firstSheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[firstSheetName]
          const header = this.getHeaderRow(worksheet)
          const results = XLSX.utils.sheet_to_json(worksheet, { defval: '' })
          this.generateData({ header, results,filename})
          this.loading = false
          resolve()
        }
        reader.readAsArrayBuffer(rawFile)
      })
    },
    getHeaderRow(sheet) {
      const headers = []
      const range = XLSX.utils.decode_range(sheet['!ref'])
      let C
      const R = range.s.r
      for (C = range.s.c; C <= range.e.c; ++C) {
        const cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })]
        let hdr = 'UNKNOWN ' + C
        if (cell && cell.t) hdr = XLSX.utils.format_cell(cell)
        headers.push(hdr)
      }
      return headers
    },
    isExcel(file) {
	  this.excelData.file.name = file.name
      return /\.(xlsx|xls|csv)$/.test(file.name)
    }
  }
}
</script>

<style scoped>
.cp-wrap {
  margin-left: 10px;
}
.excel-upload-input {
  display: none;
  z-index: -9999;
}
</style>
