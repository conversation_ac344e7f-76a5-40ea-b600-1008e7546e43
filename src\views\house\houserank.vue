<template>
      <div class="app-container">
        <!-- 操作 -->
    <el-row>
      <el-col>
        <el-button title="新增" class="float-right" @click="add()">新增</el-button>
      </el-col>
    </el-row>
      </div>

    <!-- 列表 -->
  <el-table
    ref="table"
    v-loading="loading"
    :data="data"
    :height="height"
    @sort-change="sort"
    @selection-change="select"
    key="table1"
  >
    <el-table-column type="selection" width="42" title="全选/反选" />
    <el-table-column :prop="idkey" label="ID" width="80" sortable="custom" />
    <el-table-column prop="name" label="榜单名称" width="120"/>
    <el-table-column prop="ranktype" label="榜单类型" />
    <el-table-column prop="ranksubtype" label="展现形式" />
    <el-table-column prop="create_time" label="创建时间"  />
    <el-table-column prop="update_time" label="更新时间" />
    <el-table-column label="操作" width="95">
      <template #default="scope">
        <el-link type="primary" class="mr-1" :underline="false" @click="edit(scope.row)">
          修改
        </el-link>
      </template>
    </el-table-column>
  </el-table>
  <!-- 分页 -->
  <pagination
    v-show="count > 0"
    v-model:total="count"
    v-model:page="query.page"
    v-model:limit="query.limit"
    @pagination="evaluatingPriceList"
  />

  <el-dialog
    v-model="detailloading"
    :title="dialogTitle"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="cancel"
    width="90%"
    top="1vh"
  >


</el-dialog>

<!-- 添加修改 -->
<el-dialog
      v-model="dialog"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="cancelForm"
      top="5vh"
    >
      <el-form ref="ref" :rules="rules" :model="rowInfo" label-width="100px">
        <el-tabs>
          <el-tab-pane label="信息">
            <el-scrollbar native :height="height - 80">
              
              <el-form-item label="名称" prop="sale_count">
                <el-input v-model="rowInfo.name" placeholder="名称"/>
              </el-form-item>
			  <el-form-item label="类型" prop="ranktype">
			    <el-select v-model="rowInfo.ranktype" class="w-full" clearable filterable>
			      <el-option
			        v-for="(item,key) in houseRankType"
			        :key="key"
			        :label="item"
			        :value="key"
			      />
			    </el-select>
			  </el-form-item>
			  <el-form-item label="子类型" prop="ranksubtype">
			    <el-select v-model="rowInfo.ranksubtype" class="w-full" clearable filterable>
			      <el-option
			        v-for="(item,key) in houseRankSubType"
			        :key="key"
			        :label="item"
			        :value="key"
			      />
			    </el-select>
			  </el-form-item>
			  <el-form-item label="展现形式" prop="showtype">
			    <el-radio-group
			      v-model="rowInfo.showtype"
			      style="margin-left: 10px"
			    >
			      <el-radio
			        v-for="(value, key) in houseRankShowType"
			        :label="value"
			        :key="key"
			        :value="key"
			      ></el-radio>
			    </el-radio-group>
			  </el-form-item>
			  <el-form-item label="楼盘数" prop="count">
			    <el-input v-model="rowInfo.count" placeholder="名称"/>
			  </el-form-item>
			  <el-form-item
			    label="楼盘列表"
			    
			  >
				<el-col v-for="(item,index) in houseRankList">
					<el-col :span="4" >
					  <el-select v-model="item.sort" class="w-full" filterable clearable>
						<el-option
						  :key="item.sort"
						  :label="item.sort"
						  :value="item.sort"
						/>
					  </el-select>
					</el-col>
					<el-col :span="10">
					  <el-select 
					  v-model="item.id" 
					  class="w-full" 
					  filterable 
					  clearable 
					  remote 
					  :remote-method="houselist" 
					  @click="getFocus(index)"
					  :loading="loading"
					  >
							<el-option
							  v-for="(value, k) in houseRankSortList[item.sort]"
							  :key="value.id"
							  :label="value.name"
							  :value="value.id"
							/>
					  </el-select>
					</el-col>
			  
					<el-col :span="5" :offset="1">
						<el-button @click="uppPoints(index)" type="primary" v-if="index > 0">+</el-button>
						<el-button @click="downPoints(index)" v-if="index < (rowInfo.count -1) ">-</el-button>
					</el-col>
			   </el-col>
			   <el-button :loading="loading" type="primary" @click="getHouseList">获取楼盘列表</el-button>
			  </el-form-item>
			  
              <el-form-item label="展现天数" prop="sale_amount">
                <el-input v-model="rowInfo.sale_amount" placeholder="请输入销售金额" />
              </el-form-item>
              <el-form-item label="有效日期" prop="sale_time">
                <el-date-picker
                  v-model="rowInfo.sale_time"
                  type="date"
                  value-format="YYYY-MM-DD"
                  :default-time="new Date(2024, 1, 1)"
                  placeholder="有效日期"
                />
				-
				<el-date-picker
				  v-model="rowInfo.sale_time"
				  type="date"
				  value-format="YYYY-MM-DD"
				  :default-time="new Date(2024, 1, 1)"
				  placeholder="有效日期"
				/>
              </el-form-item>
              
              <el-form-item v-if="rowInfo[idkey]" label="添加时间" prop="create_time">
                <el-input v-model="rowInfo.create_time" disabled />
              </el-form-item>
              <el-form-item v-if="rowInfo[idkey]" label="修改时间" prop="update_time">
                <el-input v-model="rowInfo.update_time" disabled />
              </el-form-item>
              <el-form-item v-if="rowInfo.delete_time" label="删除时间" prop="delete_time">
                <el-input v-model="rowInfo.delete_time" disabled />
              </el-form-item>
            </el-scrollbar>
          </el-tab-pane>
         
        </el-tabs>
      </el-form>
      <template #footer>
        <el-button :loading="loading" @click="cancelForm">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submit">提交</el-button>
      </template>
    </el-dialog>
</template>
<script setup>

import {ref, onMounted} from "vue"
import checkPermission from '@/utils/permission';
import Pagination from '@/components/Pagination/index.vue'
import * as HouseRank from '@/api/project/houserank'
import { getPageLimit } from '@/utils/settings'
import ExcelImport from '@/components/ExcelImport/multipleupload.vue'
import { customformat } from '@/utils/dateUtil'
import { houseList } from '@/api/project/house'
const loading = ref(true);

const data = ref([])
const height = ref(560)
const dialogheight = ref(700)
const query = ref({
        page: 1,
        limit: getPageLimit()
      });
const count = ref(0);
const idkey = ref('id');
const detailloading = ref(false);
const tableloading = ref(true);
const currentIndex = ref(null);
const rowInfo = ref({
	"id":null,
	"name":null,
	"ranktype":null,
	"ranksubtype":null,
	"showtype":"0",
	"count":10,
});
const houseRankType = ref({
	"sale":"销售榜",
	"upnew":"上新榜",
	"hot":"热度榜",
	"sunshine": "日照榜",
	"noise":"噪声榜",
	"landscape":"景观榜",
	"facility":"配套榜"
});
const houseRankShowType = ref({
	"0":"无限期",
	"1":"日榜单",
	"7":"周榜单",
	"30":"月榜单",
});
const houseRankSubType = ref({
	"count":"套数榜",
	"amount":"金额榜"
});
const houseRankList = ref([]);
const houseRankSortList = ref([]);
const dialog = ref(false);
const dialogTitle = ref('');


function rankList(){
    loading.value = true;
    HouseRank.rankList(query.value).then((res) => {
	  data.value = res.data.list
	  count.value = res.data.count
  
	  loading.value = false
	})
	.catch(() => {
	  loading.value = false
	})

}
// 新增楼盘销售记录
function add() {
	var timess = getCurrentDate()
	rowInfo.value.sale_time = timess.toString()
	rowInfo.value.house_id = null
	console.log(rowInfo.value)
  loading.value = true;
  dialog.value = true
  dialogTitle.value = '新增排行榜：' 
  
  loading.value = false

}
// 新增楼盘销售记录
function getCurrentDate() {
  let currentDate = new Date();
  let year = currentDate.getFullYear();
  let month = currentDate.getMonth() + 1;  // 注意月份从0开始，需要加1
  if (month <10) {
  	month = "0"+month
  }
  let day = currentDate.getDate();
  // 输出当前日期
  return year + "-" + month + "-" + day;

}
function edit(row) {

  rowInfo.value = row
  loading.value = true;
  dialog.value = true
  dialogTitle.value = row.name + '：' + row[idkey.value]
  var id = {}
  id[idkey.value] = row[idkey.value]
  HouseRank.rankInfo(id)
    .then((res) => {
      reset(res.data)
    })
    .catch(() => {})
  loading.value = false

}
// 重置
function reset(row) {
  if (row) {
	row.showtype = row.showtype.toString()
	row.ranksubtype = row.ranksubtype.toString()
	row.ranktype = row.ranktype.toString()
    rowInfo.value = row
  } else {
    rowInfo.value = {}
  }
}
// 获取楼盘列表
function getHouseList(){
	var requestData = {
		'ranktype':rowInfo.ranktype,
		"ranksubtype":rowInfo.ranksubtype,
		"showtype":rowInfo.showtype,
		"limit":rowInfo.count,
	}
	HouseRank.getHouseList(requestData).then((res) => {
	  houseRankList.value = res.data
	  
	  res.data.forEach((item,index) => {
		  houseRankSortList.value[index] = res.data
		})
		
	  loading.value = false
	})
	.catch(() => {
	  loading.value = false
	})
	
}
// 根据楼盘名称获取楼盘列表
function houselist(keywords){
	
	houseList({"name":keywords})
	  .then((res) => {
		  houseRankSortList.value[currentIndex.value] = res.data.list
	  })
	  .catch((error) => {
		
	  })
}
// 向上移动一位
function uppPoints(index){
	var temp = houseRankList.value[index-1]
	houseRankList.value[index-1] = houseRankList.value[index]
	houseRankList.value[index] = temp
	console.log(houseRankList.value[index])
	console.log(houseRankList.value[index-1])
	
	var tmp = houseRankSortList.value[index-1]
	houseRankSortList.value[index-1] = houseRankSortList.value[index]
	houseRankSortList.value[index] = tmp
	console.log(houseRankSortList.value[index])
	console.log(houseRankSortList.value[index-1])
	
}
// 向下移动一位
function downPoints(index){
	var temp = houseRankList.value[index+1]
	houseRankList.value[index+1] = houseRankList.value[index]
	houseRankList.value[index] = temp
	
	var tmp = houseRankSortList.value[index+1]
	houseRankSortList.value[index+1] = houseRankSortList.value[index]
	houseRankSortList.value[index] = tmp
}
function submit() {
    loading.value = true;
    // 验证
    let dataEdit = {}

    if (rowInfo.value.sale_time == null || rowInfo.value.sale_time=="") {
      ElMessage.error("请选择销售时间")
	  loading.value = false;
	  return
    }
	if (rowInfo.value.sale_count == null || rowInfo.value.sale_count=="") {
	  ElMessage.error("请填写销售套数")
	  loading.value = false;
	  return
	}
	if (rowInfo.value.sale_amount == null || rowInfo.value.sale_amount=="") {
	  ElMessage.error("请填写销售金额")
	  loading.value = false;
	  return
	}
	  
	HouseSales.editSales(rowInfo.value).then((res) => {
		rankList()
		dialog.value = false
	})
	  .catch(() => {
		loading.value = false
	})
    
    reset()
    loading.value = false
}
function cancelForm() {
  loading.value = false
  reset()
  dialog.value = false
  
}
function getFocus(index){
	currentIndex.value=index
}

// 操作
function select(selection) {
      this.selection = selection
      this.selectIds = this.selectGetIds(selection).toString()
    }
onMounted(()=>{
    rankList()
})
</script>