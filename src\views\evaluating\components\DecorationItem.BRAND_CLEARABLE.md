# 品牌下拉列表单个选项清除功能

## 功能说明

在品牌的多选下拉列表中，实现了可以清除单个选中品牌的功能。每个选中的品牌会显示为一个带有关闭按钮的标签，点击关闭按钮可以移除该品牌。

## 实现方案

### 1. 自定义标签模板

使用 Element Plus 的 `#tag` 插槽来自定义选中项的显示：

```vue
<template #tag="{ option }">
  <el-tag 
    :key="option.value" 
    type="primary" 
    closable
    @close="handleRemoveBrand(option.value)"
    class="brand-tag"
  >
    {{ getUnionBrand(option.value) }}
  </el-tag>
</template>
```

**关键属性：**
- `closable`：显示关闭按钮
- `@close`：处理关闭事件
- `option.value`：当前选项的值（品牌ID）

### 2. 移除品牌处理函数

```javascript
const handleRemoveBrand = (brandId) => {
  console.log('移除品牌ID:', brandId)
  
  // 从当前项目的品牌列表中移除
  if (currentItem.value && currentItem.value.brand_list) {
    currentItem.value.brand_list = currentItem.value.brand_list.filter(item => item.id !== brandId)
    console.log('移除后的品牌列表:', currentItem.value.brand_list)
  }
}
```

**处理逻辑：**
1. 接收要移除的品牌ID
2. 从 `currentItem.value.brand_list` 中过滤掉该品牌
3. 更新数据并输出调试信息

### 3. 样式优化

```css
.brand-tag {
  margin: 2px;
  cursor: pointer;
}

.brand-tag:hover {
  opacity: 0.8;
}

.brand-tag .el-tag__close {
  color: #fff;
}

.brand-tag .el-tag__close:hover {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
}
```

**样式特点：**
- 标签间距适中
- 悬停效果
- 关闭按钮样式优化

## 使用方法

### 1. 选择品牌
在品牌下拉列表中选择多个品牌，每个选中的品牌会显示为一个蓝色标签。

### 2. 移除单个品牌
点击任意品牌标签右侧的 "×" 按钮，该品牌会从选中列表中移除。

### 3. 清除所有品牌
如果需要清除所有选中的品牌，可以：
- 方法1：逐个点击每个标签的关闭按钮
- 方法2：使用下拉框的清除按钮（如果启用了 `clearable`）

## 技术细节

### 1. 数据结构

```javascript
// 品牌选项数据
brandOptions.value = [
  { id: 1, union_brand: "品牌A" },
  { id: 2, union_brand: "品牌B" },
  { id: 3, union_brand: "品牌C" }
]

// 当前项目的品牌列表
currentItem.value.brand_list = [
  { id: 1 },
  { id: 3 }
]
```

### 2. 标签显示逻辑

```javascript
const getUnionBrand = (id) => {
  let brand = brandOptions.value.find(item => item.id === id)
  return brand ? brand.union_brand : '';
}
```

通过品牌ID查找对应的品牌名称进行显示。

### 3. 移除逻辑

```javascript
currentItem.value.brand_list = currentItem.value.brand_list.filter(item => item.id !== brandId)
```

使用 `filter` 方法移除指定ID的品牌。

## 优势

### 1. 用户体验
- **直观操作**：每个选中项都有明显的关闭按钮
- **即时反馈**：点击后立即移除，无需额外确认
- **视觉清晰**：标签样式美观，易于识别

### 2. 功能完整
- **保留原有功能**：不影响下拉选择和批量清除
- **单项控制**：可以精确控制每个选中项
- **数据同步**：移除操作会同步更新数据

### 3. 兼容性
- **Element Plus 兼容**：使用官方提供的插槽机制
- **响应式设计**：支持不同屏幕尺寸
- **浏览器兼容**：支持现代浏览器

## 测试场景

### 1. 基本功能测试
1. 选择多个品牌
2. 点击其中一个品牌的关闭按钮
3. 确认该品牌被移除，其他品牌保持选中

### 2. 边界情况测试
1. 只选择一个品牌，然后移除
2. 选择所有品牌，然后逐个移除
3. 在移除过程中继续添加新品牌

### 3. 交互测试
1. 鼠标悬停效果
2. 关闭按钮的点击区域
3. 键盘导航支持

## 注意事项

### 1. 数据一致性
确保移除操作后，`currentItem.value.brand_list` 的数据与界面显示保持一致。

### 2. 性能考虑
在品牌数量较多时，频繁的移除操作可能影响性能，建议添加防抖处理。

### 3. 用户提示
可以考虑添加移除确认提示，防止误操作。

## 扩展功能

### 1. 批量移除
可以添加全选/反选功能，支持批量移除多个品牌。

### 2. 拖拽排序
支持拖拽调整已选品牌的顺序。

### 3. 分组显示
当品牌数量较多时，可以按类别分组显示。
