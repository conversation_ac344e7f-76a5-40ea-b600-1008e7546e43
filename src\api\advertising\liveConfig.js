import request from '@/utils/request'

// 视频配置管理
const url = '/admin/advertise.RecLiveConfig/'

/**
 * 获取视频配置列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页条数
 * @param {string} [params.name] - 名称筛选
 * @returns {Promise} 返回列表数据
 */
export function getList(params) {
    return request({
        url: url + 'list',
        method: 'get',
        params
    })
}

/**
 * 保存视频配置（新增/编辑）
 * @param {Object} data - 视频配置数据
 * @param {string} data.name - 名称
 * @param {number} data.type - 类型(1:视频号直播 2:视频号视频 3:上传视频)
 * @param {string} [data.video_num_id] - 视频号ID
 * @param {string} [data.title] - 标题
 * @param {string} [data.video_id] - 视频ID
 * @param {string} [data.start_time] - 开始时间
 * @param {string} [data.end_time] - 结束时间
 * @param {number} data.state - 状态(1:有效 2:无效)
 * @returns {Promise} 返回保存结果
 */
export function save(data) {
    return request({
        url: url + 'save',
        method: 'post',
        data
    })
}

/**
 * 获取视频配置详情
 * @param {number} id - 配置ID
 * @returns {Promise} 返回配置详情
 */
export function getDetail(id) {
    return request({
        url: url + 'detail',
        method: 'get',
        params: { id }
    })
}

/**
 * 上传视频文件
 * @param {FormData} data - 包含视频文件的FormData对象
 * @returns {Promise} 返回上传结果
 */
export function uploadVideo(data) {
    return request({
        url: '/api/upload',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}