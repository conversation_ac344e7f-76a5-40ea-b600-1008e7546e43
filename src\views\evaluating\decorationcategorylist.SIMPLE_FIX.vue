<template>
  <div class="app-container">
    <div class="mb-4">
      <el-button type="primary" @click="handleCreate">新建</el-button>
    </div>

    <!-- 编辑弹窗 -->
    <el-dialog 
      :title="isCreateMode ? '新建分类' : '编辑分类'"
      v-model="editDialogVisible"
      width="50%"
      @close="handleDialogClose"
    >
      <el-form :model="form" label-width="120px">
        <el-form-item label="位置">
          <div class="form-item-container">
            <el-select 
              v-model="form.position_id" 
              placeholder="请选择位置"
              class="form-select"
              @change="handlePositionChange"
            >
              <el-option
                v-for="item in positions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <el-button type="primary" @click="showPositionDialog">添加</el-button>
          </div>
        </el-form-item>

        <el-form-item label="类型">
          <div class="form-item-container">
            <el-select 
              v-model="form.category_id" 
              placeholder="请选择类型"
              class="form-select"
              :disabled="!form.position_id"
              :loading="categoriesLoading"
              key="category-select"
            >
              <el-option
                v-for="item in categories"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <el-button type="primary" @click="showCategoryDialog">添加</el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </template>
    </el-dialog>

    <!-- 类型列表 -->
    <el-table v-loading="loading" :data="tableData" style="width: 100%; margin-top: 20px">
      <el-table-column prop="id" label="类型ID" width="100" />
      <el-table-column prop="position_name" label="位置" />
      <el-table-column prop="name" label="类型" />
      <el-table-column prop="update_time" label="更新时间" />
      <el-table-column prop="status" label="发布状态">
        <template #default="scope">
          <el-tag :type="scope.row.release_status == 2 ? 'success' : 'danger'">
            {{ scope.row.release_status == 2 ? '已发布' : '未发布' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180">
        <template #default="scope">
          <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button 
            size="small" 
            :type="scope.row.release_status == 2 ? 'danger' : 'success'"
            @click="handleRelease(scope.row)"
          >
            {{ scope.row.release_status == 2 ? '下架' : '发布' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 其他弹窗... -->
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getList, add, edit, del, updateReleaseStatus, getPositionList, getCategoryList } from '@/api/project/decorationcategory'

// 响应式数据
const form = ref({
  position_id: '',
  category_id: ''
})

const positions = ref([])
const categories = ref([])
const categoriesLoading = ref(false)
const editDialogVisible = ref(false)
const isCreateMode = ref(false)
const tableData = ref([])
const loading = ref(true)

// 简化的编辑函数
async function handleEdit(row) {
  console.log('=== 开始编辑 ===');
  console.log('编辑数据:', row);
  
  try {
    loading.value = true;
    isCreateMode.value = false;
    
    // 1. 先加载位置数据
    if (!positions.value.length) {
      console.log('加载位置数据...');
      await loadPositions();
    }
    
    // 2. 设置位置ID
    form.value.position_id = row.position_id;
    console.log('设置位置ID:', row.position_id);
    
    // 3. 加载类型数据
    if (row.position_id) {
      console.log('加载类型数据...');
      await loadCategories(row.position_id);
    }
    
    // 4. 等待DOM更新后设置类型ID
    await nextTick();
    form.value.category_id = row.category_id;
    console.log('设置类型ID:', row.category_id);
    
    // 5. 再次等待确保设置生效
    await nextTick();
    console.log('最终表单数据:', form.value);
    
    // 6. 显示弹窗
    editDialogVisible.value = true;
    
  } catch (error) {
    console.error('编辑失败:', error);
    ElMessage.error('数据加载失败');
  } finally {
    loading.value = false;
  }
}

// 位置改变处理
const handlePositionChange = async (positionId) => {
  console.log('位置改变:', positionId);
  
  // 只有在新建模式下才清空类型
  if (isCreateMode.value) {
    form.value.category_id = '';
  }
  
  if (positionId) {
    await loadCategories(positionId);
  } else {
    categories.value = [];
  }
}

// 加载位置数据
const loadPositions = async () => {
  try {
    const res = await getPositionList();
    positions.value = res.data || [];
    console.log('位置数据加载完成:', positions.value);
  } catch (error) {
    console.error('加载位置数据失败:', error);
    positions.value = [];
  }
}

// 加载类型数据
const loadCategories = async (positionId) => {
  categoriesLoading.value = true;
  try {
    const res = await getCategoryList({ position_id: positionId });
    categories.value = res.data || [];
    console.log('类型数据加载完成:', categories.value);
  } catch (error) {
    console.error('加载类型数据失败:', error);
    categories.value = [];
  } finally {
    categoriesLoading.value = false;
  }
}

// 新建处理
const handleCreate = async () => {
  form.value = { position_id: '', category_id: '' };
  categories.value = [];
  isCreateMode.value = true;
  
  await loadPositions();
  editDialogVisible.value = true;
}

// 取消处理
const handleCancel = () => {
  editDialogVisible.value = false;
  form.value = { position_id: '', category_id: '' };
  categories.value = [];
}

// 弹窗关闭处理
const handleDialogClose = () => {
  form.value = { position_id: '', category_id: '' };
  categories.value = [];
}

// 提交处理
const handleSubmit = async () => {
  if (!form.value.position_id || !form.value.category_id) {
    ElMessage.warning('请选择位置和分类');
    return;
  }
  
  try {
    loading.value = true;
    const apiCall = isCreateMode.value ? add : edit;
    await apiCall(form.value);
    
    ElMessage.success(isCreateMode.value ? '添加成功' : '编辑成功');
    editDialogVisible.value = false;
    await getListData();
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error('操作失败');
  } finally {
    loading.value = false;
  }
}

// 获取列表数据
const getListData = async () => {
  try {
    loading.value = true;
    const res = await getList({ page: 1, limit: 10 });
    tableData.value = res.data.list || [];
  } catch (error) {
    console.error('获取列表失败:', error);
  } finally {
    loading.value = false;
  }
}

// 发布/下架处理
const handleRelease = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定${row.release_status == 2 ? '下架' : '发布'}该分类吗？`, 
      '提示', 
      { type: 'warning' }
    );
    
    await updateReleaseStatus({
      id: row.id,
      release_status: row.release_status == 2 ? 1 : 2
    });
    
    ElMessage.success(`${row.release_status == 2 ? '下架' : '发布'}成功`);
    await getListData();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('操作失败:', error);
    }
  }
}

// 其他函数的简化实现...
const showPositionDialog = () => {
  ElMessage.info('位置添加功能');
}

const showCategoryDialog = () => {
  ElMessage.info('类型添加功能');
}

// 组件挂载
onMounted(() => {
  getListData();
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.form-item-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.form-select {
  flex: 1;
  min-width: 300px;
  margin-right: 10px;
}
</style>
