export const template =
  '<div class="header-box" style="position: relative;background: url(https://img.betterhousing.cn/production/simple/other/202505/29/DFAF82B806C3B56CE5831625ECE46D4A.png) no-repeat; background-size: 100% 100%;padding-bottom: 10px;">            <div style="padding: 0 16px;">                <img src="https://img.betterhousing.cn/production/simple/other/202505/29/8A867F67BA688455741BCE7CA048F40B.png" alt="" style="width: 100px;margin-top: 15px;">                <div style="display: flex;justify-content: space-between;margin-top: 10px;">                    <div>                        <img style="width: 220px;margin-top: 10px;" src="https://img.betterhousing.cn/storage/file/20240730/7a09bae21f2dc65da7743f266a0cbbb11812f64a.png" alt="">                        <div class="current-day" style="font-weight: bold;font-size: 20px;color: #FFFFFF;margin-top: 10px;" contenteditable="false">                            {$currtdate}</div>                    </div>                    <img style="width: 88px;margin-right: 2px;" src="https://img.betterhousing.cn/storage/file/20240725/d14892c12c085ac60cf73f0de9599820c80a0780.png" alt="">                </div>                <div style=" background: linear-gradient(200deg, #ffedde 1%, #ffffff 30%, #ffffff 100%);border-radius: 7px;margin-top: 18px;box-shadow: 2px -44px 60px 0px RGBA(202, 171, 98, 0.7);padding: 10px 14px 15px 14px;">                    <div style="display: flex;align-items: center;">                        <div style="width: 5px;height: 20px;background: #CAAB62;">                        </div>                        <div style="margin-left: 6px; font-size: 20px;color: #333333;font-weight: bold;" contenteditable="false">                            {$tradedate}北京住宅成交数据</div>                    </div>                    <div style="margin-top: 10px; height: 44px;background: #CAAB62;display: flex;align-items: center;                            font-weight: bold;font-size: 15px;color: #FFFFFF;text-align: center; border-top-left-radius: 5px;border-top-right-radius: 5px;">                        <div style="width: 20%;"></div>                        <div style="width: 40%;" contenteditable="false">当日成交</div>                        <div style="width: 40%;" contenteditable="false">{$month}月累计成交</div>                    </div>                    <div style="border-bottom: 1px solid#CAAB62;border-left: 1px solid #CAAB62;border-right: 1px solid #CAAB62;border-bottom-left-radius: 5px;border-bottom-right-radius: 5px;">                        <div style="display: flex;align-items: center;text-align: center; border-bottom: 1px solid #CAAB62;">                            <div style="width: 20%;height: 35px;line-height: 35px;font-size: 14px;border-right: 1px solid #CAAB62;" contenteditable="false">                                新房</div>                            <div style="width: 40%;height: 35px;font-size: 16px;font-weight: bold;line-height: 35px;border-right: 1px solid #CAAB62;" contenteditable="false">                                {$newhousenum}</div>                            <div style="width: 40%;height: 35px;font-size: 16px;font-weight: bold;line-height: 35px; " contenteditable="false">                                {$newcurrnum}</div>                        </div>                        <div style="display: flex;align-items: center;text-align: center; border-bottom: 1px solid #CAAB62;">                            <div style="width: 20%;height: 35px;line-height: 35px;font-size: 14px;border-right: 1px solid #CAAB62;" contenteditable="false">                                二手房</div>                            <div style="width: 40%;height: 35px;font-size: 16px;font-weight: bold;line-height: 35px;border-right: 1px solid #CAAB62;" contenteditable="false">                                {$secondhousenum}</div>                            <div style="width: 40%;height: 35px;font-size: 16px;font-weight: bold;line-height: 35px; " contenteditable="false">                                {$secondcurrnum}</div>                        </div>                        <div style="display: flex;align-items: center;text-align: center; ">                            <div style="width: 20%;height: 35px;line-height: 35px;font-size: 14px;border-right: 1px solid #CAAB62;" contenteditable="false">                                合计</div>                            <div style="width: 40%;height: 35px;font-size: 16px;font-weight: bold;line-height: 35px;border-right: 1px solid #CAAB62;" >                                <span style="color: #CAAB62;" contenteditable="false">{$newcount}</span></div>                            <div style="width: 40%;height: 35px;font-size: 16px;font-weight: bold;line-height: 35px; " contenteditable="false">                                <span style="color: #e03e2d;" contenteditable="false">{$secondcount}</span></div>                        </div>                    </div>                </div>            </div>        </div>        <div class="content" style="padding: 0 16px;margin-top: 8px;">            <div style="background: linear-gradient(200deg, #ffedde 1%, #ffffff 30%, #ffffff 100%);        border-radius: 7px;padding: 10px 14px 15px 14px;">                <div style="display: flex;align-items: center;">                    <div style="width: 5px;height: 20px;background: #CAAB62;">                    </div>                    <div style="margin-left: 6px; font-size: 20px;color: #333333;font-weight: bold;">                        最新取证</div>                </div>                <div style="border-radius: 5px;border: 1px solid #CAAB62;overflow: hidden;position: relative;font-size: 14px;margin-top: 10px;">                    <div style="position: absolute;width: 20%;height: 100%;left: 0;top: 0;background: #CAAB62;z-index: 1;">                    </div>                    <div style="display: grid; grid-template-columns: repeat(5, 20%);background: #FFF6ED;position: relative;">                        <div style="min-height: 35px;display: flex;align-items: center;justify-content: center;z-index: 20;color: #FFFFFF;font-weight: bold;padding: 5px 0;">                            类别                        </div>                        <div style="min-height: 35px;display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            期房                        </div>                        <div style="min-height: 35px;display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            期房                        </div>                        <div style="min-height: 35px;display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            期房                        </div>                        <div style="min-height: 35px;display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            新现房                        </div>                    </div>                    <div style="display: grid; grid-template-columns: repeat(5, 20%);font-weight: bold; border-bottom: 1px solid #FFDAB8;">                        <div style="color: #FFFFFF;font-weight: bold;z-index: 20;min-height: 35px; display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            区域                        </div>                        <div style="border-right: 1px solid #FFDAB8; min-height: 35px; display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            昌平区                        </div>                        <div style="border-right: 1px solid #FFDAB8;min-height: 35px; display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            密云区                        </div>                        <div style="border-right: 1px solid #FFDAB8;min-height: 35px;display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            大兴区                        </div>                        <div style="min-height: 35px;display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            房山区                        </div>                    </div>                    <div style="display: grid; grid-template-columns: repeat(5, 20%);font-weight: bold; border-bottom: 1px solid #FFDAB8;">                        <div style="color: #FFFFFF;font-weight: bold;z-index: 20;min-height: 35px; display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            楼盘名                        </div>                        <div style="border-right: 1px solid #FFDAB8; min-height: 35px; display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            昌平区<br>时尚阿<br>斯顿                        </div>                        <div style="border-right: 1px solid #FFDAB8;min-height: 35px; display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            期房                        </div>                        <div style="border-right: 1px solid #FFDAB8;min-height: 35px;display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            期房                        </div>                        <div style="min-height: 35px;display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            新现房                        </div>                    </div>                    <div style="display: grid; grid-template-columns: repeat(5, 20%);font-weight: bold; border-bottom: 1px solid #FFDAB8;">                        <div style="color: #FFFFFF;font-weight: bold;z-index: 20;min-height: 35px; display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            获证时间                        </div>                        <div style="border-right: 1px solid #FFDAB8; min-height: 35px; display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            7/19                        </div>                        <div style="border-right: 1px solid #FFDAB8;min-height: 35px; display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            7/19                        </div>                        <div style="border-right: 1px solid #FFDAB8;min-height: 35px;display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            7/19                        </div>                        <div style="min-height: 35px;display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            7/19                        </div>                    </div>                    <div style="display: grid; grid-template-columns: repeat(5, 20%);font-weight: bold; border-bottom: 1px solid #FFDAB8;">                        <div style="color: #FFFFFF;font-weight: bold;z-index: 20;min-height: 35px; display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            取证套数                        </div>                        <div style="border-right: 1px solid #FFDAB8; min-height: 35px; display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            416                        </div>                        <div style="border-right: 1px solid #FFDAB8;min-height: 35px; display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            416                        </div>                        <div style="border-right: 1px solid #FFDAB8;min-height: 35px;display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            416                        </div>                        <div style="min-height: 35px;display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            416                        </div>                    </div>                    <div style="display: grid; grid-template-columns: repeat(5, 20%);font-weight: bold; border-bottom: 1px solid #FFDAB8;">                        <div style="color: #FFFFFF;font-weight: bold;z-index: 20;min-height: 35px; display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            拟售均价<br>                            （元/m²）                        </div>                        <div style="border-right: 1px solid #FFDAB8; min-height: 35px; display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            55998                        </div>                        <div style="border-right: 1px solid #FFDAB8;min-height: 35px; display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            55998                        </div>                        <div style="border-right: 1px solid #FFDAB8;min-height: 35px;display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            55998                        </div>                        <div style="min-height: 35px;display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            55998                        </div>                    </div>                    <div style="display: grid; grid-template-columns: repeat(5, 20%);font-weight: bold; border-bottom: 1px solid #FFDAB8;font-size: 10px;font-weight: inherit;">                        <div style="color: #FFFFFF;font-weight: bold;z-index: 20;min-height: 35px; display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;font-size: 14px;">                            预售 <br>许可证号                        </div>                        <div style="border-right: 1px solid #FFDAB8; min-height: 35px; display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            京房售证字 <br>(2024)63号                        </div>                        <div style="border-right: 1px solid #FFDAB8;min-height: 35px; display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            京房售证字 <br>(2024)63号                        </div>                        <div style="border-right: 1px solid #FFDAB8;min-height: 35px;display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            京房售证字 <br>(2024)63号                        </div>                        <div style="min-height: 35px;display: flex;align-items: center;justify-content: center;text-align: center;padding: 5px 0;">                            京房售证字 <br>(2024)63号                            <br>(2024)63号                            <br>(2024)63号                            <br>(2024)63号                        </div>                    </div>                </div>            </div>            <div style="background: linear-gradient(200deg, #ffedde 1%, #ffffff 30%, #ffffff 100%);        border-radius: 7px;padding: 10px 14px 15px 14px;margin-top: 8px;">                <div style="display: flex;align-items: center;">                    <div style="width: 5px;height: 20px;background: #CAAB62;">                    </div>                    <div style="margin-left: 6px; font-size: 20px;color: #333333;font-weight: bold;">                        热点资讯</div>                </div>                <div style="margin-top:24px;">                    <div style="font-weight: bold;font-size: 16px;color: #333333;">· 北京下调房贷利率</div>                    <p style="font-size: 16px;color: #333333;line-height: 1.6;margin-top: 10px;">                        北京地区多家银行已开始下调房贷利率，首套房贷利率降至3.4%，二套房贷利率低至3.6%。                    </p>                </div>                <div style="margin-top:24px;">                    <div style="font-weight: bold;font-size: 16px;color: #333333;">· 大兴地块“底价成交”</div>                    <p style="font-size: 16px;color: #333333;line-height: 1.6;margin-top: 10px;">                        北京兴创置地公司底价摘得大兴区大兴新城核心区 DX00-0101-051、052 地块、黄村七街DX00-0201-0248、0255地块 R2                        二类居住用地，地价2.47万/㎡，销售指导价5.9万/㎡。                    </p>                </div>                <div style="margin-top:24px;">                    <div style="font-weight: bold;font-size: 16px;color: #333333;">· 非京籍可申请“南中轴共有产权房”</div>                    <p style="font-size: 16px;color: #333333;line-height: 1.6;margin-top: 10px;">                        南中轴共有产权房亦生共悦小区7月23日起开始申购，项目共计518套房源，房屋销售均价为29000元/平方米全装修交房，并且非京籍也可参与。                    </p>                </div>            </div>        </div>        <div style="display: flex;justify-content: center;margin-top: 18px; padding-bottom: 18px;">         <span style="font-size:12px;color:#999999">选好房·上芝麻</span>     </div>    '
