<template>
  <div class="page-contaniner">
    <!-- 搜索区域 -->
    <div class="search-box">
      <el-form :model="query" label-width="110px" inline>
        <el-form-item label="用户ID">
          <el-input
            v-model="query.member_id"
            placeholder="请输入用户ID"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="用户手机号">
          <el-input
            v-model="query.phone"
            placeholder="请输入用户手机号"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="顾问ID">
          <el-input
            v-model="query.zygw_id"
            placeholder="请输入顾问ID"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="顾问手机号">
          <el-input
            v-model="query.zygw_phone"
            placeholder="请输入顾问手机号"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="一级场景" prop="scene1">
          <el-select
            v-model="query.scene1"
            placeholder="请选择一级场景"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="(label, value) in scene1Map"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="二级场景" prop="scene2">
          <el-select
            v-model="query.scene2"
            placeholder="请选择二级场景"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="(label, value) in scene2Map"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否测试数据：" prop="is_test">
          <el-select v-model="query.is_test" style="width: 120px">
            <el-option value="-1" label="全部" />
            <el-option value="1" label="是" />
            <el-option value="0" label="否" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否录入EC：" prop="is_to_ec">
          <el-select v-model="query.is_to_ec" style="width: 120px">
            <el-option value="-1" label="全部" />
            <el-option value="1" label="是" />
            <el-option value="0" label="否" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="query.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button type="success" @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-box">
      <el-table :data="tableData.list" v-loading="tableData.loading">
        <!-- 用户信息 -->
        <el-table-column prop="member_id" label="用户ID" />
        <el-table-column prop="phone" label="用户手机号">
          <template #default="{ row }">
            <div>{{ row.phone || '--' }}</div>
            <div v-if="row.is_test === 1" class="member-name">({{ row.member_name || '--' }})</div>
          </template>
        </el-table-column>
        <!--        <el-table-column prop="phone" label="用户手机号" :formatter="defaultFormat()" />-->

        <!-- 顾问信息 -->
        <el-table-column prop="zygw_id" label="顾问ID" />
        <el-table-column prop="zygw_phone" label="顾问手机号" :formatter="defaultFormat()" />
        <el-table-column prop="zygw_name" label="顾问姓名" :formatter="defaultFormat()" />

        <!-- 楼盘信息 -->
        <el-table-column prop="house_id" label="楼盘ID" />
        <el-table-column prop="house_name" label="楼盘名称" :formatter="defaultFormat()" />

        <!-- 页面来源 -->
        <el-table-column label="页面来源" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            {{ sourceMap[row.view_source] || row.view_source || '--' }}
          </template>
        </el-table-column>

        <!-- 一级场景 -->
        <el-table-column label="一级场景" min-width="120">
          <template #default="{ row }">
            {{ scene1Map[row.scene1] || '--' }}
          </template>
        </el-table-column>

        <!-- 二级场景 -->
        <el-table-column label="二级场景" min-width="120">
          <template #default="{ row }">
            {{ scene2Map[row.scene2] || '--' }}
          </template>
        </el-table-column>

        <!-- 是否测试数据 -->
        <el-table-column label="是否测试数据" width="120">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_test"
              :active-value="1"
              :inactive-value="0"
              @change="handleTestChange(row)"
            />
          </template>
        </el-table-column>

        <!-- 是否录入EC -->
        <el-table-column label="是否录入EC" width="120">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_to_ec"
              :active-value="1"
              :inactive-value="0"
              @change="handleEcChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="remarks" label="备注" />
        <!-- 时间信息 -->
        <el-table-column prop="create_at" label="创建时间" />
      </el-table>

      <pagination
        v-show="tableData.total > 0"
        v-model:total="tableData.total"
        v-model:page="query.page"
        v-model:limit="query.limit"
        @pagination="featchList"
      />
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted, defineOptions } from 'vue'
import { getPageLimit } from '@/utils/settings'
import request from '@/utils/request'
import { defaultFormat } from '@/utils'
import { ElMessage } from 'element-plus'
import Pagination from '@/components/Pagination/index.vue'

defineOptions({
  name: 'Member'
})

// API URL
const url = '/admin/member.CallLog/'

// 字典数据
const sourceMap = ref({}) // 页面来源映射
const scene1Map = ref({}) // 一级场景映射
const scene2Map = ref({}) // 二级场景映射

// 获取字典数据
const getDict = async () => {
  try {
    const { data } = await request({
      url: url + 'dict',
      method: 'get'
    })

    if (data) {
      sourceMap.value = data.source_map || {}
      scene1Map.value = data.scene1_map || {}
      scene2Map.value = data.scene2_map || {}
    }
  } catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

// 封装的list请求方法
const getList = (params) => {
  return request({
    url: url + 'list',
    method: 'get',
    params: params
  })
}

const query = ref({
  page: 1,
  limit: getPageLimit(),
  member_id: '',
  phone: '',
  zygw_id: '',
  zygw_phone: '',
  scene1: '', // 新增一级场景查询
  scene2: '', // 新增二级场景查询
  dateRange: [],
  start_time: '',
  end_time: '',
  is_test: null, // 修改默认值为 null，便于清空选择
  is_to_ec: null, // 新增EC字段
  export: 0 // 添加导出标志
})

const tableData = reactive({
  total: 0,
  list: [],
  loading: false
})

// 处理测试数据状态变更
const handleTestChange = async (row) => {
  try {
    await request({
      url: url + 'change',
      method: 'post',
      data: {
        id: row.id,
        is_test: row.is_test
      }
    })
    ElMessage.success('更新成功')
  } catch (error) {
    row.is_test = !row.is_test // 恢复状态
    ElMessage.error('更新失败')
  }
}

const handleEcChange = async (row) => {
  try {
    await request({
      url: url + 'ecChange',
      method: 'post',
      data: {
        id: row.id,
        is_to_ec: row.is_to_ec
      }
    })
    ElMessage.success('更新成功')
  } catch (error) {
    row.is_to_ec = !row.is_to_ec // 恢复状态
    ElMessage.error('更新失败')
  }
}

const handleDateChange = (val) => {
  if (val) {
    query.value.start_time = val[0]
    query.value.end_time = val[1]
  } else {
    query.value.start_time = ''
    query.value.end_time = ''
  }
}

// 格式化场景信息显示
const formatSceneInfo = (scene1, scene2) => {
  if (!scene1 && !scene2) return '--'
  if (scene1 && scene2) return `${scene1} / ${scene2}`
  return scene1 || scene2 || '--'
}

const resetQuery = () => {
  query.value = {
    page: 1,
    limit: getPageLimit(),
    member_id: '',
    phone: '',
    zygw_id: '',
    zygw_phone: '',
    scene1: '', // 重置一级场景
    scene2: '', // 重置二级场景
    dateRange: [],
    start_time: '',
    end_time: '',
    is_test: null, // 重置为 null
    is_to_ec: null, // 重置EC字段
    export: 0 // 重置导出标志
  }
  featchList()
}

const featchList = async () => {
  tableData.loading = true
  try {
    const { data } = await getList(query.value)
    tableData.total = data.count
    tableData.list = data.list
  } finally {
    tableData.loading = false
  }
}

const handleSearch = () => {
  query.value.page = 1
  featchList()
}

// 处理导出功能
const handleExport = () => {
  // 显示加载提示
  ElMessage.info('正在准备导出数据，请稍候...')
  tableData.loading = true

  // 构建导出参数，与当前搜索条件一致
  const exportParams = {}

  // 将查询条件复制到导出参数
  Object.entries(query.value).forEach(([key, value]) => {
    // 跳过分页参数、dateRange和空值
    if (
      key !== 'page' &&
      key !== 'limit' &&
      key !== 'dateRange' &&
      value !== '' &&
      value !== null &&
      value !== undefined
    ) {
      exportParams[key] = value
    }
  })

  // 转换select选项，确保"-1"被视为有效值传递
  if (query.value.is_test === '-1' || query.value.is_test === -1) {
    exportParams.is_test = -1
  }

  if (query.value.is_to_ec === '-1' || query.value.is_to_ec === -1) {
    exportParams.is_to_ec = -1
  }

  // 确保日期参数正确传递
  if (query.value.start_time) {
    exportParams.start_time = query.value.start_time
  }
  if (query.value.end_time) {
    exportParams.end_time = query.value.end_time
  }

  // 设置导出标志
  exportParams.export = 1

  console.log('导出参数:', exportParams) // 调试用，可以在上线前删除

  // 请求导出
  request({
    url: url + 'list',
    method: 'get',
    params: exportParams,
    responseType: 'blob'
  })
    .then((blob) => {
      // 创建下载链接
      const downloadLink = document.createElement('a')
      downloadLink.href = URL.createObjectURL(blob)
      downloadLink.download =
        '通话记录_' + new Date().toISOString().slice(0, 10).replace(/-/g, '') + '.csv'

      // 添加到页面并触发点击
      document.body.appendChild(downloadLink)
      downloadLink.click()

      // 释放URL对象
      URL.revokeObjectURL(downloadLink.href)

      // 移除下载链接
      document.body.removeChild(downloadLink)

      // 提示成功
      ElMessage.success('导出成功')
    })
    .catch((error) => {
      console.error('导出失败:', error)
      ElMessage.error('导出失败，请稍后重试')
    })
    .finally(() => {
      tableData.loading = false
    })
}

onMounted(() => {
  getDict() // 先获取字典数据
  featchList() // 再获取列表数据
})
</script>

<style lang="scss" scoped>
.page-contaniner {
  min-width: 1200px;

  .search-box {
    background-color: #fff;
    padding: 20px;
    margin: 0 20px 20px;
    border-radius: 4px;
  }

  .table-box {
    padding: 10px;
    margin: 0 20px;
    background-color: #fff;
    border-radius: 4px;
  }
}

.page-contaniner {
  min-width: 1200px;

  .search-box {
    background-color: #fff;
    padding: 20px;
    margin: 0 20px 20px;
    border-radius: 4px;
  }

  .table-box {
    padding: 10px;
    margin: 0 20px;
    background-color: #fff;
    border-radius: 4px;
  }

  .member-name {
    color: #666;
    font-size: 13px;
    margin-top: 4px;
  }
}
</style>
