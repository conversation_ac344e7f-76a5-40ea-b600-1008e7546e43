<template>
  <div class="app-container">
    <el-form ref="queryRef" :model="query" :inline="true" label-width="90px">
      <el-form-item label="项目名称：" prop="name">
        <el-input
            v-model="query.name"
            maxlength="20"
            placeholder="请输入项目名称"
            style="width: 140px"
        />
      </el-form-item>

      <el-form-item label="评测数据：">
        <el-select v-model="query.evaluating" style="width: 120px">
          <el-option
              v-for="evalutingdict in evaluatingList"
              :key="evalutingdict.value"
              :label="evalutingdict.label"
              :value="evalutingdict.value"
          />
        </el-select>
        <span style="display: inline-block; width: 20px; text-align: center">-</span>
        <el-select v-model="query.price_state" style="width: 140px">
          <el-option
              v-for="pricestatedict in priceFlagList"
              :key="pricestatedict.value"
              :label="pricestatedict.label"
              :value="pricestatedict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发布状态：" prop="release_status">
        <el-select v-model="query.release_status" style="width: 120px">
          <el-option
              v-for="dict in releaseStatusList"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" title="搜索" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>

        <el-button @click="refresh">刷新列表</el-button>

        <excel-import title="导入" @on-import="imports" />
      </el-form-item>
    </el-form>
    <!-- 搜索end -->

    <!--  按钮 end-->
    <el-table ref="table" v-loading="loading" :data="data" :height="height">
      <el-table-column fixed :prop="idkey" label="ID" width="80" />
      <el-table-column prop="name" label="项目名称" min-width="150" show-overflow-tooltip />
      <el-table-column prop="housetype_state" label="户型评测" min-width="130" >
        <template #default="scope">
          <div @click="handleViewHouse(scope.row.id)">
            <el-text type="primary">{{ scope.row.housetype_state }}个户型</el-text>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="价格评测" align="center">
        <template #default="scope">
          <el-text v-if="scope.row.price_state == 0">未导入</el-text>
          <el-text v-else-if="scope.row.price_state == 1" type="success">已导入</el-text>
          <el-text v-else>-</el-text>
        </template>
      </el-table-column>
      <el-table-column label="CAD模型" align="center">
        <template #default="scope">
          <el-text v-if="scope.row.cad_status == 0">未导入</el-text>
          <el-text v-else-if="scope.row.cad_status == 1" type="success">已导入</el-text>
          <el-text v-else>-</el-text>
        </template>
      </el-table-column>
      <el-table-column label="噪声评测" align="center">
        <template #default="scope">
          <el-text v-if="scope.row.noise_state == 0">评测：未导入<br/></el-text>
          <el-text v-else-if="scope.row.noise_state == 1" type="success">评测：已导入<br/></el-text>

          <el-text v-if="scope.row.noise_db">评分：{{scope.row.noise_db}} <br/></el-text>
          <el-text v-else >评分：未评分 <br/></el-text>
          <el-text v-if="scope.row.noise_photo" type="success">图片：已上传</el-text>
          <el-text v-else>图片：未上传</el-text>

        </template>
      </el-table-column>
      <el-table-column label="景观评测" align="center">
        <template #default="scope">
          <el-text v-if="scope.row.landscape_state == 0">评测：未导入<br/></el-text>
          <el-text v-else type="success">评测：已导入<br/></el-text>

          <el-text v-if="scope.row.landscape_score" type="success">评分：{{scope.row.landscape_score}} <br/></el-text>
          <el-text v-else >评分：未评分 <br/></el-text>
          <el-text v-if="scope.row.landscape_photo" type="success">图片：已上传</el-text>
          <el-text v-else >图片：未上传</el-text>

        </template>
      </el-table-column>
      <el-table-column label="日照评测" align="center">
        <template #default="scope">
          <el-text v-if="scope.row.sunshine_state == 0">评测：未导入<br/></el-text>
          <el-text v-else type="success">评测：已导入<br/></el-text>

          <el-text v-if="scope.row.sunshine_hours" type="success">时长：{{scope.row.sunshine_hours}} <br/></el-text>
          <el-text v-else >时长：无数据 <br/></el-text>
          <el-text v-if="scope.row.sunshine_photo" type="success">图片：已上传</el-text>
          <el-text v-else >图片：未上传</el-text>

        </template>
      </el-table-column>
      <el-table-column prop="decoration_state" label="装修评测" >
        <template #default="scope">
          <div v-if="scope.row.is_land==0" @click="handleDecorationViewHouse(scope.row.id)">
            <el-text type="primary">{{ scope.row.decoration_state }}个装修</el-text>
          </div>
          <div v-else>
            <el-text>不支持</el-text>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="配套评测" align="center" show-overflow-tooltip>
        <template #default="scope">
          <div @click="handleViewSup(scope.row.id, scope.row.name)">
            <el-badge is-dot class="item" :offset="[5, 5]" :hidden="scope.row.is_new === 0">
              <el-link type="primary" v-if="scope.row.score !== '0.00'">{{
                  scope.row.score
                }}</el-link>
              <el-link type="primary" v-else>暂无</el-link>
            </el-badge>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="update_time" label="更新时间" min-width="130" />

      <el-table-column label="发布状态" align="center">
        <template #default="scope">
          <el-text v-if="scope.row.release_status == 0" type="danger">未完善</el-text>
          <el-text v-else-if="scope.row.release_status == 1" type="warning">待发布</el-text>
          <el-text v-else-if="scope.row.release_status == 2" type="success">已发布</el-text>
          <el-text v-else-if="scope.row.release_status == 3" type="danger">已下架</el-text>
          <el-text v-else>-</el-text>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="125">
        <template #default="scope">
          <el-link type="primary" class="mr-1" :underline="false" @click="editProject(scope.row)">
            编辑
          </el-link>
          <el-link
              v-if="
              scope.row.price_state == 1 &&
              checkPermission(['admin/evaluating.Evaluating/priceMarkChange'])
            "
              type="primary"
              class="mr-1"
              :underline="false"
              @click="cancelMarkFlag(scope.row)"
          >
            取消打标
          </el-link>

          <br/>
          <el-link
              v-if="
              scope.row.price_state == 1 &&
              checkPermission(['admin/evaluating.Evaluating/pcinfoExchage'])
            "
              type="primary"
              class="mr-1"
              :underline="false"
              @click="exChangeBuildUnitView(scope.row.id)"
          >
            同步三大评测
          </el-link>
          
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
        v-show="count > 0"
        v-model:total="count"
        v-model:page="query.page"
        v-model:limit="query.limit"
        @pagination="projectList"
    />
    <el-dialog
        v-model="dialog"
        :title="dialogTitle"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="cancel"
        width="80%"
        top="2vh"
        destroy-on-close
    >
      <el-tabs>
        <el-tab-pane
            v-if="checkPermission(['admin/evaluating.Evaluating/priceevaluationList'])"
            label="一房一价"
            lazy
        >
          <PriceParity :house="rowInfo" />
        </el-tab-pane>
        <el-tab-pane
            v-if="checkPermission(['admin/evaluating.Evaluating/getperpheryList'])"
            label="周边楼盘"
            lazy
        >
          <Periphery :house="rowInfo" />
        </el-tab-pane>
        <!--    <el-tab-pane
             v-if="checkPermission(['admin/evaluating.Evaluating/coordinateInfo'])"
             label="位置信息"
             lazy
           >
             <Coordinate :house="rowInfo" />
           </el-tab-pane> -->
        <el-tab-pane
            label="CAD模型"
            lazy
        >
          <iframe :src="createSign(rowInfo.id,rowInfo.name,'cadTransform')" frameborder="0" width="1200px" height="900px"></iframe>
        </el-tab-pane>
        <el-tab-pane
            data-url=""
            label="日照/噪声/景观评测"
            lazy
        >
          <iframe :src="createSign(rowInfo.id,rowInfo.name,'sunshine')" frameborder="0" width="1200px" height="900px"></iframe>
          <!-- <Noise :house="rowInfo" /> -->
        </el-tab-pane>

      </el-tabs>
    </el-dialog>

    <el-dialog
        v-model="markdialog"
        :title="markactionTitle"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="markdialogcancel"
        width="30%"
        top="5vh"
    >
      <span>确定取消该楼盘的价格评测标记吗？</span>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="markdialog = false">关闭</el-button>
          <el-button type="primary" @click="markSubmit"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import checkPermission from '@/utils/permission'
import PriceParity from './components/PriceParity.vue'
import Periphery from './components/Periphery.vue'
import Coordinate from './components/Coordinate.vue'
import Other from './components/Other.vue'
import screenHeight from '@/utils/screen-height'
import Pagination from '@/components/Pagination/index.vue'
import { getPageLimit } from '@/utils/settings'
import * as Project from '@/api/project/project'
import { priceMarkChange, supportBasicimports,exChangeBuildUnit } from '@/api/project/evaluating'
import { priceFlagList, evaluatingList, releaseStatusList } from '@/mapping/selectOption'
import ExcelImport from '@/components/ExcelImport/index.vue'
import CryptoJS, { MD5 } from 'crypto-js'
const router = useRouter()
const markdialog = ref(false)
const markactionTitle = ref('')
const makrsubmitID = ref(0)
const ThreeDmodelUrl = ref('')
const rowInfo = ref({})
const coordinateList = ref({})
const loading = ref(true)
const data = ref([])
const height = ref(680)
const query = ref({
  page: 1,
  limit: getPageLimit(),
  name: '', // 项目名称
  developBrand: [], // 开发品牌
  project_status: '', // 项目状态
  release_status: '', // 发布状态
  evaluating: '', // 发布状态
  price_state: '' // 发布状态
})
const count = ref(0)
const idkey = ref('id')

const dialog = ref(false)
const dialogTitle = ref('')
function projectList() {
  loading.value = true
  Project.projectList(query.value)
      .then((res) => {
        data.value = res.data.list
        count.value = res.data.count
        coordinateList.value = res.data.coordinateList
        loading.value = false
      })
      .catch((error) => {
        loading.value = false
      })
}
function editProject(row) {
  dialog.value = true
  dialogTitle.value = row.name + ' 详情：(' + row[idkey.value] + ')'
  rowInfo.value = row

  var id = {}
  id[idkey.value] = row[idkey.value]
}
function cancelMarkFlag(row) {
  markdialog.value = true
  markactionTitle.value = row.name
  makrsubmitID.value = row.id
}
function markdialogcancel(row) {
  markdialog.value = false
}
function markSubmit() {
  console.log(makrsubmitID.value)
  priceMarkChange({ projectID: makrsubmitID.value })
      .then((res) => {
        ElMessage.success(res.msg)
        markdialog.value = false
        projectList()
      })
      .catch((error) => {
        ElMessage.error(error)
        markdialog.value = false
      })
}

function cancel() {
  dialog.value = false
}

// 刷新
function refresh() {
  projectList()
}

onMounted(() => {
  height.value = screenHeight(310)
  projectList()
})

//搜索
const queryRef = ref(null)
function resetQuery() {
  query.value.price_state = ''
  query.value.evaluating = ''
  queryRef.value.resetFields()
  handleQuery()
}

function handleQuery() {
  query.value.pageNum = 1
  projectList()
}
const createSign = (id, projectName,type) => {
  projectName = encodeURIComponent(projectName)
  var currentHostname = window.location.hostname;  // 获取主机名称，不包括端口
  let signTimestamp = parseInt(new Date().getTime() / 1000);
  // 加密盐
  const mkey = 'CD8raeNb'
  var md5Str=  CryptoJS.MD5(`${mkey}${signTimestamp}`).toString();
  var urlPrex = "";
  if(currentHostname == import.meta.env.VITE_VISIT_DOMAIN || currentHostname == import.meta.env.VITE_VISIT_IP){
    urlPrex = import.meta.env.VITE_MAIDO_IFRAME_OUT_URL_PROD
  }else{
    urlPrex = import.meta.env.VITE_MAIDO_IFRAME_OUT_URL_DEV
  }

  if(type == "cadTransform"){
    urlPrex += `${type}/${id}?pName=${projectName}`
  }else{
    urlPrex += `evaluated/${id}?pName=${projectName}&type=${type}`
  }
  var jumpUrl = `${urlPrex}&uid=12&timestamp=${signTimestamp}&token=${md5Str}`
  console.log(jumpUrl)
  return jumpUrl
}
//搜索end

const handleViewSup = (id, projectName) => {
  router.push({
    path: '/evaluating/supportevaluation',
    query: { id, projectName }
  })
}
const handleViewHouse = (house_id) => {
  router.push({
    path: '/house/housetype',
    query: { house_id }
  })
}

const handleDecorationViewHouse = (house_id) => {
  router.push({
    path: '/evaluating/decorationlist',
    query: { house_id }
  })
}

const exChangeBuildUnitView = (house_id) => {
  exChangeBuildUnit({"house_id":house_id}).then(ElMessage.success("操作成功,请稍后查看"))
}

const imports = async ({ results }) => {
  const loadingInstance1 = ElLoading.service({ fullscreen: true, text: '上传中' })
  try {
    const res = await supportBasicimports({
      import: results
    })
    ElMessage.success(res.msg)
    refresh()
  } finally {
    loadingInstance1.close()
  }
  // supportBasicimports({
  //   import: results
  // })
  //   .then((res) => {
  //     ElMessage.success(res.msg)
  //     refresh()
  //   })
  //   .catch(() => {})
}

</script>
