<template>
  <div class="app-container">
 
    <!-- 搜索end -->

    <el-table ref="table" v-loading="loading" :data="data" :height="600">
      <el-table-column fixed prop="id" label="ID" width="80" />
      <el-table-column prop="member_id" label="会员id" min-width="150" show-overflow-tooltip />
      <el-table-column prop="identity_type" label="会员身份" min-width="150" show-overflow-tooltip />
      <el-table-column prop="sdk_version" label="小程序sdk版本" min-width="150" show-overflow-tooltip />
      <el-table-column prop="devicebrand" label="设备品牌" min-width="100"></el-table-column>
      <el-table-column prop="devicemodel" label="设备型号" min-width="100"></el-table-column>
      <el-table-column prop="devicesystem" label="操作系统" min-width="100"></el-table-column>
      <el-table-column prop="page_path" label="错误页面" min-width="100"></el-table-column>
     
      <el-table-column
        prop="create_time"
        label="上报时间"
        min-width="150"
       
        show-overflow-tooltip
      />
     
      <el-table-column fixed="right" label="操作" width="125">
        <template #default="scope">
          <el-link type="primary" class="mr-1" :underline="false" @click="handleEdit(scope.row)">
            查看
          </el-link>
        
         
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="count > 0"
      v-model:total="count"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="getList"
    />

<el-dialog
    v-model="visible"
    title="错误详情"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    custom-class="custom-dialog"
   

    width="900px"
    :z-index="1000"
    top="2vh"
  >
    <el-scrollbar native :height="600">
      <el-form class="form-box" ref="formRef"  :model="detailContent" label-width="120px">
        
       
        <el-form-item label="错误内容" prop="error_content">
          <el-text   clearable >{{ detailContent.error_content }}</el-text>
        </el-form-item>
 
      </el-form>
    </el-scrollbar>


  </el-dialog>
  </div>

  
</template>
<script setup>
import { getPageLimit } from '@/utils/settings'
import * as Report from '@/api/report/report'
import { customformat } from '@/utils/dateUtil'

import { ElMessageBox, ElMessage } from 'element-plus'
import { ElLoading } from 'element-plus'
import { ref } from 'vue'

const loading = ref(true)
const data = ref([])

const query = ref({
  page: 1,
  limit: getPageLimit(),
  name: '', 
  countyid: 0 
})

const count = ref(0)

const queryRef = ref(null)
const formRef = ref(null)

const visible = ref(false)

const currentID = ref('')

const imgUrl = ref([])

const detailContent = ref([])

const getList = async () => {
  loading.value = true
  try {
    const res = await Report.list(query.value)
    data.value = res.data.list
    count.value = res.data.count
  } finally {
    loading.value = false
  }
}

const resetQuery = () => {
  query.value.priceState = ''
  query.value.evaluating = ''
  queryRef.value.resetFields()
  handleQuery()
}

const handleQuery = () => {
  query.value.pageNum = 1
  getList()
}

const handleAdd = () => {
  visible.value = true
}

const handleEdit = (row) => {
  currentID.value = row.id
  visible.value = true,
  detailContent.value = row
}




onMounted(getList)
</script>

<style lang="scss" scoped></style>
