<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-form ref="queryRef" :model="query" :inline="true" label-width="90px">
      <el-form-item label="文章标题：" prop="title">
        <el-input
          v-model="query.title"
          maxlength="40"
          placeholder="请输入标题"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="栏目：" prop="category">
        <el-select v-model="query.category" placeholder="请选择栏目" style="width: 160px">
          <el-option
            v-for="item in categoryOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!-- 内容类型搜索 -->
      <el-form-item label="内容类型：" prop="content_type">
        <el-select v-model="query.content_type" placeholder="请选择内容类型" style="width: 160px">
          <el-option label="全部" value="" />
          <el-option
            v-for="(type, key) in contentTypes"
            :key="key"
            :label="type.label"
            :value="key"
          />
        </el-select>
      </el-form-item>
      <!-- 是否置顶搜索 -->
      <el-form-item label="是否置顶：" prop="is_top">
        <el-select v-model="query.is_top" placeholder="请选择" style="width: 160px">
          <el-option label="全部" value="" />
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="发布状态：" prop="status">
        <el-select v-model="query.status" placeholder="请选择" style="width: 160px">
          <el-option label="全部" value="-1" />
          <el-option label="已发布" :value="1" />
          <el-option label="未发布" :value="0" />
        </el-select>
      </el-form-item>
      <!-- 创建时间搜索 -->
      <el-form-item label="创建时间：" prop="create_time">
        <el-date-picker
          v-model="query.create_at"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          style="width: 300px"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
        <el-button type="success" @click="handleAdd">新增文章</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格部分保持不变 -->
    <el-table
      v-if="tableData.length >= 0"
      ref="tableRef"
      v-loading="loading"
      :data="tableData"
      style="width: 100%; margin-top: 20px"
      row-key="id"
    >
      <!-- 表格列定义保持不变 -->
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="title" label="文章标题" min-width="60" />
      <el-table-column prop="category" label="所属栏目" width="100">
        <template #default="{ row }">
          <el-tag>{{ getCategoryLabel(row.category) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="内容类型" width="120">
        <template #default="{ row }">
          <el-tag :type="getContentTypeTag(row.content_type)">
            {{ getContentTypeLabel(row.content_type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="关联楼盘" prop="house" width="200" />
      <el-table-column label="浏览量" prop="view" width="100" />
      <el-table-column label="发布状态" width="80">
        <template #default="{ row }">
          <el-tag :type="row.status ? 'danger' : 'info'">
            {{ row.status ? '已发布' : '未发布' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="create_at" label="创建时间" width="180" />
      <el-table-column prop="update_at" label="更新时间" width="180" />
      <el-table-column label="操作" width="220" fixed="right">
        <template #default="{ row }">
          <el-button
            link
            :type="row.status ? 'danger' : 'success'"
            @click="handleStatusChange(row)"
          >
            {{ row.status ? '下架' : '发布' }}
          </el-button>
          <el-button link type="primary" @click="handleEdit(row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      v-model:total="total"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="getList"
    />

    <!-- 弹窗表单 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :append-to-body="true"
      destroy-on-close
      class="article-dialog"
      @close="handleDialogClose"
    >
      <el-form
        v-if="dialogVisible"
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        class="article-form"
      >
        <el-card class="box-card" shadow="never">
          <template #header>
            <div class="card-header">基础信息</div>
          </template>

          <el-form-item label="标题" prop="title">
            <el-input
              v-model="form.title"
              maxlength="40"
              show-word-limit
              placeholder="请输入标题"
            />
          </el-form-item>

          <el-form-item label="所属栏目" prop="category">
            <el-select v-model="form.category" placeholder="请选择栏目" class="w-full">
              <el-option
                v-for="item in categoryOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="作者" prop="author">
            <el-input
              v-model="form.author"
              maxlength="10"
              show-word-limit
              placeholder="请输入作者"
            />
          </el-form-item>

          <el-form-item label="内容类型" prop="content_type">
            <el-radio-group v-model="form.content_type" @change="handleContentTypeChange">
              <el-radio label="original">原创文章</el-radio>
              <el-radio label="public">公众号文章</el-radio>
              <el-radio label="video">视频号视频</el-radio>
              <el-radio label="upload">上传视频</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否添加水印" prop="is_watermark">
            <el-radio-group v-model="form.is_watermark" style="margin-left: 10px">
              <el-radio label="否" :value="0"></el-radio>
              <el-radio label="是" :value="1"></el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 根据内容类型显示不同的表单项 -->
          <template v-if="['original', 'public'].includes(form.content_type)">
            <el-form-item label="资讯头图" prop="image_url">
              <ImgUploadsHouseNews
                source="3"
                :isWatermark="form.is_watermark"
                :key="`image-${componentKey}-${uploadIds.image}`"
                v-model="form.image_urls"
                :upload-btn="form.image_urls.length ? '' : '上传封面'"
                file-type="image"
                :height="100"
                :table-id="uploadIds.image"
                singleUpload
                @update:model-value="handleImageChange"
              />
            </el-form-item>

            <template v-if="form.content_type === 'original'">
              <el-form-item label="文章内容" prop="content">
                <Editor
                  ref="editorRef"
                  v-model="form.content"
                  :init="tinymceInit"
                  class="tinymce-editor"
                />
              </el-form-item>

              <el-form-item label="关联楼盘" prop="house_ids">
                <el-select
                  v-model="form.house_ids"
                  multiple
                  filterable
                  remote
                  reserve-keyword
                  :max-collapse-tags="3"
                  :placeholder="
                    form.category === 'house_note'
                      ? '请输入楼盘名称搜索（必选且只能选择一个）'
                      : '请输入楼盘名称搜索（可多选，最多10个）'
                  "
                  :remote-method="getHouseList"
                  :loading="houseLoading"
                  style="width: 100%"
                  collapse-tags
                  collapse-tags-tooltip
                  :max="form.category === 'house_note' ? 1 : 10"
                >
                  <template #prefix>
                    <span class="selected-count" style="color: #909399; margin-right: 8px">
                      {{ form.house_ids.length }}/{{ form.category === 'house_note' ? '1' : '10' }}
                    </span>
                  </template>
                  <el-option
                    v-for="item in houseOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                    :disabled="form.house_ids.length >= 10 && !form.house_ids.includes(item.id)"
                  >
                    <div class="house-option">
                      <span class="house-name">{{ item.name }}</span>
                      <span class="house-info">
                        {{ item.region_name }} | {{ item.sector_name }}
                      </span>
                    </div>
                  </el-option>
                </el-select>
                <!-- 显示已选择的楼盘详情 -->
                <div v-if="selectedHouses.length" class="selected-houses">
                  <div v-for="house in selectedHouses" :key="house.id" class="selected-house-item">
                    <span class="house-name">{{ house.name }}</span>
                    <span class="house-location"
                      >{{ house.region_name }} {{ house.sector_name }}</span
                    >
                  </div>
                </div>
              </el-form-item>
            </template>

            <template v-if="form.content_type === 'public'">
              <el-form-item label="公众号链接" prop="public_link">
                <el-input v-model="form.public_link" placeholder="请输入公众号文章链接" />
              </el-form-item>
            </template>
          </template>

          <!-- 视频相关字段 -->
          <template v-if="['video', 'upload'].includes(form.content_type)">
            <!-- 只在上传视频类型时显示视频标题和封面图 -->
            <template v-if="form.content_type === 'upload'">
              <el-form-item label="视频标题" prop="video_title">
                <el-input v-model="form.video_title" placeholder="请输入视频标题" />
              </el-form-item>

              <el-form-item label="视频封面图" prop="video_cover_url">
                <ImgUploadsHouseNews
                  source="3"
                  :isWatermark="form.is_watermark"
                  :key="`video-cover-${componentKey}-${uploadIds.image}`"
                  v-model="form.video_cover_urls"
                  :upload-btn="form.video_cover_urls.length ? '' : '上传视频封面'"
                  file-type="image"
                  :height="100"
                  singleUpload
                  :table-id="uploadIds.videoCover"
                  @update:model-value="handleVideoCoverChange"
                />
              </el-form-item>

              <el-form-item label="上传视频（最大10M内）" prop="video_url">
                <ImgUploadsHouseNews
                  source="3"
                  :isWatermark="form.is_watermark"
                  :key="`video-${componentKey}-${uploadIds.image}`"
                  v-model="form.video_urls"
                  :upload-btn="form.video_urls.length ? '' : '上传视频'"
                  file-type="video"
                  :height="100"
                  singleUpload
                  :table-id="uploadIds.video"
                  @update:model-value="handleVideoChange"
                />
              </el-form-item>
            </template>

            <!-- 视频号视频专属字段 -->
            <template v-if="form.content_type === 'video'">
              <el-form-item label="视频号ID" prop="video_account_id">
                <el-input v-model="form.video_account_id" placeholder="请输入视频号ID" />
              </el-form-item>

              <el-form-item label="是否为同主体" prop="is_same_entity">
                <el-radio-group v-model="form.is_same_entity">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="视频ID" prop="video_id">
                <el-input
                  v-model="form.video_id"
                  placeholder="请输入视频ID或feed-token（非同主体）"
                />
              </el-form-item>

              <el-form-item label="关联主播" prop="identity_id">
                <el-select
                  v-model="form.identity_id"
                  filterable
                  remote
                  reserve-keyword
                  placeholder="请输入主播名称搜索"
                  :remote-method="getXfsList"
                  :loading="xfsLoading"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in xfsOptions"
                    :key="item.id"
                    :label="formatXfsLabel(item)"
                    :value="item.id"
                  >
                    <span>{{ formatXfsLabel(item) }}</span>
                  </el-option>
                </el-select>
                <!-- 修改已选择主播的显示 -->
                <div v-if="selectedXfs" class="selected-xfs-info mt-2 text-gray-500 text-sm">
                  已选择: {{ formatXfsLabel(selectedXfs) }}
                </div>
              </el-form-item>
            </template>
          </template>
          <el-form-item label="是否原创">
            <el-switch v-model="form.is_original" :active-value="1" :inactive-value="0" />
          </el-form-item>
          <el-form-item label="设为置顶">
            <el-switch
              v-model="form.is_top"
              :active-value="1"
              :inactive-value="0"
              @change="handleIsTopChange"
            />
          </el-form-item>

          <el-form-item v-if="form.is_top" label="置顶背景图" prop="top_background_url">
            <ImgUploadsHouseNews
              :key="`top-bg-${componentKey}-${uploadIds.image}`"
              v-model="form.top_background_urls"
              :upload-btn="form.top_background_urls.length ? '' : '上传置顶背景图'"
              file-type="image"
              :height="100"
              singleUpload
              :table-id="uploadIds.topBackground"
              @update:model-value="handleTopBackgroundChange"
            />
          </el-form-item>
        </el-card>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogClose">取 消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
            确 定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Editor from '@tinymce/tinymce-vue'
import request from '@/utils/request'
import ImgUploadsHouseNews from '@/components/FileManage/ImgUploadsHouseNews.vue'
import { tinymceInit } from '@/views/info/components/info_tinymceInit.js'
import Pagination from '@/components/Pagination/index.vue'

// 定义所有需要的响应式变量
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('添加')
const formRef = ref(null)

// 新增主播相关的响应式变量
const xfsLoading = ref(false)
const xfsOptions = ref([])
const selectedXfs = ref(null)
// 格式化主播显示标签
const formatXfsLabel = (xfs) => {
  if (!xfs) return ''
  const phone = xfs.service_phone || ''
  const lastFourDigits = phone.slice(-4)
  return `${xfs.service_name} ${lastFourDigits}`
}

// 获取主播列表
const getXfsList = async (query) => {
  if (!query) {
    xfsOptions.value = selectedXfs.value ? [selectedXfs.value] : []
    return
  }

  xfsLoading.value = true
  try {
    const res = await request({
      url: '/admin/info.Information/xfsList',
      method: 'get',
      params: { name: query }
    })

    if (res.code === 200 && res.data) {
      xfsOptions.value = res.data
    }
  } catch (error) {
    console.error('获取主播列表失败:', error)
    ElMessage.error('获取主播列表失败')
  } finally {
    xfsLoading.value = false
  }
}


// API
const api = {
  list: (params) => request.get('/admin/info.Information/list', { params }),
  detail: (id) => request.get(`/admin/info.Information/info?id=${id}`),
  update: (data) => request.post('/admin/info.Information/save', data),
  delete: (id) => request.delete(`/admin/info.Information/del?id=${id}`),
  changeStatus: (id, status) =>
    request.get(`/admin/info.Information/changeStatus`, { params: { id, status } })
}
const componentKey = ref(0)
// 上传组件ID管理
const uploadInstanceId = ref(1)
const resetUploadInstanceId = () => {
  uploadInstanceId.value++
}

// 使用ref存储上传组件的IDs
const uploadIds = ref({
  image: generateUploadId(),
  videoCover: generateUploadId(),
  video: generateUploadId(),
  topBackground: generateUploadId()
})

// 生成唯一ID的方法
function generateUploadId() {
  return `upload-${Date.now()}-${Math.floor(Math.random() * 1000000)}`
}

// 重置所有上传组件ID的方法
const resetUploadIds = () => {
  uploadIds.value = {
    image: generateUploadId(),
    videoCover: generateUploadId(),
    video: generateUploadId(),
    topBackground: generateUploadId()
  }
}

// 配置
const categoryOptions = [
  { label: '好房子', value: 'good_house' },
  { label: '楼盘评测', value: 'house_review' },
  { label: '土拍速报', value: 'land_news' },
  { label: '市场走势', value: 'market_trend' },
  { label: '政策解读', value: 'policy_analysis' },
  { label: '城市动态', value: 'city_news' },
  { label: '买房攻略', value: 'buying_guide' },
  { label: '楼盘动态', value: 'property_news' },
  { label: '楼盘手记', value: 'house_note' }
]

const contentTypes = {
  original: { label: '原创文章', type: 'success' },
  public: { label: '公众号文章', type: 'warning' },
  video: { label: '视频号', type: 'info' },
  upload: { label: '上传视频', type: 'primary' }
}

// 状态
const submitLoading = ref(false)
const tableData = ref([])
const total = ref(0)

const queryRef = ref(null)

// 查询参数
// 修改查询参数，添加新字段
const query = ref({
  page: 1,
  limit: 10,
  title: '',
  category: '',
  content_type: '', // 新增内容类型查询
  is_top: '', // 新增是否置顶查询
  create_at: [] // 新增创建时间查询
})

// 新增楼盘相关的响应式变量
const houseLoading = ref(false)
const houseOptions = ref([])
const selectedHouseMap = ref(new Map()) // 用于存储已选择的楼盘详细信息

// 计算已选择的楼盘列表
const selectedHouses = computed(() => {
  return Array.from(selectedHouseMap.value.values())
})

// 表单数据
const initForm = {
  title: '',
  category: '',
  author: '',
  content_type: 'original',
  image_urls: [],
  image_url: '',
  content: '',
  public_link: '',
  video_account_id: '', // 新增：视频号ID
  is_same_entity: 1, // 新增：是否为同主体，默认为是
  video_id: '', // 新增：视频ID
  video_title: '',
  video_urls: [],
  video_url: '',
  video_cover_urls: [],
  video_cover_url: '',
  is_top: 0,
  top_background_urls: [],
  top_background_url: '',
  house_ids: [],
  identity_id: '', // 修改主播ID字段名
  is_original: 0, // Add this line for the new field
  is_watermark: 0
}

const form = ref({ ...initForm })

// 校验规则
// 校验规则
const rules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
    { max: 40, message: '标题不能超过40个字符', trigger: 'blur' }
  ],
  category: [{ required: true, message: '请选择栏目', trigger: 'change' }],
  // author: [
  //   {required: true, message: '请输入作者', trigger: 'blur'},
  //   {max: 10, message: '作者不能超过10个字符', trigger: 'blur'}
  // ],
  content: [{ required: true, message: '请输入文章内容', trigger: 'blur' }],
  house_ids: [
    {
      validator: (rule, value, callback) => {
        if (form.value.category === 'house_note') {
          // For 楼盘手记, exactly one house must be selected
          if (!value || value.length !== 1) {
            callback(new Error('楼盘手记必须且只能关联一个楼盘'))
          } else {
            callback()
          }
        } else {
          // For other categories, maximum 10 houses can be selected
          if (value && value.length > 10) {
            callback(new Error('最多只能选择10个楼盘'))
          } else {
            callback()
          }
        }
      },
      trigger: 'change'
    }
  ],
  public_link: [
    { required: true, message: '请输入公众号文章链接', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (form.value.content_type === 'public') {
          if (!value) {
            callback(new Error('请输入公众号文章链接'))
          } else if (!/^https?:\/\/.+/.test(value)) {
            callback(new Error('链接必须以http://或https://开始'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: ['blur', 'change']
    }
  ],
  top_background_url: [
    {
      required: true,
      message: '请上传置顶背景图',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (form.value.is_top === 1 && !value) {
          callback(new Error('请上传置顶背景图'))
        } else {
          callback()
        }
      }
    }
  ],
  video_title: [
    {
      required: true,
      message: '请输入视频标题',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (form.value.content_type === 'upload' && !value) {
          callback(new Error('请输入视频标题'))
        } else {
          callback()
        }
      }
    }
  ],
  // video_cover_url: [
  //   {
  //     required: true,
  //     message: '请上传视频封面图',
  //     trigger: 'change',
  //     validator: (rule, value, callback) => {
  //       if (form.value.content_type === 'upload' && !value) {
  //         callback(new Error('请上传视频封面图'))
  //       } else {
  //         callback()
  //       }
  //     }
  //   }
  // ],
  video_url: [
    {
      required: true,
      message: '请上传视频',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (form.value.content_type === 'upload' && !value) {
          callback(new Error('请上传视频'))
        } else {
          callback()
        }
      }
    }
  ],
  video_account_id: [
    {
      required: true,
      message: '请输入视频号ID',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (form.value.content_type === 'video' && !value) {
          callback(new Error('请输入视频号ID'))
        } else {
          callback()
        }
      }
    }
  ],
  is_same_entity: [
    {
      required: true,
      message: '请选择是否为同主体',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (form.value.content_type === 'video' && value === undefined) {
          callback(new Error('请选择是否为同主体'))
        } else {
          callback()
        }
      }
    }
  ],
  video_id: [
    {
      required: true,
      message: '请输入视频ID',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (form.value.content_type === 'video' && !value) {
          callback(new Error('请输入视频ID'))
        } else {
          callback()
        }
      }
    }
  ]
}

const handleStatusChange = async (row) => {
  try {
    const newStatus = row.status ? 0 : 1
    const actionText = newStatus ? '发布' : '下架'

    await ElMessageBox.confirm(`确定要${actionText}文章"${row.title}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const res = await api.changeStatus(row.id, newStatus)

    if (res.code === 200) {
      ElMessage.success(`${actionText}成功`)
      getList() // Refresh the list after status change
    } else {
      ElMessage.error(res.msg || `${actionText}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('状态更改失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 修改处理文件变化的方法
const handleImageChange = (files) => {
  form.value.image_urls = files
  form.value.image_url = files.length ? files[files.length - 1].file_url : ''
}

// 获取主播详情的方法使用 id 参数
const getXfsDetail = async (id) => {
  if (!id) return null

  try {
    const res = await request({
      url: '/admin/info.Information/xfsList',
      method: 'get',
      params: { id } // 使用 id 参数直接获取特定主播信息
    })

    if (res.code === 200 && res.data && res.data.length > 0) {
      return res.data[0]
    }
    return null
  } catch (error) {
    console.error('获取主播详情失败:', error)
    return null
  }
}

// 选择主播时的处理
const handleXfsSelect = (value) => {
  if (!value) {
    selectedXfs.value = null
    return
  }

  // 从现有选项中查找
  const selected = xfsOptions.value.find((item) => item.id === value)
  if (selected) {
    selectedXfs.value = selected
  } else {
    // 如果没有找到（比如刷新页面后），重新获取详情
    getXfsDetail(value).then((detail) => {
      if (detail) {
        selectedXfs.value = detail
        xfsOptions.value = [detail]
      }
    })
  }
}

// 处理置顶背景图变化
const handleTopBackgroundChange = (files) => {
  form.value.top_background_urls = files
  form.value.top_background_url = files.length ? files[files.length - 1].file_url : ''
}

// 处理是否置顶变化
const handleIsTopChange = (value) => {
  componentKey.value++
  if (value === 0) {
    form.value.top_background_urls = []
    form.value.top_background_url = ''
  }
}

watch(
  () => form.value.category,
  (newCategory) => {
    // Clear house selections when switching to/from house_note category
    if (newCategory === 'house_note') {
      // If switching to house_note and multiple houses are selected, keep only the first one
      if (form.value.house_ids.length > 1) {
        const firstHouseId = form.value.house_ids[0]
        form.value.house_ids = [firstHouseId]
        // Update selectedHouseMap to keep only the first house
        const firstHouse = selectedHouseMap.value.get(firstHouseId)
        selectedHouseMap.value.clear()
        if (firstHouse) {
          selectedHouseMap.value.set(firstHouseId, firstHouse)
        }
      }
    }
    // Revalidate the form field
    if (formRef.value) {
      formRef.value.validateField('house_ids')
    }
  }
)

watch(dialogVisible, (newVal) => {
  if (!newVal) {
    resetUploadIds()
  }
})

const getHouseList = async (query) => {
  if (query === '') {
    // 当搜索词为空时，只显示已选择的楼盘
    houseOptions.value = Array.from(selectedHouseMap.value.values())
    return
  }

  houseLoading.value = true
  try {
    const params = {
      name: query,
      page: 1,
      limit: 20
    }

    // 如果是单个ID查询
    if (/^\d+$/.test(query)) {
      params.id = query
      delete params.name
    } else {
      // 添加时需要传入 release_status=2
      if (!form.value.id) {
        params.release_status = 2
      }
    }

    const res = await request({
      url: '/admin/info.Information/searchHouse',
      method: 'get',
      params
    })

    if (res.code === 200 && res.data.list) {
      const searchResults = res.data.list
      // 合并搜索结果和已选择的楼盘
      const selectedHouses = Array.from(selectedHouseMap.value.values())

      // 确保已选楼盘始终在列表中
      houseOptions.value = [
        ...searchResults,
        ...selectedHouses.filter(
          (selected) => !searchResults.some((house) => house.id === selected.id)
        )
      ]
    }
  } catch (error) {
    console.error('获取楼盘列表失败:', error)
    ElMessage.error('获取楼盘列表失败')
  } finally {
    houseLoading.value = false
  }
}

// 监听内容类型变化，动态设置验证规则
watch(
  () => form.value.content_type,
  () => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  }
)

// 监听置顶状态变化，动态设置验证规则
watch(
  () => form.value.is_top,
  () => {
    if (formRef.value) {
      formRef.value.clearValidate(['top_background_url'])
    }
  }
)

// 监听楼盘选择变化
watch(
  () => form.value.house_ids,
  (newIds = []) => {
    if (form.value.category === 'house_note') {
      // For house_note category, enforce single selection
      if (newIds.length > 1) {
        ElMessage.warning('楼盘手记只能关联一个楼盘')
        form.value.house_ids = [newIds[newIds.length - 1]] // Keep only the latest selection
      }
    } else {
      // For other categories, maintain the 10-house limit
      if (newIds.length > 10) {
        ElMessage.warning('最多只能选择10个楼盘')
        form.value.house_ids = newIds.slice(0, 10)
        return
      }
    }

    // Update selectedHouseMap as before
    const currentOptions = houseOptions.value
    selectedHouseMap.value.clear()
    currentOptions.forEach((house) => {
      if (form.value.house_ids.includes(house.id)) {
        selectedHouseMap.value.set(house.id, house)
      }
    })
  },
  { deep: true }
)

// 监听 is_top 变化，动态设置 top_background_url 的验证规则
watch(
  () => [form.value.image_url, form.value.video_cover_url, form.value.video_url],
  ([newImage, newVideoCover, newVideo]) => {
    if (Array.isArray(newImage) && newImage.length > 0) {
      form.value.image_url = newImage[newImage.length - 1]
    }
    if (Array.isArray(newVideoCover) && newVideoCover.length > 0) {
      form.value.video_cover_url = newVideoCover[newVideoCover.length - 1]
    }
    if (Array.isArray(newVideo) && newVideo.length > 0) {
      form.value.video_url = newVideo[newVideo.length - 1]
    }
  },
  { deep: true }
)

// 处理视频封面图变化
const handleVideoCoverChange = (files) => {
  form.value.video_cover_urls = files
  form.value.video_cover_url = files.length ? files[files.length - 1].file_url : ''
}

// 处理视频变化
const handleVideoChange = (files) => {
  form.value.video_urls = files
  form.value.video_url = files.length ? files[files.length - 1].file_url : ''
}

// 内容类型变化处理
// 内容类型变化处理
const handleContentTypeChange = () => {
  componentKey.value++
}

// 修改获取列表方法，处理时间范围
const getList = async () => {
  loading.value = true
  try {
    // 处理查询参数
    const params = { ...query.value }
    if (params.create_time?.length === 2) {
      params.start_time = params.create_time[0]
      params.end_time = params.create_time[1]
    }
    delete params.create_time

    const res = await api.list(params)
    if (res.code === 200) {
      // 确保 tableData 始终是数组
      tableData.value = Array.isArray(res.data.list)
        ? res.data.list.map((item) => ({
            ...item,
            create_at: item.create_at || item.create_time,
            houses: Array.isArray(item.houses) ? item.houses : []
          }))
        : []
      total.value = res.data.count || 0
    } else {
      ElMessage.error(res.msg || '获取数据失败')
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取列表失败:', error)
    ElMessage.error('获取列表失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleQuery = async () => {
  query.value.page = 1
  await getList()
  // 重置后确保表格重新渲染
  await nextTick()
}

// 重置查询方法更新
const resetQuery = () => {
  queryRef.value?.resetFields()
  query.value = {
    page: 1,
    limit: 10,
    title: '',
    category: '',
    content_type: '',
    is_top: '',
    create_at: []
  }
  getList()
}

// 添加方法
const handleAdd = async () => {
  // 重置所有状态
  await handleDialogClose()

  // 使用 nextTick 确保状态已完全重置
  await nextTick()

  // 设置标题并打开对话框
  dialogTitle.value = '添加'
  dialogVisible.value = true
}

watch(dialogVisible, async (newVal) => {
  if (!newVal) {
    // 当对话框关闭时，确保完全重置状态
    await handleDialogClose()
  }
})

// 修改编辑方法
const handleEdit = async (row) => {
  resetUploadIds()
  dialogTitle.value = '编辑'
  try {
    const res = await request({
      url: `/admin/info.Information/info?id=${row.id}`,
      method: 'get'
    })

    if (res.code === 200) {
      const data = res.data

      // 如果有关联主播ID，先获取主播详情
      if (data.identity_id) {
        const xfsDetail = await getXfsDetail(data.identity_id)
        if (xfsDetail) {
          selectedXfs.value = xfsDetail
          xfsOptions.value = [xfsDetail]
        }
      }

      // 构造上传组件需要的数据结构
      const imageUrls = data.image_url
        ? [
            {
              file_url: data.image_url,
              name: getFileNameFromUrl(data.image_url)
            }
          ]
        : []

      const videoCoverUrls = data.video_cover_url
        ? [
            {
              file_url: data.video_cover_url,
              name: getFileNameFromUrl(data.video_cover_url)
            }
          ]
        : []

      const videoUrls = data.video_url
        ? [
            {
              file_url: data.video_url,
              name: getFileNameFromUrl(data.video_url)
            }
          ]
        : []

      const topBackgroundUrls = data.top_background_url
        ? [
            {
              file_url: data.top_background_url,
              name: getFileNameFromUrl(data.top_background_url)
            }
          ]
        : []

      // 处理楼盘IDs
      let houseIds = []
      if (data.house_ids) {
        try {
          houseIds = JSON.parse(data.house_ids)
          // 如果解析成功且有关联楼盘，使用所有ID查询楼盘信息
          if (houseIds.length > 0) {
            const houseRes = await request({
              url: '/admin/house.Index/index',
              method: 'get',
              params: {
                id_arr: houseIds.join(',') // 将所有ID用逗号连接传递给接口
              }
            })

            if (houseRes.code === 200 && houseRes.data.list) {
              // 更新所有楼盘的选项和选中状态
              const houses = houseRes.data.list
              houses.forEach((house) => {
                selectedHouseMap.value.set(house.id, house)
              })
              houseOptions.value = houses
            }
          }
        } catch (e) {
          houseIds = []
          console.error('解析楼盘IDs失败:', e)
        }
      }

      form.value = {
        ...initForm,
        ...data,
        // 添加转换后的数据
        image_urls: imageUrls,
        video_cover_urls: videoCoverUrls,
        video_urls: videoUrls,
        top_background_urls: topBackgroundUrls,
        is_top: Number(data.is_top),
        identity_id: data.identity_id || '',
        house_ids: houseIds
      }

      dialogVisible.value = true
    } else {
      ElMessage.error(res.msg || '获取详情失败')
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  }
}

// 添加一个辅助函数来从URL中获取文件名
const getFileNameFromUrl = (url) => {
  if (!url) return ''
  try {
    // 先尝试从 URL 中截取文件名
    const urlParts = url.split('/')
    return urlParts[urlParts.length - 1]
  } catch (e) {
    // 如果解析失败，返回时间戳作为文件名
    return `file_${Date.now()}`
  }
}

const handleSizeChange = (val) => {
  query.value.limit = val
  getList()
}

const handleCurrentChange = (val) => {
  query.value.page = val
  getList()
}

// 修改提交方法，在提交前清理无关字段
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    // 定义各类型特有的字段
    const typeSpecificFields = {
      original: ['content', 'image_url', 'house_ids'],
      public: ['public_link', 'image_url'],
      video: ['video_account_id', 'is_same_entity', 'video_id', 'identity_id'],
      upload: ['video_title', 'video_cover_url', 'video_url']
    }

    // 通用字段 - 这些字段将被保留
    const commonFields = [
      'id',
      'title',
      'category',
      'author',
      'content_type',
      'is_top',
      'is_original',
      'top_background_url'
    ]

    // 创建提交数据对象
    const submitData = {
      ...form.value,
      image_url: form.value.image_urls?.[0]?.file_url || '',
      video_cover_url: form.value.video_cover_urls?.[0]?.file_url || '',
      video_url: form.value.video_urls?.[0]?.file_url || '',
      top_background_url: form.value.top_background_urls?.[0]?.file_url || ''
    }

    // 移除上传组件相关的数组字段
    delete submitData.image_urls
    delete submitData.video_cover_urls
    delete submitData.video_urls
    delete submitData.top_background_urls
    delete submitData.houses

    // 获取当前内容类型允许的字段
    const allowedFields = [...commonFields, ...(typeSpecificFields[form.value.content_type] || [])]

    // 清理不相关的字段
    Object.keys(submitData).forEach((key) => {
      if (!allowedFields.includes(key)) {
        if (typeof submitData[key] === 'string') {
          submitData[key] = ''
        } else if (Array.isArray(submitData[key])) {
          submitData[key] = []
        } else if (typeof submitData[key] === 'number') {
          submitData[key] = 0
        } else {
          delete submitData[key]
        }
      }
    })

    // 根据内容类型特殊处理某些字段
    if (form.value.content_type === 'original') {
      // 原创文章需要清空视频和公众号相关字段
      submitData.public_link = ''
      submitData.video_account_id = ''
      submitData.video_id = ''
      submitData.video_title = ''
      submitData.video_cover_url = ''
      submitData.video_url = ''
      submitData.identity_id = ''
      submitData.is_same_entity = 0
    } else if (form.value.content_type === 'public') {
      // 公众号文章需要清空视频和原创相关字段
      submitData.content = ''
      submitData.video_account_id = ''
      submitData.video_id = ''
      submitData.video_title = ''
      submitData.video_cover_url = ''
      submitData.video_url = ''
      submitData.identity_id = ''
      submitData.is_same_entity = 0
      submitData.house_ids = []
    } else if (form.value.content_type === 'video') {
      // 视频号需要清空其他类型字段
      submitData.content = ''
      submitData.public_link = ''
      submitData.video_title = ''
      submitData.video_cover_url = ''
      submitData.video_url = ''
      submitData.house_ids = []
    } else if (form.value.content_type === 'upload') {
      // 上传视频需要清空其他类型字段
      submitData.content = ''
      submitData.public_link = ''
      submitData.video_account_id = ''
      submitData.video_id = ''
      submitData.identity_id = ''
      submitData.is_same_entity = 0
      submitData.house_ids = []
    }

    const res = await api.update(submitData)

    if (res.code === 200) {
      ElMessage.success(form.value.id ? '编辑成功' : '添加成功')
      dialogVisible.value = false
      getList()
    } else {
      ElMessage.error(res.msg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const handleDialogClose = async () => {
  // 重置表单验证
  formRef.value?.resetFields()

  // 使用 nextTick 确保 DOM 更新
  await nextTick()

  // 完全重置表单数据
  form.value = JSON.parse(JSON.stringify(initForm))

  // 重置主播相关数据
  selectedXfs.value = null
  xfsOptions.value = []

  // 重置已选择的楼盘
  selectedHouseMap.value.clear()

  // 增加组件key的值，强制重新渲染上传组件
  componentKey.value++

  // 重置所有上传组件ID
  uploadIds.value = {
    image: generateUploadId(),
    videoCover: generateUploadId(),
    video: generateUploadId(),
    topBackground: generateUploadId()
  }

  // 最后关闭对话框
  dialogVisible.value = false
}

// 辅助方法
const getCategoryLabel = (value) => {
  const item = categoryOptions.find((opt) => opt.value === value)
  return item ? item.label : '--'
}

const getContentTypeLabel = (type) => {
  return contentTypes[type]?.label || '--'
}

watch(
  () => form.value.identity_id,
  (newId) => {
    if (!newId) {
      selectedXfs.value = null
      return
    }

    // 如果新ID与当前选中的主播ID不同，需要重新获取详情
    if (!selectedXfs.value || selectedXfs.value.id !== newId) {
      handleXfsSelect(newId)
    }
  }
)

const getContentTypeTag = (type) => {
  return contentTypes[type]?.type || 'default'
}

// 初始化
onMounted(async () => {
  resetUploadInstanceId() // 初始化时重置上传组件ID
  getList()
})

// 监听弹窗可见性变化
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    resetUploadIds() // 当弹窗关闭时重置所有上传组件ID
  }
})
</script>

<style lang="scss" scoped>
.house-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 5px 0;

  .house-name {
    font-weight: 500;
  }

  .house-info {
    color: #999;
    font-size: 12px;
  }
}

.selected-houses {
  margin-top: 8px;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;

  .selected-house-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;

    .house-name {
      font-weight: 500;
    }

    .house-location {
      color: #909399;
      font-size: 12px;
    }

    &:not(:last-child) {
      border-bottom: 1px dashed #dcdfe6;
    }
  }
}
</style>

<style lang="scss">
// 添加全局样式以确保编辑器弹出层正确显示
.tox-tinymce-aux {
  z-index: 3000 !important;
}

.tox-dialog-wrap {
  z-index: 3000 !important;
}

.tox-dialog {
  z-index: 3001 !important;
}

.article-dialog {
  .el-dialog {
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: 90vh;
    overflow-y: auto;
  }

  .tinymce-editor {
    z-index: auto;

    .tox-editor-container {
      z-index: auto;
    }
  }
}

// 确保弹出框样式正确
.tox {
  .tox-toolbar__primary {
    z-index: 1000;
  }

  .tox-toolbar__overflow {
    z-index: 1000;
  }
}
</style>
