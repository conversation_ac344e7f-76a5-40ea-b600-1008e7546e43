<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="keywords" content="yyladmin" />
  <meta name="description" content="yyladmin" />
  <link rel="icon" href="/favicon.ico" />
  <script src="https://map.qq.com/api/gljs?v=1.exp&key=POYBZ-JXV6Q-3KD5A-44ZNG-SGTIO-J6FDN"></script>

  <title>yylAdmin</title>
</head>

<body>
  <div id="app" class="app">
    <div class="mesh-loader">
      <div class="set-one">
        <div class="circle"></div>
        <div class="circle"></div>
      </div>
      <div class="set-two">
        <div class="circle"></div>
        <div class="circle"></div>
      </div>
    </div>
  </div>

  <script type="module" src="/src/main.js"></script>
  <script>
    global = globalThis
  </script>

  <style>
    html,
    body,
    #app {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .mesh-loader {
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    .mesh-loader .circle {
      position: absolute;
      width: 25px;
      height: 25px;
      margin: -12.5px;
      border-radius: 50%;
      background: #fe7002;
      animation: mesh 3s ease-in-out infinite;
      animation: mesh 3s ease-in-out infinite -1.5s;
    }

    .mesh-loader>div .circle:last-child {
      animation-delay: 0s;
      animation-delay: 0s;
    }

    .mesh-loader>div {
      position: absolute;
      top: 50%;
      left: 50%;
    }

    .mesh-loader>div:last-child {
      transform: rotate(90deg);
      transform: rotate(90deg);
    }

    @keyframes mesh {
      0% {
        transform: rotate(0);
        transform: rotate(0);
        transform-origin: 50% -100%;
        transform-origin: 50% -100%;
      }

      50% {
        transform: rotate(360deg);
        transform: rotate(360deg);
        transform-origin: 50% -100%;
        transform-origin: 50% -100%;
      }

      50.00001% {
        transform: rotate(0deg);
        transform: rotate(0deg);
        transform-origin: 50% 200%;
        transform-origin: 50% 200%;
      }

      100% {
        transform: rotate(360deg);
        transform: rotate(360deg);
        transform-origin: 50% 200%;
        transform-origin: 50% 200%;
      }
    }

    @keyframes mesh {
      0% {
        transform: rotate(0);
        transform: rotate(0);
        transform-origin: 50% -100%;
        transform-origin: 50% -100%;
      }

      50% {
        transform: rotate(360deg);
        transform: rotate(360deg);
        transform-origin: 50% -100%;
        transform-origin: 50% -100%;
      }

      50.00001% {
        transform: rotate(0deg);
        transform: rotate(0deg);
        transform-origin: 50% 200%;
        transform-origin: 50% 200%;
      }

      100% {
        transform: rotate(360deg);
        transform: rotate(360deg);
        transform-origin: 50% 200%;
        transform-origin: 50% 200%;
      }
    }
  </style>
</body>

</html>