<template>
	<!-- Query -->
	<el-form ref="formRef" :model="query" :inline="true" label-width="90px">
		<el-form-item label="查询区县">
		<el-select v-model="query.regions" class="w-full ya-search-value" multiple clearable filterable >
		  <el-option
		      v-for="item in regionsData"
		      :key="item.key"
		      :label="item.value"
		      :value="item.key"
		  />
		</el-select>
		  
		</el-form-item>
		<el-form-item>
		  <el-select v-model="query.date_field" class="ya-date-field" placeholder="时间类型">
		    <el-option value="dealDate" label="创建时间" />
		    <!-- <el-option value="update_time" label="修改时间" /> -->
		  </el-select>
		</el-form-item>
		<el-form-item>
		  <el-date-picker
		    v-model="query.date_value"
		    type="datetimerange"
		    class="ya-date-value"
		    start-placeholder="开始日期"
		    end-placeholder="结束日期"
		    value-format="YYYY-MM-DD HH:mm:ss"
		  />
		</el-form-item>
		<el-form-item label="发布状态">
		<el-select v-model="query.release_status" class="w-full ya-search-value" clearable filterable >
		  <el-option
		      v-for="item in releaseStatus"
		      :key="item.key"
		      :label="item.value"
		      :value="item.key"
		  />
		</el-select>
		</el-form-item>
		<el-form-item>
		  <el-button type="primary" title="搜索" @click="getProjectsList()">搜索</el-button>
		  <el-button @click="refresh()"><svg-icon icon-class="refresh" /></el-button>
		  <newExport title="导入" @on-import="imports" />
		</el-form-item>
	</el-form>
    <!-- 列表 -->
  <el-table
    ref="table"
    v-loading="loading"
    :data="data"
    :height="height"
    @sort-change="sort"
    @selection-change="select"
    key="table1"
  >
    <el-table-column prop="sort" label="排序" width="80" sortable="custom" />
    <el-table-column prop="project_name" label="所属周期" min-width="120" >
	<template #default="scope">
	    <span v-text="scope.row.start_date+'-'+scope.row.end_date"></span>
	  </template>
	</el-table-column>
    <el-table-column prop="project_name" label="项目名称" min-width="100" />
    <el-table-column prop="promotion_name" label="项目推广名称" min-width="100" />
    <el-table-column prop="region_name" label="城区" min-width="80" />
    <el-table-column prop="company" label="所属企业" min-width="80" />
    <el-table-column prop="deal_count" label="成交套数(套)" />
    <el-table-column prop="deal_area" label="成交面积(㎡)" />
    <el-table-column prop="deal_price" label="成交价格(元/㎡)" />
    <el-table-column prop="deal_amount" label="成交金额(万元)" />
    <el-table-column prop="deal_unit_price" label="成交套均总价(万元/套)" />
    <el-table-column prop="create_time" label="创建时间" min-width="120"/>
    <el-table-column label="操作" width="130">
      <template #default="scope">
        <el-link type="primary" class="mr-1" :underline="false" @click="scanDetails(scope.row)">
          编辑
        </el-link>
        <el-link v-if="scope.row.release_status != 1" type="primary" :underline="false" @click="changeStateButton(scope.row,'up')">
          发布
        </el-link>
        <el-link v-else-if="scope.row.release_status == 1" type="primary" :underline="false" @click="changeStateButton(scope.row,'down')">
          下架
        </el-link>
		  &nbsp;
		  <el-link type="primary" v-if="scope.row.release_status != 1" :underline="false" @click="deleteRow(scope.row.id)">
			删除
		  </el-link>
      </template>
    </el-table-column>
  </el-table>
  <pagination v-show="count > 0" v-model:total="count" v-model:page="query.page" v-model:limit="query.limit"
    @pagination="getProjectsList" />
	<!-- 添加修改 -->
	<el-dialog
	      v-model="dialog"
	      :title="dialogTitle"
	      :close-on-click-modal="false"
	      :close-on-press-escape="false"
	      :before-close="cancel"
	      top="5vh"
	    >
	      <el-form ref="ref" :rules="rules" :model="model" label-width="100px">
	        <el-tabs>
	          <el-tab-pane label="信息">
	            <el-scrollbar native :height="height - 80">
	              
	              <el-form-item label="项目名称" prop="project_name">
	                <el-input v-model="model.project_name" placeholder="请输入名称" disabled />
	              </el-form-item>
	              
	              <el-form-item label="推广名称" prop="promotion_name">
	                <el-input
	                    v-model="model.promotion_name"
	                    type="textarea"
	                    autosize
	                    placeholder="推广名称"
	                    disabled
	                  />
	              </el-form-item>
					<el-form-item label="城区" prop="region_name">
					  <el-input v-model="model.region_name" placeholder="请输入城区名称" disabled />
					</el-form-item>
	              <el-form-item  label="发布状态">
	                
					<el-select v-model="model.release_status" class="w-full ya-search-value" clearable filterable >
					  <el-option
					      v-for="item in releaseStatus"
					      :key="item.key"
					      :label="item.value"
					      :value="item.key"
					  />
					  </el-select>
	              </el-form-item>
	              
	              <el-form-item label="所属企业" prop="company">
					  <el-input
					      v-model="model.company"
					      type="textarea"
					      autosize
					      placeholder="所属企业"
						  clearable
					    />
	              </el-form-item>
				   <el-form-item label="成交套数" type="number" prop="deal_count">
				     <el-input v-model="model.deal_count" placeholder="成交套数(套)"  >
					 <template #append>(套)</template>
					 </el-input>
				   </el-form-item>
				   <el-form-item label="成交面积" prop="deal_area">
				     <el-input v-model="model.deal_area" type="number" placeholder="成交面积(㎡)">
						 <template #append>(㎡)</template>
					 </el-input>
				   </el-form-item>
				   <el-form-item label="成交价格" prop="deal_price">
				     <el-input v-model="model.deal_price" type="number" placeholder="成交价格(元/㎡)" >
						 <template #append>(元/㎡)</template>
					 </el-input>
				   </el-form-item>
				   <el-form-item label="成交金额" prop="deal_amount">
				     <el-input v-model="model.deal_amount" type="number" placeholder="成交金额(万元)"  >
						 <template #append>(万元)</template>
					 </el-input>
				   </el-form-item>
				   <el-form-item label="成交套均总价" prop="deal_unit_price">
				     <el-input v-model="model.deal_unit_price" type="number" placeholder="成交套均总价"  >
					 <template #append>(万元/套)</template>
					 </el-input>
				   </el-form-item>
				  <el-form-item label="周期开始日期" prop="start_date">
				    <el-date-picker
				  	v-model="model.start_date"
				  	type="date"
				  	value-format="YYYY-MM-DD"
				  	:default-time="new Date(2024, 1, 1)"
				  	placeholder="上新时间"
					disabled
				    />
				  </el-form-item>
				  <el-form-item label="周期结束日期" prop="end_date">
				    <el-date-picker
				  	v-model="model.end_date"
				  	type="date"
				  	value-format="YYYY-MM-DD"
				  	:default-time="new Date(2024, 1, 1)"
				  	placeholder="上新时间"
					disabled
				    />
				  </el-form-item>
	              
	              <el-form-item v-if="model.create_time" label="添加时间" prop="create_time">
	                <el-input v-model="model.create_time" disabled />
	              </el-form-item>
				  <el-form-item v-if="model.update_time" label="更新时间" prop="update_time">
				    <el-input v-model="model.update_time" disabled />
				  </el-form-item>
	            </el-scrollbar>
	          </el-tab-pane>
	         
	        </el-tabs>
	      </el-form>
	      <template #footer>
	        <el-button :loading="loading" @click="cancel">取消</el-button>
	        <el-button :loading="loading" type="primary" @click="submit">提交</el-button>
	      </template>
	    </el-dialog>
</template>
<script setup> 

import {ref, onMounted, defineProps} from "vue"
import checkPermission from '@/utils/permission';
import Pagination from '@/components/Pagination/index.vue'
import { getProjectList,getRegionList,changeState,monthDataExport,editDealData,deleteItems  } from '@/api/project/housedealdata'
import { getPageLimit } from '@/utils/settings'
import { customformat } from '@/utils/dateUtil'
import newExport from '@/components/ExcelImport/newExport.vue'
import { useUserStoreHook } from '@/store/modules/user'
// const regionInfo = defineProps({region:{type:Object}});

const loading = ref(true);
const regionsData = ref([])
const data = ref([])
const height = ref(560)
const dialogheight = ref(700)
const query = ref({
	page: 1,
	limit: 20,
	region_name: null,
	release_status: null,
	dealDate: [null,null],
	type: "month",
	date_field:"dealDate"
});
const uploadUrl = import.meta.env.VITE_APP_BASE_URL + '/admin/house.HouseDealData/housedealdataexport'
const releaseStatus = ref([{"key":0,"value":"未发布"},{"key":1,"value":"已发布"},{"key":2,"value":"已下架"}])
const count = ref(0);
const idkey = ref('id');
const dialog = ref(false);
const dialogTitle = ref('');
const selection = ref([]);
const model = ref({});
const formRef = ref(null)
function getProjectsList(){
    loading.value = true;
    getProjectList(query.value).then((res) => {
          data.value = res.data.data
		  count.value = res.data.total
          loading.value = false
        })
        .catch(() => {
          loading.value = false
        })
		loading.value = false
}
const regionlist = async () => {
	  loading.value = true;
	  try {
	    const params = {}
	    const res = await getRegionList(params);
	    if (res && res.data) {
			regionsData.value = res.data
	    } else {
	      ElMessage.error('获取城区列表失败');
	    }
	  } catch (error) {
	    ElMessage.error('获取列表异常');
	  } finally {
	    loading.value = false;
	  }
	}
const changeStateButton = async (row,opttype)=>{
		var release_status = 1;
	  if(opttype == "up"){
		  var msg = "发布"
		  if(row.release_status==1){
			ElMessage.error('已经是发布状态')
			return
		  }
	  }else{
		var msg = "下架"  
		if(row.release_status!=1){
			ElMessage.error('已经是下架状态')
			return
		}
		release_status = 2;
	  }
      loading.value = true
	  ElMessageBox.confirm('确定要'+msg+'数据吗？', '提示', {
	    type: 'warning'
	  }).then(() => {
	    try {
	      changeState({
			"id":row.id,
			"type":query.value.type,
			"release_status":release_status
			}).then((res) => {
			  getProjectsList()
			  ElMessage.success(res.msg)
			  loading.value = false
			})
	      
	    } catch (error) {
			console.log(error)
	      ElMessage.error(msg+'失败')
	    } finally {
	      loading.value = false
	    }
	  }).catch(() => { loading.value = false })
      loading.value = false

    }
	const deleteRow = async (id)=>{
			
	      loading.value = true
		  ElMessageBox.confirm('确定要删除数据吗？', '提示', {
		    type: 'warning'
		  }).then(() => {
		    try {
		      deleteItems({
				"id":id,
				"type":query.value.type
				}).then((res) => {
				  getProjectsList()
				  ElMessage.success(res.msg)
				  loading.value = false
				})
		      
		    } catch (error) {
				console.log(error)
		      ElMessage.error(msg+'失败')
		    } finally {
		      loading.value = false
		    }
		  }).catch(() => { loading.value = false })
	      loading.value = false
	
	    }
	const refresh = () => {
	  
	  query.value.regions=null
	  query.value.release_status=null
	  query.value.date_value= [null,null]
	  getProjectsList()
	}
// 排序
function sort(sort) {
      this.query.sort_field = sort.prop
      this.query.sort_value = ''
      if (sort.order === 'ascending') {
        this.query.sort_value = 'asc'
        this.list()
      }
      if (sort.order === 'descending') {
        this.query.sort_value = 'desc'
        this.list()
      }
    }
// 操作
function select(selection) {
      selection.value = selection
      // this.selectIds = this.selectGetIds(selection).toString()
    }
const scanDetails = (row) => {
		model.value = row
		dialog.value = true
		dialogTitle.value = row.project_name + ' 详情：(' + row.start_date + '~'+ row.end_date +')'
    }
	// 导入，results数据，header表头
	const imports = async ({ results, header,filename }) => {
	  loading.value = true
	  const userStore = useUserStoreHook()
	  const tokenValue = userStore.token
	  const formData = new FormData()
	  formData.append('filename', filename) // 可以根据实际需要生
	  formData.append('import',JSON.stringify(results)) // 可以根据实际需要生
	  formData.append('type', query.value.type) // 可以根据实际需要生
	  try{
		 const response = await fetch(uploadUrl, {
		   method: 'POST',
		   headers: {
		     AdminToken: tokenValue
		   },
		   body: formData
		 })
		 const res = await response.json()
		 if(res.code!=200){
			 ElMessage.error(res.msg)
		 }else{
			ElMessage.success(res.msg)
		 }

		 getProjectsList()
		 
		 loading.value = false 
	  }catch{
		  loading.value = false 
	  }
	  
	  // console.log(await uploadResponse.json())
	  // .then((res) => {
	  //     getProjectsList()
	  //     ElMessage.success(res.msg)
	  //     loading.value = false
	  //   })
	  //   .catch(() => {
	  //     loading.value = false
	  //   })
	}
function submit() {
  loading.value = true;
  model.value.type = query.value.type
    editDealData(model.value).then((res) => {
      getProjectsList()
      dialog.value = false
    })
    .catch(() => {
      loading.value = false
    })
  loading.value = false
}
function cancel() {
  dialog.value = false
  getProjectsList()
  reset()    
}
// 重置
function reset(row) {
  if (row) {
    model.value = row
  } else {
    model.value = {}
  }
}
onMounted(()=>{
	regionlist() 
    // this.height = screenHeight(310)
    getProjectsList()
})
</script>