<template>
  <div class="app-container">
    <!-- 查询 -->
    <el-row>
      <el-col class="mb-2">
        <el-form-item label="楼盘名称"> 样板间图片 </el-form-item>
        <el-divider></el-divider>
      </el-col>
    </el-row>
   
    <el-dialog
      v-model="selectDialog"
      :title="selectTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      top="20vh"
      append-to-body
    >
      <el-form label-width="120px">
        <el-form-item v-if="selectType === 'dele'">
          <span class="ya-margin-left c-red">
            确定要删除选中的{{ name }}吗？删除会对已使用该文件的业务造成影响！
          </span>
        </el-form-item>

        <el-form-item :label="name + 'ID'">
          <el-input v-model="selectIds" type="textarea" autosize disabled />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="selectCancel">取消</el-button>
        <el-button type="primary" @click="selectSubmit">提交</el-button>
      </template>
    </el-dialog>

    <!-- 列表筛选 -->
    <el-row :gutter="3">
      
      <!-- 卡片展示 -->
      <template v-if="showMode == 'card'">
        <el-checkbox-group
          v-model="selection"
          style="display: contents; line-height: 1.5"
          @change="select"
        >
          <el-col v-if="count > 0" :span="22">
            <el-scrollbar native :height="height">
              <el-row v-loading="loading" :gutter="3" class="mt-[6px] !mr-0 !ml-0">
                <el-col
                  v-for="(item, index) in data"
                  :key="index"
                  :span="4"
                  style="margin-bottom: 6px; text-align: center"
                >
                  <el-card
                    :body-style="{
                      minWidth: '16.0%',
                      height: (height - height * 0.1) / 3 + 'px',
                      minHeight: '180px',
                      padding: '0 6px'
                    }"
                  >
                    <div style="text-align: left">
                      <el-checkbox :key="item[idkey]" :value="item[idkey]" :label="item[idkey]">
                        {{ pictypes[item['pictype']] }} ({{ item[idkey] }})
                      </el-checkbox>
                    </div>
                    <div
                      :style="{
                        width: '100%',
                        height:
                          (height - height * 0.1) / 3 - ((height - height * 0.1) / 3) * 0.5 + 'px',
                        minHeight: '62px'
                      }"
                    >
                      <el-image
                        style="height: 100%"
                        :src="item.path"
                        :preview-src-list="fileImgPre"
                        :initial-index="imagePreIndex(item.path)"
                        fit="contain"
                        title="点击看大图"
                        lazy
                      />
                    </div>
                    
                  </el-card>
                </el-col>
              </el-row>
            </el-scrollbar>
            
          </el-col>
          <el-col v-else :span="22">
            <el-empty :description="'暂无' + name" />
          </el-col>
        </el-checkbox-group>
      </template>
      
    </el-row>
     <!-- 文件管理操作 -->
     <el-row >
      <el-col class="text-right mt-2">
        <el-button @click="fileCancel()">取消</el-button>
        <el-button type="primary" @click="fileSubmit(selection)">确定</el-button>
      </el-col>
    </el-row>
    <!-- 分页 -->
   
    
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import screenHeight from '@/utils/screen-height'
import Pagination from '@/components/Pagination/index.vue'
import clip from '@/utils/clipboard'
import { useSettingsStoreHook } from '@/store/modules/settings'
import { useUserStoreHook } from '@/store/modules/user'
import { arrayColumn } from '@/utils/index'
import { list, info, add, edit, dele, editSet, setlist } from '@/api/project/houseImge'

export default {
  name: '',
  components: { Pagination },
  
  props: {
    houseId: {
      type: Number,
      default: 0
    },
    decorationId: {
      type: Number,
      default: 0
    },
    picType: {
      type: Number,
      default: 6
    }
  },
  data() {
    return {
      name: '文件',
      recycle: 0, // 是否回收站
      height: 680,
      loading: false,
      is_tao: 0, // 是否是套图
      idkey: 'id',
      exps: [],
      picsets: [], // 套图列表
      query: {
        page: 1,
        limit: 18,
        storage: '',
        pictype: '',
        is_disable: '',
        search_field: 'name',
        search_exp: 'like',
        date_field: 'create_time',
        sort_field: 'id',
        sort_value: '',
        house_id: 0
      },
      data: [],
      count: 0,
      showMode: 'card',
      dialog: false,
      dialogTitle: '',
      dialogSet: false,
      dialogTitleSet: '新增套图',
      model: {
        id: 0,
        file_id: '',
        name: '',
        pictype: '',
        path: '',
        remark: '',
        sort: 250,
        is_disable: 0,
        is_delete: 0,
        is_cover: 0,
        house_id: 0,
        images: []
      },

      rules: {
        pictype: [{ required: true, message: '请选择图片类型', trigger: 'blur' }],
        file_url: [{ required: true, message: '请输入文件链接', trigger: 'blur' }]
      },
      fileIds: [],
      storages: [],
      pictypes: [],
      fileImgPre: [],
      selectAll: false,
      selectAllInd: false,
      selection: [],
      selectIds: '',
      selectTitle: '操作',
      selectDialog: false,
      selectType: '',
      group_id: 0,
      tag_ids: [],
      domain: '',
      pictype: '',
      is_disable: 0,
      uploadAction: add(),
      uploadAccept: '',
      uploadFilelist: [],
      uploadCount: 0,
      uploadNumber: 0,
      is_set: 0, // 0 上传单图 1 上传多图
      is_watermark: 1,
      source: 1
    }
  },
 

  created() {
    
    this.height = screenHeight() - 10
    
    this.height = this.height - 145
    this.query.pictype = this.picType
    this.query.house_id = this.houseId
    this.query.house_id = this.houseId
    this.query.limit = 18
    this.list()
  },
  methods: {
    // 列表
    list() {
      this.loading = true
      list(this.query)
          .then((res) => {
            this.listData(res.data)
          })
          .catch(() => {
            this.loading = false
          })
      
    },
    
    listData(data) {
      this.data = data.list
      this.count = data.count
      this.fileIds = data.ids
      this.exps = data.exps
      this.pictypes = data.setting.pictypes
      this.imagePreview(data.list)
      this.loading = false
    },
  
    // 添加修改
    add() {
      this.dialog = true
      this.dialogTitle = this.name + '添加'
      this.reset()
      this.model.type = 'url'
    },
    
    
    cancel() {
      this.dialog = false
      this.reset()
    },


    // 查询
    search() {
      this.query.page = 1
      this.list()
    },
    // 刷新
    refresh() {
      const limit = this.query.limit
      this.query = this.$options.data().query
      this.query.limit = limit
      this.reset()
      this.list()
    },
    // 排序
    sort(sort) {
      if (this.showMode === 'card') {
        if (this.query.sort_value && this.query.sort_value) {
          this.list()
        }
      }
    },
    // 重置
    reset(row) {
      if (row) {
        this.model = row
      } else {
        this.model = this.$options.data().model
      }
      this.selection = []
      this.selectIds = ''
      this.selectAll = false
      this.selectAllInd = false
      if (this.$refs['ref'] !== undefined) {
        this.$refs['ref'].resetFields()
      }
      if (this.$refs['table'] !== undefined) {
        try {
          this.$refs['table'].clearSelection()
          this.$refs['table'].clearSort()
        } catch (error) {}
      }
    },

    // 选择操作
    select(selection) {
      this.selection = selection
      this.selectIds = this.selectGetIds().toString()
      this.selectAll = selection.length === this.fileIds.length
      this.selectAllInd = selection.length > 0 && selection.length < this.fileIds.length
    },
    selectAlls(value) {
      if (value) {
        this.selection = this.fileIds
        if (this.$refs['table'] !== undefined) {
          this.$refs['table'].toggleAllSelection()
        }
      } else {
        this.selection = []
        if (this.$refs['table'] !== undefined) {
          try {
            this.$refs['table'].clearSelection()
          } catch (error) {}
        }
      }
      this.select(this.selection)
    },
    selectGetIds() {
      if (this.showMode === 'card') {
        return this.selection
      } else {
        return arrayColumn(this.selection, this.idkey)
      }
    },
    selectAlert() {
      ElMessageBox.alert('请选择需要操作的' + this.name, '提示', {
        type: 'warning',
        callback: () => {}
      })
    },
    selectOpen(selectType, selectRow = '') {
      if (selectRow) {
        if (this.showMode === 'card') {
          this.selection = []
        } else {
          if (this.$refs['table'] !== undefined) {
            try {
              this.$refs['table'].clearSelection()
            } catch (error) {}
          }
        }
        this.select(selectRow)
      }
      if (!this.selection.length) {
        this.selectAlert()
      } else {
        this.selectTitle = '操作'
        if (selectType === 'dele') {
          this.selectTitle = this.name + '删除'
        }
        this.selectDialog = true
        this.selectType = selectType
      }
    },
    selectCancel() {
      this.selectDialog = false
    },
    selectSubmit() {
      if (!this.selection.length) {
        this.selectAlert()
      } else {
        const selectType = this.selectType
        if (selectType === 'dele') {
          this.dele()
        }
        this.selectDialog = false
      }
    },
    // 文件管理操作
    fileCancel() {
      this.reset()
      this.$emit('fileCancel')
    },
    fileSubmit(row) {
      if (!row.length) {
        ElMessageBox.alert('请选择文件')
      } else {
        const files = []
        const data = this.data
        const row_len = row.length
        const data_len = data.length
        for (let i = 0; i < row_len; i++) {
          for (let j = 0; j < data_len; j++) {
            if (this.showMode === 'card') {
              if (row[i] === data[j][this.idkey]) {
                data[j]['file_url'] = data[j]['path']
                files.push(data[j])
                break
              }
            } else {
              if (row[i][this.idkey] === data[j][this.idkey]) {
                files.push(data[j])
                break
              }
            }
          }
        }
        
        this.reset()
        this.$emit('fileSubmit', files, this.fileType)
      }
    },
    
    // 图片预览
    imagePreview(list) {
      console.log('图片预览')
      var preview = []
      const length = list.length
      for (let index = 0; index < length; index++) {
        preview.push(list[index]['path'])
      }
      this.fileImgPre = preview
    },
    imagePreIndex(fileUrl) {
      return this.fileImgPre.indexOf(fileUrl)
    },
    // 文件下载
    fileDownload(file) {
      clip(file.name, '文件名复制成功')
      setTimeout(() => {
        window.open(file.path, '_blank')
      }, 500)
    },

    // 类型筛选
    typeSelect(pictype = '') {
      this.query.pictype = pictype
      this.list()
    },

    // 复制
    copy(text) {
      clip(text)
    },
    // 单元格双击复制
    cellDbclick(row, column) {
      this.copy(row[column.property])
    }
  }
}
</script>
