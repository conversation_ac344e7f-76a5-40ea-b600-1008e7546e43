<template>
  <div class="example-container">
    <el-card class="example-card">
      <template #header>
        <div class="card-header">
          <span>装修条目标准管理组件使用示例</span>
          <div class="header-actions">
            <el-button type="primary" @click="changeId">切换项目ID</el-button>
            <el-button @click="refreshComponent">刷新组件</el-button>
          </div>
        </div>
      </template>
      
      <div class="example-content">
        <div class="id-selector">
          <el-form :inline="true">
            <el-form-item label="当前项目ID:">
              <el-input 
                v-model="selectedId" 
                placeholder="请输入项目ID" 
                style="width: 200px"
                @change="handleIdChange"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="success" @click="applyId">应用ID</el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 装修条目组件 -->
        <DecorationItem 
          ref="decorationItemRef"
          :id="currentId"
        />
      </div>
    </el-card>
    
    <!-- 使用说明 -->
    <el-card class="usage-card" style="margin-top: 20px;">
      <template #header>
        <span>使用说明</span>
      </template>
      
      <div class="usage-content">
        <h4>组件特性：</h4>
        <ul>
          <li>支持通过 <code>id</code> 属性传入项目标识符</li>
          <li>自动监听 <code>id</code> 变化并重新加载数据</li>
          <li>包含搜索、分页、新增、编辑、删除等完整功能</li>
          <li>响应式设计，适配不同屏幕尺寸</li>
          <li>支持通过 ref 调用组件内部方法</li>
          <li>集成现有的 Pagination 组件和 Element Plus 样式</li>
        </ul>
        
        <h4>使用方法：</h4>
        <pre><code>&lt;template&gt;
  &lt;DecorationItem :id="projectId" ref="decorationItemRef" /&gt;
&lt;/template&gt;

&lt;script setup&gt;
import DecorationItem from '@/views/evaluating/components/DecorationItem.vue'

const projectId = ref('123')
const decorationItemRef = ref()

// 刷新组件数据
const refresh = () => {
  decorationItemRef.value?.refresh()
}
&lt;/script&gt;</code></pre>
        
        <h4>API 接口：</h4>
        <ul>
          <li><code>GET /admin/decoration.Item/list</code> - 获取装修条目列表</li>
          <li><code>GET /admin/decoration.Item/info</code> - 获取装修条目详情</li>
          <li><code>POST /admin/decoration.Item/add</code> - 添加装修条目</li>
          <li><code>POST /admin/decoration.Item/edit</code> - 编辑装修条目</li>
          <li><code>POST /admin/decoration.Item/delete</code> - 删除装修条目</li>
        </ul>

        <h4>表格字段说明：</h4>
        <ul>
          <li><strong>序号</strong>：记录的唯一标识</li>
          <li><strong>室内分类</strong>：装修项目的分类</li>
          <li><strong>入户门</strong>：装修位置信息</li>
          <li><strong>不含项目</strong>：不包含的装修项目</li>
          <li><strong>TODO项目</strong>：待完成的装修项目</li>
          <li><strong>小计单价</strong>：单项装修的价格</li>
          <li><strong>总金额</strong>：装修项目的总价格</li>
          <li><strong>状态</strong>：启用/禁用状态</li>
        </ul>

        <h4>编辑表单字段：</h4>
        <ul>
          <li><strong>大类</strong>：下拉选择装修分类</li>
          <li><strong>位置</strong>：下拉选择装修位置（入户门、客厅、卧室等）</li>
          <li><strong>类型</strong>：下拉选择项目类型</li>
          <li><strong>品牌</strong>：复选框组选择相关品牌</li>
          <li><strong>不含项目</strong>：文本域输入不包含的项目</li>
          <li><strong>TODO项目</strong>：文本域输入待完成项目</li>
          <li><strong>小计单价</strong>：数字输入框</li>
          <li><strong>总金额</strong>：数字输入框</li>
          <li><strong>状态</strong>：单选按钮选择启用/禁用</li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import DecorationItem from '@/views/evaluating/components/DecorationItem.vue'

defineOptions({
  name: 'DecorationItemExample'
})

const decorationItemRef = ref()
const currentId = ref('1')
const selectedId = ref('1')

const changeId = () => {
  const ids = ['1', '2', '3', '4', '5']
  const randomId = ids[Math.floor(Math.random() * ids.length)]
  selectedId.value = randomId
  currentId.value = randomId
  ElMessage.success(`已切换到项目ID: ${randomId}`)
}

const handleIdChange = (value) => {
  selectedId.value = value
}

const applyId = () => {
  if (!selectedId.value) {
    ElMessage.warning('请输入有效的项目ID')
    return
  }
  currentId.value = selectedId.value
  ElMessage.success(`已应用项目ID: ${selectedId.value}`)
}

const refreshComponent = () => {
  decorationItemRef.value?.refresh()
  ElMessage.success('组件已刷新')
}
</script>

<style lang="scss" scoped>
.example-container {
  padding: 20px;
  
  .example-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-actions {
        display: flex;
        gap: 10px;
      }
    }
    
    .example-content {
      .id-selector {
        margin-bottom: 20px;
        padding: 15px;
        background: #f5f7fa;
        border-radius: 4px;
      }
    }
  }
  
  .usage-card {
    .usage-content {
      h4 {
        margin: 20px 0 10px 0;
        color: #409eff;
      }
      
      ul {
        margin: 10px 0;
        padding-left: 20px;
        
        li {
          margin: 5px 0;
          line-height: 1.6;
        }
      }
      
      pre {
        background: #f5f7fa;
        padding: 15px;
        border-radius: 4px;
        overflow-x: auto;
        
        code {
          font-family: 'Courier New', monospace;
          font-size: 13px;
          line-height: 1.5;
        }
      }
      
      code {
        background: #f5f7fa;
        padding: 2px 4px;
        border-radius: 2px;
        font-family: 'Courier New', monospace;
        font-size: 13px;
        color: #e6a23c;
      }
    }
  }
}
</style>
