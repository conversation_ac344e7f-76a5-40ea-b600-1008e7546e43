import request from '@/utils/request'
// 会员任务
const url = '/admin/member.MemberTask/'
/**
 * 任务列表
 * @param {array} params 请求参数
 */
export function tasklist(data) {
    return request({
        url: url + 'list',
        method: 'post',
        data
      })
}
/**
 * 获取批次任务的列表
 * @param {array} params 请求参数
 */
export function bachNoInfo(data) {
    return request({
        url: url + 'detaillist',
        method: 'post',
        data
      })
}

/**
 * 批量审核任务
 * @param {array} params 请求参数
 */
export function bachReviewTask(data) {
    return request({
        url: url + 'detailreview',
        method: 'post',
        data
      })
}

