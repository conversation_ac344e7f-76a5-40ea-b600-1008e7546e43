import request from '@/utils/request';
// 早报
const url = '/admin/info.Zaobao/';

/**
 * 早报列表
 * @param {array} params 请求参数
 */
export function list(params) {
  return request({
    url: url + 'list',
    method: 'get',
    params: params
  })
}

/**
 * 创建早报
 * @param {array} params 请求参数
 */
export function add(data) {
  return request({
    url: url + 'add',
    method: 'post',
    data
  })
}

/**
 * 编辑早报
 * @param {array} params 请求参数
 */
export function edit(data) {
  return request({
    url: url + 'edit',
    method: 'post',
    data
  })
}
/**
 * 获取详情
 * @param {array} params 请求参数
 */
export function getInfo(data) {
  return request({
    url: url + 'getInfo',
    method: 'post',
    data
  })
}



/**
 * 上下架
 * @param {array} params 请求参数
 */
export function stateChange(data) {
  return request({
    url: url + 'stateChange',
    method: 'post',
    data
  })
}

/**
 * 获取默认模版
 * @param {array} params 请求参数
 */
export function getTemplate(data) {
  return request({
    url: url + 'getTemplate',
    method: 'post',
    data
  })
}

/**
 * 获取h5截图
 * @param {array} params 请求参数
 */
export function createScreenshot(data) {
  return request({
    url: '/admin/info.Zaobao/createScreenshot',
    method: 'post',
    data
  })
}