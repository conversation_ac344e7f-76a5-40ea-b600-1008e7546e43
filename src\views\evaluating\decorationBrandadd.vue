<template>
  <div class="app-container">
    <el-form :model="form" ref="formRef" label-position="top" :rules="rules">
      <!-- 基础信息 -->
      <div class="filter-section">
        <div class="section-title">基础信息</div>
        <el-form-item label="品牌中文名称" prop="cn_name">
          <el-input v-model="form.cn_name" placeholder="输入品牌中文名称" />
        </el-form-item>
        <el-form-item label="品牌英文名称" prop="en_name">
          <el-input v-model="form.en_name" placeholder="输入品牌英文名称" />
        </el-form-item>
        <el-form-item label="名称展示选择" prop="show">
          <el-radio-group v-model="form.show">
            <el-radio :label="1" :disabled="!form.cn_name" :key="1">
              <el-tooltip
                :disabled="form.show === '2' && !form.en_name"
                content="中文"
                placement="top"
              >
                <span>中文</span>
              </el-tooltip>
            </el-radio>
            <el-radio :label="2" :disabled="!form.en_name" :key="2">
              <el-tooltip
                :disabled="form.show === '1' && !form.cn_name"
                content="英文"
                placement="top"
              >
                <span>英文</span>
              </el-tooltip>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="品牌logo" prop="pic_id">
          <ImgUpload
            :isWatermark="form.is_watermark"
            :source="0"
            v-model="form.pic_id"
            v-model:file-url="form.pic_url"
            file-type="image"
            :height="100"
            upload
          />
        </el-form-item>
        <el-form-item label="级别" prop="level">
          <el-radio-group v-model="form.level">
            <el-radio label="1">经济</el-radio>
            <el-radio label="2">中档</el-radio>
            <el-radio label="3">高档</el-radio>
            <el-radio label="4">豪华</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
    </el-form>
    <div class="footer">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import ImgUpload from '@/components/FileManage/ImgUpload.vue'
import request from '@/utils/request'
import { useRouter } from 'vue-router'

// 状态变量
const router = useRouter()
const formRef = ref(null)
const loading = ref(false)

const initialForm = {
  cn_name: '',
  en_name: '',
  show: '', // 不预选任何选项
  pic_url: '',
  pic_id: 0,
  level: '1',
  is_watermark: 0
}

// 表单数据
const form = reactive({ ...initialForm })

// 表单验证规则
const rules = {
  cn_name: [
    {
      validator: (rule, value, callback) => {
        if (!value && !form.en_name) {
          callback(new Error('品牌中文名称和英文名称至少填写一个'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  en_name: [
    {
      validator: (rule, value, callback) => {
        if (!value && !form.cn_name) {
          callback(new Error('品牌中文名称和英文名称至少填写一个'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  show: [{ required: true, message: '请选择名称展示方式', trigger: 'change' }],
  pic_id: [], // 非必填
  level: [{ required: true, message: '请选择级别', trigger: 'change' }]
}

// 重置表单
const resetForm = () => {
  Object.assign(form, { ...initialForm })
  if (!form.cn_name && form.en_name) {
    form.show = '2' // 仅英文时默认选英文（但初始为空）
  } else if (form.cn_name && !form.en_name) {
    form.show = '1' // 仅中文时默认选中文（但初始为空）
  }
  if (formRef.value) {
    formRef.value.resetFields() // 重置验证状态
  }
}

// 取消
const cancel = () => {
  resetForm()
  router.push('/evaluating/decorationBrand') // 返回列表页面
}

// 提交
const submit = async () => {
  if (!formRef.value) return
  try {
    loading.value = true
    await formRef.value.validate()
    const res = await request.post('/admin/furnish.DecorationBrand/create', form)
    if (res.code === 400) {
      ElMessage.error(res.msg)
      resetForm()
      return
    }
    if (res.code === 200) {
      ElMessage.success('新增成功')
      resetForm()
      await router.push('/evaluating/decorationBrand')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('新增失败')
  } finally {
    loading.value = false
  }
}

// 页面加载
onMounted(() => {
  nextTick(() => {
    console.log('Component mounted, formRef:', formRef.value)
    resetForm()
  })
})

// 监听路由变化
watch(
  () => router.currentRoute.value.path,
  () => {
    if (router.currentRoute.value.path === '/evaluating/decorationBrandAdd') {
      resetForm()
    }
  }
)
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-section {
  background-color: #ffffff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #303133;
}

:deep(.el-form-item__label) {
  font-weight: bold;
  color: #606266;
}

.footer {
  margin-top: 20px;
  text-align: right;
}
</style>