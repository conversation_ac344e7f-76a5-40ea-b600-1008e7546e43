<template>
  <div class="app-container">
    <el-form :model="form" ref="formRef" label-position="top" :rules="rules">
      <!-- 基础信息 -->
      <div class="filter-section">
        <div class="section-title">基础信息</div>
        <el-form-item label="品牌中文名称" prop="cn_name">
          <el-input v-model="form.cn_name" placeholder="输入品牌中文名称" />
        </el-form-item>
        <el-form-item label="品牌英文名称" prop="en_name">
          <el-input v-model="form.en_name" placeholder="输入品牌英文名称" />
        </el-form-item>
        <el-form-item label="名称展示选择" prop="show">
          <el-radio-group v-model="form.show" @change="handleHeaderImageChange">
            <el-radio label="1">中文</el-radio>
            <el-radio label="2">英文</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="品牌logo" prop="pic">
          <ImgUploadsHouseType
            source="0"
            :isWatermark="form.is_watermark"
            v-model="form.pic"
            :upload-btn="form.pic.length ? '' : '上传图片'"
            file-type="image"
            :height="100"
          />
        </el-form-item>
        <el-form-item label="级别" prop="level">
          <el-radio-group v-model="form.level">
            <el-radio label="1">经济</el-radio>
            <el-radio label="2">中档</el-radio>
            <el-radio label="3">高档</el-radio>
            <el-radio label="4">豪华</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
    </el-form>
    <div class="footer">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import ImgUploadsHouseType from '@/components/FileManage/ImgUploadsHouseType.vue'
import request from '@/utils/request'
import { useRouter } from 'vue-router'

// 状态变量
const router = useRouter()
const formRef = ref(null)
const loading = ref(false)

// 表单数据
const form = reactive({
  cn_name: '',
  en_name: '',
  show: '', // 默认显示中文
  pic: [], // 图片URL或ID
  level: '', // 默认经济
  is_watermark: 0
})

// 表单验证规则
const rules = {
  cn_name: [{ required: true, message: '请输入品牌中文名称', trigger: 'blur' }],
  en_name: [{ required: true, message: '请输入品牌英文名称', trigger: 'blur' }],
  show: [{ required: true, message: '请选择名称展示方式', trigger: 'change' }],
  pic: [{ required: true, message: '请上传品牌logo', trigger: 'change' }],
  level: [{ required: true, message: '请选择级别', trigger: 'change' }]
}

// 图片上传来源（根据组件需求调整）
const source = ref('decorationBrand')

// 处理头图变化
const handleHeaderImageChange = () => {
  if (form.show !== '4') {
    form.pic = []
  }
}

// 取消
const cancel = () => {
  router.push('/evaluating/decorationBrand') // 返回列表页面
}

// 提交
const submit = async () => {
  if (!formRef.value) return
  try {
    loading.value = true
    await formRef.value.validate()
    const res = await request.post('/admin/furnish.DecorationBrand/create', form)
    if (res.code === 200) {
      ElMessage.success('新增成功')
      await router.push('/evaluating/decorationBrand')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('新增失败')
  } finally {
    loading.value = false
  }
}

// 页面加载
onMounted(() => {
  nextTick(() => {
    console.log('Component mounted, formRef:', formRef.value)
  })
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-section {
  background-color: #ffffff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #303133;
}

:deep(.el-form-item__label) {
  font-weight: bold;
  color: #606266;
}

.footer {
  margin-top: 20px;
  text-align: right;
}
</style>