import request from '@/utils/request';
// 意见管理
const url = '/admin/intelligent.ProductClass/';

/**
 * 列表
 * @param {array} params 请求参数
 */
export function queryList(params) {
    return request({
      url: url + 'list',
      method: 'get',
      params: params
    })
}
/**
 * 修改意见信息
 * @param {array} data 请求数据
 */
export function editSubmit(data) {
  return request({
    url: url + 'edit',
    method: 'post',
    data
  })
}

/**
 * 修改意见信息
 * @param {array} data 请求数据
 */
export function info(params) {
  return request({
    url: url + 'info',
    method: 'get',
    params: params
  })
}
/**
 * 发布状态修改
 * @param {array} data 请求数据
 */
export function releaseStatusChange(data) {
  return request({
    url: url + 'release',
    method: 'post',
    data
  })
}

/**
 * 发布状态修改
 * @param {array} data 请求数据
 */
export function deleteInfo(data) {
  return request({
    url: url + 'dele',
    method: 'post',
    data
  })
}

