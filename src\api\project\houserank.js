import request from '@/utils/request';
// 销售数据列表
const url = '/admin/house.Houserank/';

/**
 * 销售数据列表
 * @param {array} params 请求参数
 */
export function rankList(params) {
    return request({
      url: url + 'list',
      method: 'get',
      params: params
    })
}

/**
 * 修改信息
 * @param {array} data 请求数据
 */
export function editRank(data) {
  return request({
    url: url + 'edit',
    method: 'post',
    data
  })
}

/**
 * 获取楼盘信息
 * @param {array} params 请求数据
 */
export function rankInfo(params) {
  return request({
    url: url + 'info',
    method: 'get',
    params: params
  })
}

/**
 * 获取楼盘信息
 * @param {array} params 请求数据
 */
export function getHouseList(params) {
  return request({
    url: url + 'houselist',
    method: 'get',
    params: params
  })
}

/**
 * 获取楼盘信息
 * @param {array} params 请求数据
 */
export function rankhouselist(params) {
  return request({
    url: url + 'rankhouselist',
    method: 'get',
    params: params
  })
}

/**
 * 修改推荐流量计算因子
 * @param {array} params 请求数据
 */
export function editViewNum(params) {
	console.log(params)
  return request({
    url: url + 'editviewnum',
    method: 'post',
    params: params
  })
}

