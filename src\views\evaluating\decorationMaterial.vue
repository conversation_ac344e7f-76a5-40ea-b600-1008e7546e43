<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-form ref="queryRef" :model="query" :inline="true" label-width="90px">
        <el-form-item label="项目名称：" prop="name">
          <el-input v-model="query.name" maxlength="20" placeholder="请输入关键字" style="width: 140px" />
        </el-form-item>
            <el-form-item label="创建时间">
        <el-date-picker
          v-model="query.date_value"
          type="datetimerange"
          class="ya-date-value"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="[new Date(2024, 1, 1, 0, 0, 0), new Date(2024, 1, 1, 23, 59, 59)]"
        />
      </el-form-item>
      <!-- <el-form-item label="上线时间：" prop="online_time">
        <el-date-picker
          v-model="query.online_time"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择时间"
          style="width: 200px"
        />
      </el-form-item> -->
      <el-form-item label="发布状态">
          <el-select v-model="query.state" style="width: 140px">
            <el-option
              v-for="states in stateSelectList"
              :key="states.value"
              :label="states.label"
              :value="states.value"
            />
          </el-select>
        </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
        <el-button type="success" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格部分 -->
    <el-table
      v-if="tableData.length >= 0"
      ref="tableRef"
      v-loading="loading"
      :data="tableData"
      style="width: 100%; margin-top: 20px"
      row-key="id"
    >
      <el-table-column prop="id" label="材质ID" width="80" />
      <el-table-column prop="name" label="材质名称" width="150" />
      <el-table-column prop="update_time" label="更新时间" width="180" />
      <el-table-column prop="release_status" label="发布状态" width="120">
      <template #default="{ row }">
          {{ row.release_status === 2 ? '发布' : '下架' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleEdit(row)">编辑</el-button>
          <el-button
            link
            :type="row.release_status ? 'danger' : 'success'"
            @click="handleStatusChange(row)"
          >
              {{ row.release_status === 2 ? '下架' : '发布' }}
          </el-button>
          <!-- <el-button link type="danger" @click="handleDelete(row)">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      v-model:total="total"
      v-model:page="query.page"
      v-model:limit="query.limit"
      @pagination="getList"
    />

    <!-- 编辑弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="材质名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入材质名称" style="width: 50%" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false" :disabled="submitLoading">取 消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Pagination from '@/components/Pagination/index.vue'
import request from '@/utils/request'

// 状态变量
const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('新增')
const formRef = ref(null)
const queryRef = ref(null)
const tableData = ref([])
const total = ref(0)

// 查询参数
const query = ref({
  page: 1,
  limit: 10,
  update_time: '',
  online_time: '',
  date_value: ['', '']
})

// 表单数据
const initForm = {
  id: null,
  name: '',
  update_time: '',
  online_time: '',
  release_status: 1
}

const form = ref({ ...initForm })

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入材质名称', trigger: 'blur' }],
  update_time: [{ required: true, message: '请选择更新时间', trigger: 'change' }],
  online_time: [{ required: true, message: '请选择上线时间', trigger: 'change' }]
}

// API调用
const api = {
  list: (params) => request.get('/admin/furnish.DecorationMaterial/list', { params }),
  save: (data) => request.post('/admin/furnish.DecorationMaterial/create', data),
  update: (data) => request.post('/admin/furnish.DecorationMaterial/update', data),
  delete: (id) => request.get(`/admin/furnish.DecorationMaterial/delete?id=${id}`),
  changeStatus: (id, release_status) =>
    request.get(`/admin/furnish.DecorationMaterial/changeStatus`, { params: { id, release_status } })
}

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const res = await api.list(query.value)
    if (res.code === 200) {
      tableData.value = res.data.list
      total.value = res.data.total
    }
  } catch (error) {
    console.error('获取列表失败:', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}
const stateSelectList = [
  { value: 0, label: '全部' },
  { value: '2', label: '发布' },
  { value: '1', label: '下架' }
]
// 查询处理
const handleQuery = () => {
  query.value.page = 1
  getList()
}

// 重置查询
const resetQuery = () => {
  queryRef.value?.resetFields()
  query.value = {
    page: 1,
    limit: 10,
    update_time: '',
    online_time: ''
  }
  getList()
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增'
  form.value = { ...initForm }
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  dialogTitle.value = '编辑'
  form.value = { ...row }
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除该记录吗？`, '提示', {
      type: 'warning'
    })
    const res = await api.delete(row.id)
    if (res.code === 200) {
      ElMessage.success('删除成功')
      getList()
    }
  } catch (error) {
    console.error('删除失败:', error)
  }
}

// 状态变更
const handleStatusChange = async (row) => {
  try {
   const newStatus = row.release_status == 2 ? 1 : 2
    const res = await api.changeStatus(row.id, newStatus)
    if (res.code === 200) {
      ElMessage.success(`${newStatus ? '上线' : '下线'}成功`)
      getList()
    }
  } catch (error) {
    console.error('状态更改失败:', error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    submitLoading.value = true
    await formRef.value.validate()
    let res;
    if (form.value.id) {
      res = await api.update(form.value);
    } else {
      res = await api.save(form.value);
    }
    if (res.code === 200) {
      ElMessage.success(form.value.id ? '编辑成功' : '新增成功')
      dialogVisible.value = false
      getList()
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitLoading.value = false
  }
}

// 初始化
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.dialog-footer {
  padding: 20px 0 0;
  text-align: right;
}
</style>