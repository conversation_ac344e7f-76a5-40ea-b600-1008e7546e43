<template>
  <!-- 最外层div 100%宽高 内部氛围左右两部分，左侧是一个iframe高度是100%，右侧是一个div -->
  <div class="app-container">
    <div class="app-left">
      <!-- Loading 遮罩层 -->
      <div v-if="iframeLoading" class="iframe-loading">
        <div class="loading-content">
          <el-icon class="loading-icon">
            <Loading />
          </el-icon>
          <div class="loading-text">正在加载聊天界面...</div>
        </div>
      </div>
      <iframe
        src="http://8.141.29.113/chat/4LJ8VjKAzGm83C5u"
        frameborder="0"
        width="100%"
        height="100%"
        @load="onIframeLoad"
        @error="onIframeError"
      ></iframe>
    </div>
    <div class="app-right">
      <!-- 一个表单 提交六个字段 name字段  question字段  answer字段 fraction字段（星星评分组件100分满分） reason字段（文本域） -->
      <el-form :model="form" :rules="rules" ref="formRef" label-width="140px">
        <el-form-item>
          <div class="font-bold text-[16px]">
            为了帮助芝麻选房 ai
            快速迭代体验，请抽出宝贵的时间，帮我们反馈一下您的使用体验。目前的体验待完善，需要您复制一下左侧的问题和答案，我们才能定位到您反馈的问题
          </div>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="问题" prop="question">
          <el-input v-model="form.question" placeholder="请复制左侧的问题 "></el-input>
        </el-form-item>
        <el-form-item label="回答" prop="answer">
          <el-input
            type="textarea"
            v-model="form.answer"
            placeholder="请复制左侧的答案"
            :rows="10"
          ></el-input>
        </el-form-item>
        <el-form-item label="体验问题类型" prop="tag">
          <el-checkbox-group v-model="form.tag" size="small">
            <div class="flex flex-wrap gap-[10px]">
              <el-checkbox label="内容或数据错误" value="1" border />
              <el-checkbox label="时效不及时" value="2" border />
              <el-checkbox label="回答不完整" value="3" border />
              <el-checkbox label="答非所问" value="4" border />
              <el-checkbox label="没记住我的信息" value="5" border />
              <el-checkbox label="回答格式混乱" value="6" border />
              <el-checkbox label="回答内容太长" value="7" border />
              <el-checkbox label="回答速度太慢" value="8" border />
              <el-checkbox label="推销倾向" value="9" border />
              <el-checkbox label="其他" value="10" border />
            </div>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="体验评分" prop="fraction">
          <el-rate v-model="form.fraction" :max="10" allow-half></el-rate>
          <div>{{ form.fraction * 10 }}分</div>
        </el-form-item>

        <el-form-item label="评分理由" prop="reason">
          <el-input v-model="form.reason" placeholder="请输入评分理由"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="submitForm">提交</el-button>
          <!--重置 -->
          <el-button type="default" @click="resetFormExceptName">重置</el-button>
        </el-form-item>
        <el-form-item>
          <!-- 默认展示下划线 -->
          <!-- <el-link
            type="success"
            underline
            size="large"
            target="_blank"
            href="https://icnh6uysqslc.feishu.cn/wiki/NKx6wGxSnidPtLk0baYctUI3nQf?table=tblKctuXwlpLyhV4&view=vewNkX9Vjk"
          >
            非常感谢您成为我们的体验官！您提交的问题，我们正在快马加鞭分析和解决，点击查看进度：体验问收集
          </el-link> -->
          <div class="flex items-center">
            非常感谢您成为我们的体验官！您提交的问题，我们正在快马加鞭分析和解决，点击查看进度：
            <el-link
              type="success"
              underline
              size="large"
              target="_blank"
              href="https://icnh6uysqslc.feishu.cn/wiki/NKx6wGxSnidPtLk0baYctUI3nQf?table=tblKctuXwlpLyhV4&view=vewNkX9Vjk"
            >
              体验问题收集
            </el-link>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, onMounted } from 'vue'
import { Loading } from '@element-plus/icons-vue'
import { aiFeedback } from '@/api/system/user'

// 缓存key
const CACHE_NAME_KEY = 'ai_feedback_user_name'

// iframe loading状态
const iframeLoading = ref(true)

const form = reactive({
  name: '',
  question: '',
  answer: '',
  fraction: 0,
  reason: '',
  tag: []
})

const loading = ref(false)

const formRef = ref(null)

const rules = reactive({
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  question: [{ required: true, message: '请输入问题', trigger: 'blur' }],
  answer: [{ required: true, message: '请输入回答', trigger: 'blur' }],
  fraction: [{ required: true, message: '请输入评分', trigger: 'blur' }],
  reason: [{ required: true, message: '请输入原因', trigger: 'blur' }]
})

// 从localStorage获取缓存的姓名
const getCachedName = () => {
  try {
    return localStorage.getItem(CACHE_NAME_KEY) || ''
  } catch (error) {
    console.warn('获取缓存姓名失败:', error)
    return ''
  }
}

// 缓存姓名到localStorage
const cacheName = (name) => {
  try {
    if (name && name.trim()) {
      localStorage.setItem(CACHE_NAME_KEY, name.trim())
    }
  } catch (error) {
    console.warn('缓存姓名失败:', error)
  }
}

// 清空表单（除了姓名字段）
const resetFormExceptName = () => {
  const cachedName = form.name // 保存当前姓名
  Object.assign(form, {
    name: cachedName, // 保留姓名
    question: '',
    answer: '',
    fraction: 0,
    reason: '',
    tag: []
  })
  // 清除表单验证状态
  formRef.value?.clearValidate()
}

const submitForm = async () => {
  const valid = await formRef.value.validate()
  if (!valid) {
    return
  }

  loading.value = true

  try {
    const params = { ...form, tag: form.tag.join(','), fraction: form.fraction * 10 }

    // 缓存姓名
    cacheName(form.name)

    console.log(form)

    // 这里应该是实际的API调用
    await aiFeedback(params)

    // 提交成功后清空表单（除了姓名）
    resetFormExceptName()

    // 可以添加成功提示
    ElMessage.success('提交成功')
  } catch (error) {
    console.error('提交失败:', error)
    // ElMessage.error('提交失败，请重试')
  } finally {
    loading.value = false
  }
}

// 组件挂载时恢复缓存的姓名
onMounted(() => {
  const cachedName = getCachedName()
  if (cachedName) {
    form.name = cachedName
  }
})

// iframe加载完成事件
const onIframeLoad = () => {
  iframeLoading.value = false
}

// iframe加载错误事件
const onIframeError = () => {
  iframeLoading.value = false
  console.error('iframe加载失败')
}
</script>

<style lang="scss" scoped>
.app-container {
  min-width: 1000px;
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  .app-left {
    width: 60%;
    height: 100%;
    position: relative; // 为loading遮罩层提供定位基准

    .iframe-loading {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 10;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(255, 255, 255, 0.9);

      .loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;

        .loading-icon {
          font-size: 32px;
          color: #409eff;
          animation: rotate 2s linear infinite;
        }

        .loading-text {
          font-size: 14px;
          color: #666;
        }
      }
    }

    iframe {
      display: block;
    }
  }
  .app-right {
    padding-top: 100px;
    width: 40%;
    height: 100%;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
