<template>
      <div class="app-container">

        <el-form  ref="queryRef" :model="query" :inline="true" label-width="90px">
        <el-form-item label="项目名称：" prop="name">
          <el-input v-model="query.name" maxlength="20" placeholder="请输入姓名" style="width: 140px" />
        </el-form-item>

        <el-form-item label="联系方式：" prop="phone">
          <el-input v-model="query.phone" maxlength="20" placeholder="请输入手机号" style="width: 140px" />
        </el-form-item>
       
        <!-- <el-form-item label="可以回访：" prop="accept_survey">
          <el-select v-model="query.accept_survey" style="width: 120px">
            <el-option value="-1" label="不限" />
            <el-option value="1" label="是" />
            <el-option value="0" label="否" />
          </el-select>
        </el-form-item> -->

        <el-form-item label="回访状态：" prop="status">
          <el-select v-model="query.status" style="width: 120px">
            <el-option value="-1" label="不限" />
            <el-option value="1" label="已回访" />
            <el-option value="0" label="未回访" />
          </el-select>
        </el-form-item>
        
        <!-- 时间 -->
        <el-form-item>
        <el-select v-model="query.date_field" class="ya-date-field" placeholder="时间类型">
          <el-option value="" label="请选择" />
          <el-option value="create_time" label="添加时间" />
          <el-option value="update_time" label="修改时间" />
        </el-select>
        <el-date-picker
          v-model="query.date_value"
          type="datetimerange"
          class="ya-date-value"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="[new Date(2024, 1, 1, 0, 0, 0), new Date(2024, 1, 1, 23, 59, 59)]"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" title="搜索" @click="handleQuery">搜索</el-button>
        <el-button  @click="resetQuery">重置</el-button>
      </el-form-item>
       
        
      </el-form>
      <!-- 搜索end -->
    <el-table
    ref="table"
    v-loading="loading"
    :data="data"
    :height="height"
    @sort-change="sort"
    @selection-change="select"
  >
    <el-table-column type="selection" width="42" title="全选/反选" />
    <el-table-column :prop="idkey" label="ID" width="80" sortable="custom" />
    <el-table-column prop="name" label="项目名称" min-width="100" />
    <el-table-column prop="phone" label="联系方式" min-width="150" show-overflow-tooltip />
    <!-- <el-table-column prop="accept_survey" label="是否愿意接听调研电话" min-width="250" show-overflow-tooltip >
      <template #default="scope">
          <el-switch
            v-model="scope.row.accept_survey"
            :active-value="1"
            :inactive-value="0"
            disabled
          />
        </template>
    </el-table-column> -->
    <el-table-column prop="status" label="回访状态" min-width="110" show-overflow-tooltip >
      <template #default="scope">
        <el-switch
                v-model="scope.row.status"
                :active-value="1"
                :inactive-value="0"
                disabled
              />
      </template>
    </el-table-column>
    <el-table-column prop="content" label="意见内容" min-width="130" />
    <el-table-column prop="remark" label="备注" min-width="160" show-overflow-tooltip />
    
    <el-table-column prop="create_time" label="请求时间" min-width="165" sortable="custom" />
    <el-table-column prop="update_time" label="更新时间" min-width="165" sortable="custom" />
    <el-table-column label="操作" width="95">
        <template #default="scope">
        <el-link type="primary" class="mr-1" :underline="false" @click="infoSuggests(scope.row)">
          编辑
        </el-link>
        
      </template>
    </el-table-column>
  </el-table>
  <!-- 分页 -->
  <pagination
    v-show="count > 0"
    v-model:total="count"
    v-model:page="query.page"
    v-model:limit="query.limit"
    @pagination="suggestsList"
  />
  
<!-- 添加修改 -->
<el-dialog
      v-model="dialog"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="cancel"
      top="5vh"
    >
      <el-form ref="ref" :rules="rules" :model="model" label-width="100px">
        <el-tabs>
          <el-tab-pane label="信息">
            <el-scrollbar native :height="height - 80">
              
              <el-form-item label="项目名称" prop="name">
                <el-input v-model="model.name" placeholder="请输入名称" disabled />
              </el-form-item>
              <el-form-item label="联系方式" prop="phone">
                <el-input v-model="model.phone" placeholder="请输入手机号" disabled />
              </el-form-item>
              <el-form-item label="内容" prop="content">
                <el-input
                    v-model="model.content"
                    type="textarea"
                    autosize
                    placeholder="content"
                    disabled
                  />
              </el-form-item>
              <!-- <el-form-item  label="是否允许回访">
                <el-switch v-model="model.accept_survey" :active-value="1" :inactive-value="0" disabled/>
                <el-text size="default"> </el-text>
              </el-form-item> -->

              <el-form-item  label="回访状态">
                <el-switch v-model="model.status" :active-value="1" :inactive-value="0" />
                <el-text size="default"> </el-text>
              </el-form-item>
              
              <el-form-item label="备注" prop="remark">
                <el-input v-model="model.remark" placeholder="备注" clearable />
              </el-form-item>
              <!-- <el-form-item label="排序" prop="sort">
                <el-input v-model="model.sort" type="number" placeholder="sort" clearable />
              </el-form-item> -->
              
              <el-form-item v-if="model[idkey]" label="添加时间" prop="create_time">
                <el-input v-model="model.create_time" disabled />
              </el-form-item>
              <el-form-item v-if="model[idkey]" label="修改时间" prop="update_time">
                <el-input v-model="model.update_time" disabled />
              </el-form-item>
              <el-form-item v-if="model.delete_time" label="删除时间" prop="delete_time">
                <el-input v-model="model.delete_time" disabled />
              </el-form-item>
            </el-scrollbar>
          </el-tab-pane>
         
        </el-tabs>
      </el-form>
      <template #footer>
        <el-button :loading="loading" @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="editSuggests">提交</el-button>
      </template>
    </el-dialog>
</div>
</template>
<script setup>
import {ref, onMounted} from "vue"
import checkPermission from '@/utils/permission';
import screenHeight from '@/utils/screen-height'
import Pagination from '@/components/Pagination/index.vue'
import { getPageLimit } from '@/utils/settings'
import * as Project from '@/api/suggests/suggests'
import { useFormDisabled } from "element-plus";
const model = ref({});
const rowInfo = ref({});

const loading = ref(true);
const data = ref([])
const height = ref(680)
const query = ref({
        page: 1,
        limit: getPageLimit()
      });
const count = ref(0);
const idkey = ref('id');

const dialog = ref(false);
const dialogTitle = ref('');
function suggestsList(){
   
    loading.value = true;
    Project.suggestsList(query.value).then((res) => {
       
          data.value = res.data.list
          count.value = res.data.count
          loading.value = false
        })
        .catch(error => {
          console.log(error);
          loading.value = false
        })

}
function infoSuggests(row) {

  rowInfo.value = row
  loading.value = true;
  dialog.value = true
  dialogTitle.value = row.name + '意见详情：' + row[idkey.value]
  var id = {}
  id[idkey.value] = row[idkey.value]
  Project.info(id)
    .then((res) => {
      reset(res.data)
    })
    .catch(() => {})
  loading.value = false

}
function editSuggests() {
  loading.value = true;
  // 验证
  let dataEdit = {}
  // 备注
  if (rowInfo.value.remark != model.value.remark) {
    dataEdit['remark'] = model.value.remark
  }
  // 排序
  if (rowInfo.value.sort != model.value.sort) {
    dataEdit['sort'] = model.value.sort
  }
  // 是否已经回访
  if (rowInfo.value.status != model.value.status) {
    dataEdit['status'] = model.value.status
  }
  if (Object.keys(dataEdit).length === 0) {
    ElMessage.success("修改成功")
    dialog.value = false
  } else {
    dataEdit[idkey.value] = model.value[idkey.value]
    
    Project.editSuggest(dataEdit).then((res) => {
      suggestsList()
      dialog.value = false
    })
    .catch(() => {
      loading.value = false
    })
  }
  
  loading.value = false
}
// 排序
function sort(sort) {
      query.sort_field = sort.prop
      query.sort_value = ''
      if (sort.order === 'ascending') {
        query.sort_value = 'asc'
        suggestsList()
      }
      if (sort.order === 'descending') {
        this.query.sort_value = 'desc'
        suggestsList()
      }
}
// 操作
function select(selection) {
      this.selection = selection
      this.selectIds = this.selectGetIds(selection).toString()
    }

function cancel() {
  dialog.value = false
  reset()    
}
//搜索
const queryRef = ref(null);
function resetQuery(){
  query.value.accept_survey = '-1';
  query.value.status = '-1';
  query.value.status = '-1';
  query.value.date_field = '';
  query.value.date_value[0] = '';
  query.value.date_value[1] = '';
  queryRef.value.resetFields();
  handleQuery();

}
function handleQuery() {
  query.value.pageNum = 1
  suggestsList()
}
//搜索end
// 重置
function reset(row) {
  if (row) {
    model.value = row
  } else {
    model.value = {}
  }
}
onMounted(()=>{
    height.value = screenHeight(310)
    suggestsList()
})

</script>