<template>
	<div class="app-container">
		<h1>经纬度转换工具</h1>

		<el-row :gutter="20">
			<!-- 左侧：经纬度对调 + 批量数据处理 -->
			<el-col :span="12">
				<!-- 经纬度对调 -->
				<el-card style="height: 100%">
					<el-card shadow="never" class="box-card">
						<template #header>
							<span>经纬度对调</span>
						</template>
						<el-input type="textarea" :rows="4" placeholder="输入经纬度信息，多个点使用分号;分隔" v-model="latLngInput">
						</el-input>
						<el-button type="primary" @click="handleLatLngConvert"
							style="margin-top: 10px">经纬度对调</el-button>
						<div v-if="latLngResult" class="result">
							<strong>结果：</strong>
							<el-input type="textarea" :rows="4" v-model="latLngResult" readonly> </el-input>
						</div>
					</el-card>

					<!-- 批量数据处理 -->
					<el-card shadow="never" class="box-card" style="margin-top: 20px">
						<template #header>
							<span>批量数据处理</span>
						</template>
						<el-upload class="upload-demo" accept=".xlsx, .csv" :before-upload="handleBeforeUpload"
							:limit="1">
							<el-button type="primary">上传文件</el-button>
							<template #tip>
								<span class="el-upload__tip">只能上传xlsx文件</span>
								<el-link class="ml-[10px]" type="primary" @click="handleExport">导入模版下载
								</el-link>
							</template>
						</el-upload>
						<div v-if="fileData && !resultFile">
							<el-link class="ml-[10px]" type="primary">{{ fileData.name }}</el-link>
							<el-button class="ml-[10px]" type="primary" @click="handleBatchProcess">开始处理</el-button>
						</div>
						<div v-if="percentage !== 0 && percentage <= 100 && !resultFile" class="mt-[10px]">
							<el-progress text-inside :duration="5" :percentage="percentage" :stroke-width="15"
								status="success" :striped="percentage !== 100" striped-flow />
						</div>

						<div class="mt-[10px]" v-if="resultFile">
							<el-link class="ml-[10px]" type="primary"
								@click="handleExportResultFile">转换结果.xlsx</el-link>
							<el-button type="primary" class="ml-[10px]" @click="handleBatchReset">重新开始</el-button>
						</div>
					</el-card>
				</el-card>
			</el-col>

			<!-- 右侧：坐标系转换 + 转换为腾讯地图坐标批量处理 -->
			<el-col :span="12">
				<!-- 坐标系转换 -->
				<el-card>
					<el-card shadow="never" class="box-card">
						<template #header>
							<span>坐标系转换</span>
						</template>
						<el-input type="textarea" :rows="4" placeholder="输入经纬度信息，多个点使用分号;分隔" v-model="coordInput">
						</el-input>
						<div class="w-[200px]">
							<el-select v-model="coordSystem" placeholder="选择原始数据的坐标系">
								<el-option label="百度" value="baidu"></el-option>
							</el-select>
						</div>

						<el-button type="primary" @click="handleCoordConvert">转换为腾讯地图坐标</el-button>
						<div v-if="coordResult" class="result">
							<strong>结果：</strong>
							<el-input type="textarea" :rows="4" v-model="coordResult" readonly> </el-input>
						</div>
					</el-card>

					<!-- 转换为腾讯地图坐标批量处理 -->
					<el-card shadow="never" class="box-card" style="margin-top: 20px">
						<template #header>
							<span>转换为腾讯地图坐标批量处理</span>
						</template>
						<el-upload class="upload-demo" accept=".xlsx, .csv" :before-upload="handleCoordBeforeUpload"
							:limit="1">
							<el-button type="primary">上传文件</el-button>
							<template #tip>
								<span class="el-upload__tip">只能上传xlsx文件</span>
								<el-link class="ml-[10px]" type="primary" @click="handleExport">导入模版下载</el-link>
							</template>
						</el-upload>
						<div class="w-[200px]">
							<el-select v-model="tencentCoordSystem" placeholder="选择原始数据的坐标系">
								<el-option label="百度" value="baidu"></el-option>
							</el-select>
						</div>
						<div v-if="tencentFile && !tencentResultFile">
							<el-link class="ml-[10px]" type="primary">{{ tencentFile.name }}</el-link>
							<el-button class="ml-[10px]" type="primary"
								@click="handleTencentBatchProcess">开始处理</el-button>
						</div>
						<div v-if="percentage2 !== 0 && percentage2 <= 100 && !tencentResultFile" class="mt-[10px]">
							<el-progress text-inside :duration="5" :percentage="percentage2" :stroke-width="15"
								status="success" :striped="percentage2 !== 100" striped-flow />
						</div>
						<div class="mt-[10px]" v-if="tencentResultFile">
							<el-link class="ml-[10px]" type="primary"
								@click="handleExportResultFile2">转换结果.xlsx</el-link>
							<el-button type="primary" class="ml-[10px]"
								@click="handleTencentBatchReset">重新开始</el-button>
						</div>

						<!-- <div>
              <el-button type="primary" @click="handleTencentBatchProcess">开始处理</el-button>
              <el-button @click="handleTencentBatchReset">重新开始</el-button>
            </div> -->
					</el-card>
				</el-card>
			</el-col>
		</el-row>
	</div>
</template>

<script>
	import * as XLSX from 'xlsx'
	import {
		excelExport
	} from '@/components/ExcelExport/index'
	import {
		convertCoordinates
	} from '@/api/info/coordinate'
	export default {
		name: 'CoordinateConverter',
		data() {
			return {
				latLngInput: '',
				latLngResult: '',
				percentage: 0,
				fileData: null,
				resultFile: null,

				coordInput: '',
				coordResult: '',
				coordSystem: '',

				tencentCoordSystem: '',
				tencentFile: null,
				tencentResultFile: null,
				percentage2: 0
			}
		},
		methods: {
			// 经纬度对调
			handleLatLngConvert() {
				if (!this.latLngInput || !this.latLngInput.trim()) {
					ElMessage.warning('请输入经纬度')
					return
				}

				this.latLngResult = this.latLngInput
					.split(';')
					.map((i) => i.split(',').reverse())
					.join(';')
			},
			// 模版导出
			handleExport() {
				const fileName = '经纬度数据导入模版下载'
				const header = [{
					title: '标题'
				}, {
					latLng: '经纬度数据'
				}]
				const data = [{
						title: '密云其它',
						latLng: '40.235355,116.918893;40.23402526261032,116.92736177093406'
					},
					{
						title: '金宝街',
						latLng: '40.235355,116.918893;40.23402526261032,116.92736177093406'
					},
					{
						title: '阜成门',
						latLng: '40.235355,116.918893;40.23402526261032,116.92736177093406'
					}
				]
				excelExport(data, header, fileName, 'xlsx', true)
			},
			// 经纬度上传
			handleBeforeUpload(file) {
				this.fileData = file
				return false
			},

			handleBatchProcess() {
				this.readerData(this.fileData).then((res) => {
					const {
						header,
						results
					} = res
					this.setExcelData(header, results)
				})
				let interval = setInterval(() => {
					if (this.percentage >= 100) {
						clearInterval(interval)
					} else {
						const num = this.percentage + Math.floor(Math.random() * 10) + 1
						this.percentage = num >= 100 ? 100 : num // 每次增加1到10的随机数
					}
				}, 200) // 每500毫秒更新一次进度
			},
			setExcelData(header, results) {
				console.log(results)
				const eHeader = header.map((i) => {
					const obj = {}
					obj[i] = i
					return obj
				})
				results.forEach((item) => {
					const key = '经纬度数据'
					item[key] = item[key]
						.split(';')
						.map((i) => i.split(',').reverse())
						.join(';')
				})

				console.log(eHeader, results)

				const {
					workbook,
					worksheet
				} = excelExport(results, eHeader, '转换结果', 'xlsx', true, false)

				setTimeout(() => {
					this.percentage = 50
				}, 400)

				setTimeout(() => {
					this.percentage = 90
				}, 800)
				setTimeout(() => {
					this.percentage = 100
					this.resultFile = {
						workbook,
						worksheet
					}
					ElMessage.success('处理完成')
				}, 1000)
			},
			handleExportResultFile() {
				const {
					workbook,
					worksheet
				} = this.resultFile
				XLSX.utils.book_append_sheet(workbook, worksheet, '转换结果')
				XLSX.writeFile(workbook, '转换结果' + '.' + 'xlsx')
			},
			handleExportResultFile2() {
				const {
					workbook,
					worksheet
				} = this.tencentResultFile
				XLSX.utils.book_append_sheet(workbook, worksheet, '转换结果')
				XLSX.writeFile(workbook, '转换结果' + '.' + 'xlsx')
			},
			getHeaderRow(sheet) {
				const headers = []
				const range = XLSX.utils.decode_range(sheet['!ref'])
				let C
				const R = range.s.r
				for (C = range.s.c; C <= range.e.c; ++C) {
					const cell = sheet[XLSX.utils.encode_cell({
						c: C,
						r: R
					})]
					let hdr = 'UNKNOWN ' + C
					if (cell && cell.t) hdr = XLSX.utils.format_cell(cell)
					headers.push(hdr)
				}
				return headers
			},
			readerData(file) {
				return new Promise((reslove, reject) => {
					const reader = new FileReader()
					reader.onload = (e) => {
						const data = e.target.result
						const workbook = XLSX.read(data, {
							type: 'array',
							cellDates: true
						})
						const firstSheetName = workbook.SheetNames[0]
						const worksheet = workbook.Sheets[firstSheetName]
						const header = this.getHeaderRow(worksheet)
						const results = XLSX.utils.sheet_to_json(worksheet, {
							defval: ''
						})
						reslove({
							header,
							results
						})
					}
					reader.readAsArrayBuffer(file)
				})
			},

			handleCoordConvert() {
				if (!this.coordInput) {
					ElMessage.warning('请输入经纬度信息')
					return
				}
				if (!this.coordSystem) {
					ElMessage.warning('请选择原始数据的坐标系')
					return
				}
				convertCoordinates({
					source: this.coordSystem,
					coordinate: this.coordInput
				}).then((res) => {
					this.coordResult = res.data[0]
				})
			},

			handleBatchReset() {
				this.percentage = 0
				this.resultFile = null
				this.fileData = null
			},
			// 坐标体系
			handleCoordBeforeUpload(file) {
				this.tencentFile = file
				return false
			},

			setTencentExcelData(header, results) {
				console.log(results)
				const eHeader = header.map((i) => {
					const obj = {}
					obj[i] = i
					return obj
				})

				const {
					workbook,
					worksheet
				} = excelExport(
					results,
					eHeader,
					'腾讯坐标系转换结果',
					'xlsx',
					true,
					false
				)

				setTimeout(() => {
					this.percentage2 = 50
				}, 400)

				setTimeout(() => {
					this.percentage2 = 90
				}, 800)
				setTimeout(() => {
					this.percentage2 = 100
					this.tencentResultFile = {
						workbook,
						worksheet
					}
					ElMessage.success('处理完成')
				}, 1000)
			},
			setTransformTencentData(header, results) {
				const data = results.map((i) => {
					const obj = {
						标题: i['标题'],
						经纬度数据: i['经纬度数据']
					}
					return obj
				})
				const params = {
					is_file: 1,
					source: this.tencentCoordSystem,
					coordinate: data
				}
				convertCoordinates(params).then((res) => {
					console.log(res)
					this.setTencentExcelData(['标题', '经纬度数据'], res.data)
					// this.coordResult = res.data.map((i) => i.join(',')).join(';')
				})
				let interval = setInterval(() => {
					if (this.percentage2 >= 100) {
						clearInterval(interval)
					} else {
						const num = this.percentage2 + Math.floor(Math.random() * 10) + 1
						this.percentage2 = num >= 100 ? 100 : num // 每次增加1到10的随机数
					}
				}, 200) // 每500毫秒更新一次进度
			},
			handleTencentBatchProcess() {
				if (!this.tencentCoordSystem) {
					ElMessage.warning('请选择原始数据的坐标系')
					return
				}
				this.readerData(this.tencentFile)
					.then((res) => {
						const {
							header,
							results
						} = res
						this.setTransformTencentData(header, results)
					})
					.catch(() => {})
			},
			handleTencentBatchReset() {
				this.tencentFile = null
				this.percentage2 = 0
				this.tencentCoordSystem = ''
				this.tencentResultFile = null
			}
		}
	}
</script>

<style scoped>
	.coordinate-converter {
		max-width: 1200px;
		margin: 0 auto;
		padding: 20px;
	}

	.box-card {
		margin-bottom: 20px;
	}

	.el-upload {
		margin-bottom: 20px;
	}

	.result {
		margin-top: 15px;
	}

	h1 {
		text-align: center;
		margin-bottom: 20px;
	}

	.el-button {
		margin-right: 10px;
	}

	.el-textarea {
		margin-bottom: 10px;
	}

	.el-card__header {
		padding: 10px 20px;
	}

	.el-select {
		margin-bottom: 10px;
	}
</style>