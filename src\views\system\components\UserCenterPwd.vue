<template>
  <el-scrollbar native :height="height">
    <el-row>
      <el-col :span="12">
        <el-form ref="ref" :rules="rules" :model="model" label-width="120px">
          <el-form-item label="旧密码" prop="password_old">
            <el-input
              v-model="model.password_old"
              type="password"
              placeholder="请输入旧密码"
              autocomplete="off"
              clearable
              show-password
            />
          </el-form-item>
          <el-form-item label="新密码" prop="password_new">
            <el-input
              v-model="model.password_new"
              type="password"
              placeholder="请输入新密码,密码长度为8-18个字符,必须包含数字、字母和特殊字符"
              autocomplete="off"
              clearable
              show-password
            >
            </el-input>
          </el-form-item>
          <el-form-item label="确认新密码" prop="password_confirm">
            <el-input
              v-model="model.password_confirm"
              type="password"
              placeholder="请再次输入新密码,密码长度为8-18个字符,必须包含数字、字母和特殊字符"
              autocomplete="off"
              clearable
              show-password
            >
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button :loading="loading" @click="reset">重置</el-button>
            <el-button :loading="loading" type="primary" @click="submit">提交</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
  </el-scrollbar>
</template>

<script>
import screenHeight from '@/utils/screen-height'
import { pwd } from '@/api/system/user-center'
import { useUserStore } from '@/store/modules/user'
import { useTagsViewStore } from '@/store/modules/tagsView'

const userStore = useUserStore()
const tagsViewStore = useTagsViewStore()

export default {
  name: 'SystemUserCenterPwd',
  data() {
    var validatePasswordConfirm = (rule, value, callback) => {
      if (value !== this.model.password_new) {
        callback(new Error('两次输入的新密码不一致'))
      } else if(value == this.model.password_old){
        callback(new Error('新密码和旧密码不能相同'))
      } else {
        callback()
      }
    }
    return {
      name: '修改密码',
      height: 680,
      loading: false,
      model: {
        password_old: '',
        password_new: '',
        password_confirm: ''
      },
      rules: {
        password_old: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
        password_new: [{ required: true, message: '请输入新密码', trigger: 'blur' }],
        password_confirm: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: validatePasswordConfirm, trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.height = screenHeight(210)
  },
  methods: {
    // 重置
    reset() {
      this.$refs['ref'].resetFields()
      this.$refs['ref'].clearValidate()
    },
    // 提交
    submit() {
      this.$refs['ref'].validate((valid) => {
        if (valid) {
          this.loading = true
          pwd(this.model)
            .then((res) => {
              this.loading = false
              ElMessage.success(res.msg)
              this.logout()
              // setTimeout(() => {
              //   // 延迟1秒后执行的代码，基于someData的变化
              //   this.logout()
              // }, 1000);
              
            })//
            .catch(() => {
              this.loading = false
            })
        }
      })
    },
    // 退出系统
    logout() {
      if (userStore.user.forcechangepwd == 1) {
        userStore
        .logout()
        .then(() => {
          tagsViewStore.delAllViews()
        })
        .then(() => {
          this.$router.push(`/login?redirect=/dashboard`)
        })
      }
    }
  }
}
</script>
