import request from '@/utils/request'
// 会员身份管理
const url = '/admin/member.PropertyConsultant/'
/**
 * 获取可能存在的身份身份获取
 * @param {array} params 请求参数
 */
export function getIsExistIdentity(data) {
  return request({
    url: url + 'getIsExistIdentity',
    method: 'post',
    data
  })
}

/**
 * 编辑选房师/置业顾问数据
 * @param {array} params 请求参数
 */
export function memberIdentityEidt(data) {
  return request({
    url: url + 'memberIdentityEidt',
    method: 'post',
    data
  })
}

/**
 * 置业顾问审核
 * @param {array} params 请求参数
 */
export function identityreview(data) {
  return request({
    url: url + 'review',
    method: 'post',
    data
  })
}

/**
 * 取消置业顾问身份
 * @param {array} params 请求参数
 */
export function identitycancel(data) {
  return request({
    url: url + 'cancel',
    method: 'post',
    data
  })
}

/**
 * 置业顾问身份显示隐藏
 * @param {array} params 请求参数
 */
export function zhiyeguwenisshow(data) {
  return request({
    url: url + 'zhiyeguwenisshow',
    method: 'post',
    data
  })
}

/**
 * 置业��问身份列表
 * @param {array} params 请求参数
 */
export function getIdentityList(data) {
  return request({
    url: url + 'getIdentityList',
    method: 'post',
    data
  })
}

export function isCooperateIdentity(data) {
  return request({
    url: url + 'isCooperateIdentity',
    method: 'post',
    data
  })
}
